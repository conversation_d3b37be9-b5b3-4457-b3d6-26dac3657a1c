-- 为 sys_user 表添加缺失字段的 SQL 语句

-- 1. 修改 user_name 字段长度从 varchar(30) 改为 varchar(100)
ALTER TABLE `sys_user` MODIFY COLUMN `user_name` varchar(100) NOT NULL COMMENT '用户账号';

-- 2. 添加 pwd 字段
ALTER TABLE `sys_user` ADD COLUMN `pwd` varchar(100) NOT NULL DEFAULT '' COMMENT '密码' AFTER `password`;

-- 3. 添加 freight_forwarder_id 字段
ALTER TABLE `sys_user` ADD COLUMN `freight_forwarder_id` bigint(20) DEFAULT NULL COMMENT '网络货运人id' AFTER `remark`;

-- 4. 添加 default_project 字段
ALTER TABLE `sys_user` ADD COLUMN `default_project` bigint(20) NOT NULL DEFAULT '1' COMMENT '项目权限类别  0 指定项目 1 所有项目' AFTER `freight_forwarder_id`;

-- 5. 为 sys_role 表添加权限集合字段
ALTER TABLE `sys_role` ADD COLUMN `permissions` text COMMENT '权限集合（JSON格式存储）' AFTER `status`;
