-- ----------------------------
-- 1. 货主信息表
-- ----------------------------
drop table if exists customer_info;
CREATE TABLE `customer_info` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `customer_name` varchar(255) NOT NULL COMMENT '货主名称',
                                 `social_credit_code` varchar(50) NOT NULL COMMENT '统一社会信用代码',
                                 `contact_person` varchar(50) NOT NULL COMMENT '企业联系人',
                                 `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
                                 `transport_routes` varchar(500) DEFAULT NULL COMMENT '运输线路',
                                 `dispatcher` bigint(20) DEFAULT NULL COMMENT '调度员',
                                 `cooperation_status` char(1) DEFAULT '2' COMMENT '合作状态（1-在合作中，2-待合作，3-未开始，4-已结束，5-即将到期）',
                                 `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                 `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb4 COMMENT='货主信息表';
-- ----------------------------
-- 2. 货主承运合同表
-- ----------------------------
CREATE TABLE `customer_contract` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `customer_id` bigint(20) NOT NULL COMMENT '货主ID',
                                     `start_date` date NOT NULL COMMENT '合作开始时间',
                                     `end_date` date NOT NULL COMMENT '合作结束时间',
                                     `contract_status` char(1) DEFAULT '1' COMMENT '合约状态（1-待生效，2-生效中，3-已结束）',
                                     `attachment_path` varchar(255) DEFAULT NULL COMMENT '合同附件路径',
                                     `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                     `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COMMENT='货主承运合同表';


CREATE TABLE `daily_cargo` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                               `customer_id` bigint(20) NOT NULL COMMENT '货主关联ID',
                               `task_id` bigint(20) DEFAULT NULL COMMENT '调度任务ID',
                               `frequent_cargo_id` bigint(20) DEFAULT NULL COMMENT '常发货源ID',
                               `cargo_name` varchar(100) NOT NULL COMMENT '货物名称',
                               `cargo_type` varchar(50) NOT NULL COMMENT '货物类型',
                               `packaging_method` varchar(50) NOT NULL COMMENT '包装方式',
                               `cargo_weight` decimal(10,3) DEFAULT NULL COMMENT '货物数量(吨)',
                               `cargo_volume` varchar(10) DEFAULT NULL COMMENT '货物数量(方)',
                               `loading_province_code` varchar(20) DEFAULT NULL COMMENT '装货省编码',
                               `loading_province_name` varchar(50) DEFAULT NULL COMMENT '装货省名称',
                               `loading_city_code` varchar(20) DEFAULT NULL COMMENT '装货市编码',
                               `loading_city_name` varchar(50) DEFAULT NULL COMMENT '装货市名称',
                               `loading_district_code` varchar(20) DEFAULT NULL COMMENT '装货区编码',
                               `loading_district_name` varchar(50) DEFAULT NULL COMMENT '装货区名称',
                               `loading_address` varchar(255) DEFAULT NULL COMMENT '装货详细地址',
                               `loading_longitude` decimal(10,6) DEFAULT NULL COMMENT '装货地址经度',
                               `loading_latitude` decimal(10,6) DEFAULT NULL COMMENT '装货地址纬度',
                               `unloading_province_code` varchar(20) DEFAULT NULL COMMENT '卸货省编码',
                               `unloading_province_name` varchar(50) DEFAULT NULL COMMENT '卸货省名称',
                               `unloading_city_code` varchar(20) DEFAULT NULL COMMENT '卸货市编码',
                               `unloading_city_name` varchar(50) DEFAULT NULL COMMENT '卸货市名称',
                               `unloading_district_code` varchar(20) DEFAULT NULL COMMENT '卸货区编码',
                               `unloading_district_name` varchar(50) DEFAULT NULL COMMENT '卸货区名称',
                               `unloading_address` varchar(255) DEFAULT NULL COMMENT '卸货详细地址',
                               `unloading_longitude` decimal(10,6) DEFAULT NULL COMMENT '卸货地址经度',
                               `unloading_latitude` decimal(10,6) DEFAULT NULL COMMENT '卸货地址纬度',
                               `loading_date` date DEFAULT NULL COMMENT '装货日期',
                               `loading_time_start` varchar(10) DEFAULT NULL COMMENT '最早装货时间',
                               `loading_time_end` varchar(10) DEFAULT NULL COMMENT '最晚装货时间',
                               `vehicle_type` varchar(50) DEFAULT NULL COMMENT '车辆类型',
                               `vehicle_length` double DEFAULT NULL COMMENT '车辆长度，单位米',
                               `vehicle_count` int(11) DEFAULT NULL COMMENT '车辆数量',
                               `special_requirements` varchar(255) DEFAULT NULL COMMENT '特殊要求',
                               `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态(0-正常,1-已删除)',
                               `is_frequent_source` char(1) DEFAULT '0' COMMENT '是否常发货源(0-否,1-是)',
                               `ai_schedule_status` char(1) DEFAULT '0' COMMENT 'AI调度状态0=未开始,1=调度中,2=调度结束,3=匹配完成',
                               `transport_capacity` char(1) DEFAULT '0' COMMENT '运力配置 0=无，1=部分匹配，2=匹配完成',
                               `online_time` datetime DEFAULT NULL COMMENT '上架时间',
                               `offline_time` datetime DEFAULT NULL COMMENT '下架时间',
                               `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                               `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                               `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                               PRIMARY KEY (`id`),
                               KEY `idx_carrier_id` (`customer_id`),
                               KEY `idx_loading_date` (`loading_date`),
                               KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='每日货源表';
-- ----------------------------
-- 3. 承运司机表
-- ----------------------------
drop table if exists customer_driver;
CREATE TABLE `customer_driver` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `cargo_id` bigint(20) NOT NULL COMMENT '货源ID',
                                   `task_id` bigint(20) NOT NULL COMMENT '调度任务ID',
                                   `task_driver_match_id` bigint(20) DEFAULT NULL COMMENT '调度匹配司机ID',
                                   `driver_name` varchar(50) NOT NULL COMMENT '司机姓名',
                                   `id_card` varchar(18) NOT NULL COMMENT '身份证号',
                                   `phone_number` varchar(20) NOT NULL COMMENT '手机号',
                                   `plate_number` varchar(20) DEFAULT NULL COMMENT '车牌号',
                                   `plate_color` char(2) DEFAULT NULL COMMENT '车牌颜色(1-蓝色,2-黄色,3-黑色,4-白色,5-绿色,91-其他,92-农绿色,93-黄绿色,94-渐变绿)',
                                   `accept_time` datetime DEFAULT NULL COMMENT '接单时间',
                                   `carrier_status` char(1) DEFAULT '0' COMMENT '承运状态(0-待接单,1-待装车,2-运输中,3-已送达)',
                                   `status` char(1) DEFAULT '0' COMMENT '状态(0-正常,1-已删除)',
                                   `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_cargo_id` (`cargo_id`),
                                   KEY `idx_task_id` (`task_id`),
                                   KEY `idx_plate_number` (`plate_number`),
                                   KEY `idx_carrier_status` (`carrier_status`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COMMENT='承运司机表';
-- ----------------------------
-- 4. 常发货源表
-- ----------------------------
drop table if exists frequent_cargo;
CREATE TABLE `frequent_cargo` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `customer_id` bigint(20) NOT NULL COMMENT '货主关联ID',
                                  `cargo_name` varchar(100) NOT NULL COMMENT '货物名称',
                                  `cargo_type` varchar(50) NOT NULL COMMENT '货物类型',
                                  `packaging_method` varchar(50) NOT NULL COMMENT '包装方式',
                                  `loading_province_code` varchar(20) DEFAULT NULL COMMENT '装货省编码',
                                  `loading_province_name` varchar(50) DEFAULT NULL COMMENT '装货省名称',
                                  `loading_city_code` varchar(20) DEFAULT NULL COMMENT '装货市编码',
                                  `loading_city_name` varchar(50) DEFAULT NULL COMMENT '装货市名称',
                                  `loading_district_code` varchar(20) DEFAULT NULL COMMENT '装货区编码',
                                  `loading_district_name` varchar(50) DEFAULT NULL COMMENT '装货区名称',
                                  `loading_address` varchar(255) DEFAULT NULL COMMENT '装货详细地址',
                                  `loading_longitude` decimal(10,6) DEFAULT NULL COMMENT '装货地址经度',
                                  `loading_latitude` decimal(10,6) DEFAULT NULL COMMENT '装货地址纬度',
                                  `unloading_province_code` varchar(20) DEFAULT NULL COMMENT '卸货省编码',
                                  `unloading_province_name` varchar(50) DEFAULT NULL COMMENT '卸货省名称',
                                  `unloading_city_code` varchar(20) DEFAULT NULL COMMENT '卸货市编码',
                                  `unloading_city_name` varchar(50) DEFAULT NULL COMMENT '卸货市名称',
                                  `unloading_district_code` varchar(20) DEFAULT NULL COMMENT '卸货区编码',
                                  `unloading_district_name` varchar(50) DEFAULT NULL COMMENT '卸货区名称',
                                  `unloading_address` varchar(255) DEFAULT NULL COMMENT '卸货详细地址',
                                  `unloading_longitude` decimal(10,6) DEFAULT NULL COMMENT '卸货地址经度',
                                  `unloading_latitude` decimal(10,6) DEFAULT NULL COMMENT '卸货地址纬度',
                                  `status` char(1) DEFAULT '0' COMMENT '状态(0-正常,1-已删除)',
                                  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                  `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_carrier_id` (`customer_id`),
                                  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8mb4 COMMENT='常发货源表';

-- ----------------------------
-- 添加daily_cargo_id字段到scheduler_task_plan表
-- ----------------------------
ALTER TABLE `scheduler_task_plan` 
ADD COLUMN `daily_cargo_id` bigint(20) DEFAULT NULL COMMENT '每日货源ID' AFTER `id`,
ADD INDEX `idx_daily_cargo_id` (`daily_cargo_id`) USING BTREE;



ALTER TABLE `scheduler_task_plan`
    ADD COLUMN `packaging_method` varchar(50) COMMENT '包装方式' AFTER `cargo_name`,
    ADD COLUMN `cargo_weight` decimal(10,2) COMMENT '货物数量(吨)' AFTER `packaging_method`,
    ADD COLUMN `cargo_volume` decimal(10,2) COMMENT '货物数量(方)' AFTER `cargo_weight`,
    ADD COLUMN `loading_province_code` varchar(20) COMMENT '装货省编码' AFTER `cargo_volume`,
    ADD COLUMN `loading_province_name` varchar(50) COMMENT '装货省名称' AFTER `loading_province_code`,
    ADD COLUMN `loading_city_code` varchar(20) COMMENT '装货市编码' AFTER `loading_province_name`,
    ADD COLUMN `loading_city_name` varchar(50) COMMENT '装货市名称' AFTER `loading_city_code`,
    ADD COLUMN `loading_district_code` varchar(20) COMMENT '装货区编码' AFTER `loading_city_name`,
    ADD COLUMN `loading_district_name` varchar(50) COMMENT '装货区名称' AFTER `loading_district_code`,
    ADD COLUMN `loading_address` varchar(255) COMMENT '装货详细地址' AFTER `loading_district_name`,
    ADD COLUMN `loading_longitude` decimal(10,6) COMMENT '装货地址经度' AFTER `loading_address`,
    ADD COLUMN `loading_latitude` decimal(10,6) COMMENT '装货地址纬度' AFTER `loading_longitude`,
    ADD COLUMN `unloading_province_code` varchar(20) COMMENT '卸货省编码' AFTER `loading_latitude`,
    ADD COLUMN `unloading_province_name` varchar(50) COMMENT '卸货省名称' AFTER `unloading_province_code`,
    ADD COLUMN `unloading_city_code` varchar(20) COMMENT '卸货市编码' AFTER `unloading_province_name`,
    ADD COLUMN `unloading_city_name` varchar(50) COMMENT '卸货市名称' AFTER `unloading_city_code`,
    ADD COLUMN `unloading_district_code` varchar(20) COMMENT '卸货区编码' AFTER `unloading_city_name`,
    ADD COLUMN `unloading_district_name` varchar(50) COMMENT '卸货区名称' AFTER `unloading_district_code`,
    ADD COLUMN `unloading_address` varchar(255) COMMENT '卸货详细地址' AFTER `unloading_district_name`,
    ADD COLUMN `unloading_longitude` decimal(10,6) COMMENT '卸货地址经度' AFTER `unloading_address`,
    ADD COLUMN `unloading_latitude` decimal(10,6) COMMENT '卸货地址纬度' AFTER `unloading_longitude`;



INSERT INTO `zly_ai_scheduler`.`sys_job` (`job_id`, `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, '每五分钟执行一次自动下架处理', 'SYSTEM', 'sysTask.autoOfflineProcess', '0 0/5 * * * ?', '3', '1', '0', 'admin', '2025-07-08 17:01:06', '', '2925-07-08 17:01:06', '过了计划装车最晚时间的24小时，自动下架，需求车辆数量满足后，当天23:59自动下架');
INSERT INTO `zly_ai_scheduler`.`sys_job` (`job_id`, `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, '每天凌晨执行货主状态更新任务', 'SYSTEM', 'sysTask.updateCarrierStatus', '0 0 0 * * ?', '3', '1', '0', 'admin', '2025-07-08 17:01:06', '', '2925-07-08 17:01:06', '每天凌晨执行货主状态更新任务');





INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (16, '货物类型', 'cargo_type', '0', 'admin', '2021-10-27 15:04:18', '', NULL, NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (313, 0, '煤炭及制品', '0100', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (314, 0, '石油、天然气及制品', '0200', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (315, 0, '金属矿石', '0300', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (316, 0, '钢铁', '0400', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (317, 0, '矿建材料', '0500', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (318, 0, '水泥', '0600', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (319, 0, '木材', '0700', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (320, 0, '非金属矿石', '0800', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (321, 0, '化肥及农药', '0900', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (322, 0, '盐', '1000', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (323, 0, '粮食', '1100', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (324, 0, '机械、设备、电器', '1200', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (325, 0, '轻工原料及制品', '1300', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (326, 0, '有色金属', '1400', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (327, 0, '轻工医药产品', '1500', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (328, 0, '鲜活农产品', '1601', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (329, 0, '冷藏冷冻货物', '1602', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (330, 0, '商品汽车', '1701', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (331, 0, '快递', '1702', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (332, 0, '其他', '1700', 'cargo_type', '', 'default', 'N', '0', 'admin', '2021-10-27 15:09:54', '', '0000-00-00 00:00:00', '');