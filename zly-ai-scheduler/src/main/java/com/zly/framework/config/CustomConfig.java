package com.zly.framework.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 自定义配置
 *
 * <AUTHOR>
 *
 */
@Component
@Data
public class CustomConfig {

	/** gps接口服务访问账号 */
	@Value("${custom.gps.acc}")
	private String gpsAcc;

	/** gps接口服务访问密码 */
	@Value("${custom.gps.pas}")
	private String gpsPas;

	/** gps接口服务访问地址 */
	@Value("${custom.gps.url}")
	private String gpsUrl;

	/** gps最新位置方法名 */
	@Value("${custom.gps.lastLocation}")
	private String lastLocation;

	/** 补全轨迹方法名 */
	@Value("${custom.gps.historyLocation}")
	private String historyLocation;

	/** gps历史轨迹方法名 */
	@Value("${custom.gps.historyTrack}")
	private String gpsHistoryTrack;

	/** gps查询轨迹集合方法名 */
	@Value("${custom.gps.trackList}")
	private String gpsTrackList;

	/** gps车辆是否入网方法名 */
	@Value("${custom.gps.checkExist}")
	private String checkExist;

	/** 高德API接口key */
	@Value("${custom.amap.webServerKey}")
	private String amapWebServerKey;

	/** 高德API距离测量接口地址 */
	@Value("${custom.amap.distanceUrl}")
	private String amapDistanceUrl;

	/** 高德API逆地理编码接口地址 */
	@Value("${custom.amap.geocodeUrl}")
	private String amapGeocodeUrl;

	/** 高德API地理编码接口地址 */
	@Value("${custom.amap.geoUrl}")
	private String amapGeoUrl;

	/** 高德API货车路径规划接口地址 */
	@Value("${custom.amap.truckDirectionUrl}")
	private String amapTruckDirectionUrl;

	/** 高德API驾车路线规划接口地址 */
	@Value("${custom.amap.drivingDirectionUrl}")
	private String amapDrivingDirectionUrl;

	/** 蜂鸟迅达接口地址 */
	@Value("${custom.beeBird.apiUrl}")
	private String beeBirdApiUrl;

	/** 蜂鸟迅达获取变量 */
	@Value("${custom.beeBird.getField}")
	private String beeBirdGetField;

	/** 蜂鸟迅达获取任务列表 */
	@Value("${custom.beeBird.taskList}")
	private String beeBirdTaskList;

	/** 蜂鸟迅达开始任务 */
	@Value("${custom.beeBird.taskStart}")
	private String beeBirdTaskStart;

	/** 蜂鸟迅达结束任务 */
	@Value("${custom.beeBird.taskStop}")
	private String beeBirdTaskStop;

	/** 蜂鸟迅达添加号码 */
	@Value("${custom.beeBird.taskNumber}")
	private String beeBirdTaskNumber;

	/** 蜂鸟迅达添加号码新 */
	@Value("${custom.beeBird.taskNumberNew}")
	private String beeBirdTaskNumberNew;

	/** 蜂鸟迅达重置号码 */
	@Value("${custom.beeBird.numberReset}")
	private String beeBirdResetNumber;

	/** 蜂鸟迅达删除号码 */
	@Value("${custom.beeBird.deleteNumber}")
	private String beeBirdDeleteNumber;

	/** 蜂鸟迅达获取话单录音文字 */
	@Value("${custom.beeBird.getCallOrder}")
	private String beeBirdGetCallOrder;

	/** 阿里云百炼API-Key */
	@Value("${custom.bailian.apiKey}")
	private String bailianApiKey;

	/** 阿里云百炼智能体应用ID */
	@Value("${custom.bailian.appId}")
	private String bailianAppId;

	/** 阿里云百炼数据库默认目录 */
	@Value("${custom.bailian.agentKey}")
	private String bailianAgentKey;

	/** 阿里云百炼空间ID */
	@Value("${custom.bailian.modelId}")
	private String bailianModelId;

	/** 阿里云百炼知识库ID */
	@Value("${custom.bailian.indexId}")
	private String bailianIndexId;

	/** 阿里云百炼通义千问模型名称 */
	@Value("${custom.bailian.model}")
	private String bailianModel;

	/** 查询4.0系统司机画像数据的URL */
	@Value("${custom.scheduler.driverProfile.apiUrl}")
	private String driverProfileApiUrl;

	/** 询价功能API地址 */
	@Value("${custom.inquiry.apiUrl}")
	private String inquiryApiUrl;

	/** 询价功能API-Key */
	@Value("${custom.inquiry.apiKey}")
	private String inquiryApiKey;

	/** 七牛云accessKey */
	public static String qiNiuAccessKeyStatic;

	/** 七牛云secretKey */
	public static String qiNiuSecretKeyStatic;

	/** 七牛云访问域名 */
	public static String qiNiuDomainStatic;

	/** 七牛云存储空间名 */
	public static String qiNiuBucketStatic;

	/** 七牛云存储区域,华东:0 华北:1 华南:2 */
	public static String qiNiuRegionStatic;

	/** 七牛云访问域名 */
	@Value("${custom.qiniu.domain}")
	private String qiniuDomain;

	@Value("${custom.qiniu.accessKey}")
	public void setQiNiuAccessKeyStatic(String qiNiuAccessKeyStatic) {
		CustomConfig.qiNiuAccessKeyStatic = qiNiuAccessKeyStatic;
	}

	@Value("${custom.qiniu.secretKey}")
	public void setQiNiuSecretKeyStatic(String qiNiuSecretKeyStatic) {
		CustomConfig.qiNiuSecretKeyStatic = qiNiuSecretKeyStatic;
	}

	@Value("${custom.qiniu.domain}")
	public void setQiNiuDomainStatic(String qiNiuDomainStatic) {
		CustomConfig.qiNiuDomainStatic = qiNiuDomainStatic;
	}

	@Value("${custom.qiniu.bucket}")
	public void setQiNiuBucketStatic(String qiNiuBucketStatic) {
		CustomConfig.qiNiuBucketStatic = qiNiuBucketStatic;
	}

	@Value("${custom.qiniu.region}")
	public void setQiNiuRegionStatic(String qiNiuRegionStatic) {
		CustomConfig.qiNiuRegionStatic = qiNiuRegionStatic;
	}
}
