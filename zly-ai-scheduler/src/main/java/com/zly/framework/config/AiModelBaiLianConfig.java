package com.zly.framework.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 物流文本解析配置类
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "custom.bailian")
public class AiModelBaiLianConfig {

    /**
     * 通义千问API Key
     */
    private String apiKey = "sk-89675ad40f0a4d29992950785daf1a17";

    /**
     * 通义千问appId 智能体应用id
     */
    private String appId = "ace7dc35d9cd43a7aaaa5cbd3b1b56b3";

	private String agentKey;

	private String modelId = "llm-ynhw38ot2ttdm2ox";

	public  String indexId = "eu5u5ue600";

    /**
     * 通义千问模型名称
     */
    //private String model = "qwen-plus";
	private String model = "qwen-turbo";

}