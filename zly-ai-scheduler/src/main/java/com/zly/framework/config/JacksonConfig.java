package com.zly.framework.config;

import java.text.SimpleDateFormat;
import java.util.TimeZone;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * @Title: zly-ai-scheduler
 * @Description: Jackson配置
 * @Create DateTime: 2021年8月11日 下午8:04:19
 * 
 * <AUTHOR>
 */
@Configuration
public class JacksonConfig {

	@Bean
	public MappingJackson2HttpMessageConverter jackson2HttpMessageConverter() {
		final Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder();
		builder.serializationInclusion(JsonInclude.Include.NON_NULL);
		final ObjectMapper objectMapper = builder.build();
		SimpleModule simpleModule = new SimpleModule();
		// Long 转为 String 防止 js 丢失精度
		simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
		objectMapper.registerModule(simpleModule);
		// 忽略 transient 关键词属性
		objectMapper.configure(MapperFeature.PROPAGATE_TRANSIENT_MARKER, true);
		// 时区设置
		objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
		objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
		// 忽略 transient 关键词属性
		objectMapper.configure(MapperFeature.PROPAGATE_TRANSIENT_MARKER, true);
		return new MappingJackson2HttpMessageConverter(objectMapper);
	}

}
