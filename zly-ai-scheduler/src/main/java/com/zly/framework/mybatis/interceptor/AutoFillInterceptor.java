package com.zly.framework.mybatis.interceptor;

import com.zly.common.utils.SecurityUtils;
import com.zly.framework.web.domain.BaseEntity;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Properties;

/**
 * MyBatis拦截器 - 用于自动填充字段
 * 
 * <AUTHOR>
 */
@Component
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
public class AutoFillInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];
        if (parameter instanceof BaseEntity) {
            BaseEntity entity = (BaseEntity) parameter;
            Date now = new Date();
            // 在获取用户ID的地方添加异常处理
            Long userId = null;
            try {
                userId = SecurityUtils.getUserId();
            } catch (Exception e) {
                // 用户未登录或其他异常情况，不设置用户ID
            }
            String userIdStr = userId != null ? userId.toString() : null;
            // 根据SQL类型进行不同处理
            if (SqlCommandType.INSERT.equals(ms.getSqlCommandType())) {
                // 插入操作
                if (entity.getCreateTime() == null) {
                    entity.setCreateTime(now);
                }
                if (entity.getUpdateTime() == null) {
                    entity.setUpdateTime(now);
                }
                if (userIdStr != null && entity.getCreateBy() == null) {
                    entity.setCreateBy(userIdStr);
                }
                if (userIdStr != null && entity.getUpdateBy() == null) {
                    entity.setUpdateBy(userIdStr);
                }
            } else if (SqlCommandType.UPDATE.equals(ms.getSqlCommandType())) {
                // 更新操作
                entity.setUpdateTime(now);
                if (userIdStr != null) {
                    entity.setUpdateBy(userIdStr);
                }
            }
        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以在这里设置属性
    }
}