package com.zly.framework.knowledge;

import com.aliyun.bailian20231229.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 百炼API客户端工厂类
 * 使用Spring的Bean方式管理Client对象，确保单例
 */
@Configuration
public class BailianClientFactory {

    @Value("${custom.bailian.accessKeyId:LTAI5tLJuq7XAi7gknAt3qRg}")
    private String accessKeyId;

    @Value("${custom.bailian.accessKeySecret:******************************}")
    private String accessKeySecret;

    @Value("${custom.bailian.endpoint:bailian.cn-beijing.aliyuncs.com}")
    private String endpoint;

    private static Client clientInstance;

    /**
     * 创建百炼API客户端Bean
     * 
     * @return 百炼API客户端实例
     * @throws Exception 创建客户端异常
     */
    @Bean
    public Client bailianClient() throws Exception {
        if (clientInstance == null) {
            synchronized (BailianClientFactory.class) {
                if (clientInstance == null) {
                    Config config = new Config()
                            .setAccessKeyId(accessKeyId)
                            .setAccessKeySecret(accessKeySecret);
                    config.endpoint = endpoint;
                    clientInstance = new Client(config);
                }
            }
        }
        return clientInstance;
    }

    /**
     * 获取百炼API客户端实例（非Spring环境使用）
     * 
     * @return 百炼API客户端实例
     * @throws Exception 创建客户端异常
     */
    public static Client getClient() throws Exception {
        if (clientInstance == null) {
            synchronized (BailianClientFactory.class) {
                if (clientInstance == null) {
                    // 使用默认配置创建客户端
                    Config config = new Config()
                            .setAccessKeyId("")
                            .setAccessKeySecret("");
                    config.endpoint = "bailian.cn-beijing.aliyuncs.com";
                    clientInstance = new Client(config);
                }
            }
        }
        return clientInstance;
    }
}