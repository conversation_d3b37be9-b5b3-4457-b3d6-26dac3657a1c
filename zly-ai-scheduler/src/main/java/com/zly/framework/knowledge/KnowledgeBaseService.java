package com.zly.framework.knowledge;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.bailian20231229.Client;
import com.aliyun.bailian20231229.models.ApplyFileUploadLeaseRequest;
import com.aliyun.bailian20231229.models.ApplyFileUploadLeaseResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.google.gson.internal.LinkedTreeMap;
import com.aliyun.bailian20231229.models.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.*;

import static com.zly.project.driver.service.impl.DriverProfileServiceImpl.*;

/**
 * 大模型知识库服务类
 * 用于实现知识库数据的更新、上传等操作
 */
@Slf4j
@Component
public class KnowledgeBaseService {

    // 将静态变量改为实例变量
    private static Client bailianClient = null;

    // 通过构造函数注入
    public KnowledgeBaseService(Client bailianClient) {
        KnowledgeBaseService.bailianClient = bailianClient;
    }
    /**
     * <b>description</b> :
     * <p>使用工厂类获取百炼API客户端实例</p>
     * @return Client
	 */
    public static Client getClient() {
        // 使用工厂类获取客户端实例，确保单例
        return bailianClient;
    }

    /**
     * 申请文件上传租约
     * 
     * @param filePath 文件路径
     * @return 租约申请结果，包含上传URL和租约ID等信息
     * @throws Exception 异常信息
     */
    public static UploadResult applyFileUploadLease(String filePath,String DEFAULT_AGENT_KEY,String DEFAULT_MODEL_ID,String DEFAULT_INDEX_ID) throws Exception {
        UploadResult result = new UploadResult();
        // 获取文件MD5
        String md5 = getFileMd5(filePath);
        // 获取文件大小
        Path path = Paths.get(filePath);
        long fileSize = Files.size(path);
        // 获取文件名
        String fileName = path.getFileName().toString();
        // 创建客户端
        Client client = getClient();
        // 创建请求
        ApplyFileUploadLeaseRequest request = new ApplyFileUploadLeaseRequest()
                .setFileName(fileName)
                .setMd5(md5)
                .setSizeInBytes(String.valueOf(fileSize))
                .setCategoryType("UNSTRUCTURED");
        // 运行时选项
        RuntimeOptions runtime = new RuntimeOptions();
        Map<String, String> headers = new HashMap<>();
        try {
            // 调用API申请租约
            ApplyFileUploadLeaseResponse response = client.applyFileUploadLeaseWithOptions(
                    DEFAULT_AGENT_KEY, DEFAULT_MODEL_ID, request, headers, runtime);
            // 处理响应
            Integer statusCode = response.getStatusCode();
            result.setCode(String.valueOf(statusCode));
            result.setStatus(String.valueOf(statusCode));
            result.setIsSuccess(statusCode == 200);
            result.setRequestId(response.getBody().getRequestId());
            result.setMessage(response.getBody().getMessage());
            // 如果成功，设置数据
            if (statusCode == 200) {
                JSONObject data = new JSONObject();
                String leaseId = response.getBody().getData().getFileUploadLeaseId();
                data.put("fileUploadLeaseId", leaseId);
                data.put("url", response.getBody().getData().getParam().getUrl());
                data.put("method", response.getBody().getData().getParam().getMethod());
                // 获取headers
                LinkedTreeMap<String, String> responseHeaders =
                        (LinkedTreeMap<String, String>) response.getBody().getData().getParam().getHeaders();
                data.put("headers", responseHeaders);
                result.setData(data);
                // 上传文件
                boolean uploadSuccess = uploadFile(
                        response.getBody().getData().getParam().getUrl(),
                        filePath,
                        responseHeaders.get("X-bailian-extra"),
                        responseHeaders.get("Content-Type")
                );
                // 更新上传结果
                if (!uploadSuccess) {
                    result.setIsSuccess(false);
                    result.setMessage("文件上传失败");
                    return result;
                }
                // 上传成功后，添加文件到数据中心
                log.info("文件上传成功，开始添加文件到数据中心...");
                String fileId = addFile(client, leaseId,DEFAULT_AGENT_KEY,DEFAULT_MODEL_ID);
                if (fileId == null) {
                    result.setIsSuccess(false);
                    result.setMessage("添加文件到数据中心失败");
                    return result;
                }
                // 添加文件ID到返回数据
                data.put("fileId", fileId);
                // 等待文件解析完成
                log.info("等待文件解析完成...");
                boolean parseSuccess = waitForFileParsing(client, fileId,DEFAULT_MODEL_ID);
                if (!parseSuccess) {
                    result.setIsSuccess(false);
                    result.setMessage("文件解析失败或超时");
                    return result;
                }
                log.info("文件解析完成，可以进行后续操作");
                data.put("parseStatus", "PARSE_SUCCESS");
            }
            return result;
        } catch (TeaException error) {
            result.setIsSuccess(false);
            result.setMessage(error.getMessage());
            JSONObject data = new JSONObject();
            data.put("error", error.getMessage());
            data.put("recommend", error.getData().get("Recommend"));
            result.setData(data);
            return result;
        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setMessage(e.getMessage());
            JSONObject data = new JSONObject();
            data.put("error", e.getMessage());
            result.setData(data);
            return result;
        }
    }
    
    /**
     * 添加文件到数据中心
     * 
     * @param client 客户端
     * @param leaseId 租约ID
     * @return 文件ID，失败返回null
     */
    private static String addFile(Client client, String leaseId,String DEFAULT_AGENT_KEY,String DEFAULT_MODEL_ID) {
        try {
            // 创建添加文件请求
            AddFileRequest addFileRequest = new AddFileRequest()
                    .setParser("DASHSCOPE_DOCMIND")
                    .setCategoryId(DEFAULT_AGENT_KEY)
                    .setCategoryType("UNSTRUCTURED")
                    .setLeaseId(leaseId);
            // 运行时选项
            RuntimeOptions runtime = new RuntimeOptions();
            Map<String, String> headers = new HashMap<>();
            // 调用API添加文件
            AddFileResponse addFileResponse = client.addFileWithOptions(
                    DEFAULT_MODEL_ID, addFileRequest, headers, runtime);
            // 检查响应
            if (addFileResponse.getStatusCode() == 200) {
                String fileId = addFileResponse.getBody().getData().getFileId();
                log.info("文件添加成功，文件ID: " + fileId);
                return fileId;
            } else {
                log.info("文件添加失败: " + addFileResponse.getBody().getMessage());
                return null;
            }
        } catch (Exception e) {
            log.info("添加文件异常: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 等待文件解析完成
     * 
     * @param client 客户端
     * @param fileId 文件ID
     * @return 是否解析成功
     */
    private static boolean waitForFileParsing(Client client, String fileId,String DEFAULT_MODEL_ID) {
        final int MAX_RETRY = 200; // 最大重试次数
        final int RETRY_INTERVAL = 50000; // 重试间隔（毫秒）
        try {
            for (int i = 0; i < MAX_RETRY; i++) {
                // 查询文件状态
                DescribeFileResponse describeFileResponse = client.describeFile(DEFAULT_MODEL_ID, fileId);
                // 检查状态
                String status = describeFileResponse.getBody().getData().getStatus();
                log.info("文件解析状态: " + status + " (尝试 " + (i + 1) + "/" + MAX_RETRY + ")");
                // 如果解析成功，返回true
                if ("PARSE_SUCCESS".equals(status)) {
                    return true;
                }
                // 如果解析失败，返回false
                if ("PARSE_FAILED".equals(status)) {
                    log.info("文件解析失败");
                    return false;
                }
                // 等待一段时间后重试
                Thread.sleep(RETRY_INTERVAL);
            }
            log.info("等待文件解析超时");
            return false;
        } catch (Exception e) {
            log.info("查询文件状态异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 上传文件到预签名URL
     * 
     * @param preSignedUrl 预签名URL
     * @param filePath 文件路径
     * @param extraHeader X-bailian-extra 头信息
     * @param contentType Content-Type 头信息
     * @return 上传是否成功
     */
    private static boolean uploadFile(String preSignedUrl, String filePath, String extraHeader, String contentType) {
        HttpURLConnection connection = null;
        try {
            // 创建URL对象
            URL url = new URL(preSignedUrl);
            connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法
            connection.setRequestMethod("PUT");
            // 允许输出
            connection.setDoOutput(true);
            // 设置请求头
            connection.setRequestProperty("X-bailian-extra", extraHeader);
            connection.setRequestProperty("Content-Type", contentType);
            // 读取文件并上传
            try (DataOutputStream outStream = new DataOutputStream(connection.getOutputStream());
                 FileInputStream fileInputStream = new FileInputStream(filePath)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    outStream.write(buffer, 0, bytesRead);
                }
                outStream.flush();
            }
            // 检查响应
            int responseCode = connection.getResponseCode();
            return responseCode == HttpURLConnection.HTTP_OK;
        } catch (Exception e) {
            return false;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    
    /**
     * 获取文件的MD5值
     * 
     * @param filePath 文件路径
     * @return MD5值
     * @throws Exception 异常信息
     */
    private static String getFileMd5(String filePath) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("MD5");
        try (InputStream is = Files.newInputStream(Paths.get(filePath))) {
            byte[] buffer = new byte[1024];
            int read;
            while ((read = is.read(buffer)) > 0) {
                digest.update(buffer, 0, read);
            }
        }
        byte[] md5Bytes = digest.digest();
        StringBuilder md5String = new StringBuilder();
        for (byte b : md5Bytes) {
            md5String.append(String.format("%02x", b));
        }
        return md5String.toString();
    }
    
    /**
     * 将文件添加到知识库
     * 
     * @param fileId 文件ID
     * @param indexId 知识库ID
     * @return 添加结果
     */
    public static UploadResult addFileToKnowledgeBase(String fileId, String indexId,String DEFAULT_MODEL_ID) {
        UploadResult result = new UploadResult();
        Client client;
        try {
            // 创建客户端
            client = getClient();
            // 创建添加文档任务请求
            SubmitIndexAddDocumentsJobRequest submitRequest = new SubmitIndexAddDocumentsJobRequest()
                    .setIndexId(indexId)
                    .setSourceType("DATA_CENTER_FILE")
                    .setDocumentIds(Collections.singletonList(fileId));
            // 运行时选项
            RuntimeOptions runtime = new RuntimeOptions();
            Map<String, String> headers = new HashMap<>();
            // 调用API提交添加文档任务
            SubmitIndexAddDocumentsJobResponse submitResponse = client.submitIndexAddDocumentsJobWithOptions(
                    DEFAULT_MODEL_ID, submitRequest, headers, runtime);
            // 检查响应
            if (submitResponse.getStatusCode() == 200 && submitResponse.getBody().getSuccess()) {
                String jobId = submitResponse.getBody().getData().getId();
                log.info("添加文档任务提交成功，任务ID: " + jobId);
                // 等待任务完成
                boolean jobSuccess = waitForJobCompletion(client, indexId, jobId,DEFAULT_MODEL_ID);
                // 设置结果
                result.setIsSuccess(jobSuccess);
                result.setCode(String.valueOf(submitResponse.getStatusCode()));
                result.setStatus(jobSuccess ? "COMPLETED" : "FAILED");
                result.setRequestId(submitResponse.getBody().getRequestId());
                result.setMessage(jobSuccess ? "文档添加成功" : "文档添加失败或超时");
                // 设置数据
                JSONObject data = new JSONObject();
                data.put("jobId", jobId);
                data.put("fileId", fileId);
                data.put("indexId", indexId);
                result.setData(data);
            } else {
                result.setIsSuccess(false);
                result.setCode(String.valueOf(submitResponse.getStatusCode()));
                result.setStatus("FAILED");
                result.setRequestId(submitResponse.getBody().getRequestId());
                result.setMessage("提交添加文档任务失败: " + submitResponse.getBody().getMessage());
            }
            return result;
        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setMessage("添加文档到知识库异常: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 等待任务完成
     * 
     * @param client 客户端
     * @param indexId 知识库ID
     * @param jobId 任务ID
     * @return 是否成功完成
     */
    private static boolean waitForJobCompletion(Client client, String indexId, String jobId,String DEFAULT_MODEL_ID) {
        final int MAX_RETRY = 200; // 最大重试次数
        final int RETRY_INTERVAL = 50000; // 重试间隔（毫秒）
        try {
            for (int i = 0; i < MAX_RETRY; i++) {
                // 创建查询任务状态请求
                GetIndexJobStatusRequest statusRequest = new GetIndexJobStatusRequest()
                        .setIndexId(indexId)
                        .setJobId(jobId);
                
                // 查询任务状态
                GetIndexJobStatusResponse statusResponse = client.getIndexJobStatus(DEFAULT_MODEL_ID, statusRequest);
                // 检查状态
                String status = statusResponse.getBody().getData().getStatus();
                log.info("任务状态: " + status + " (尝试 " + (i + 1) + "/" + MAX_RETRY + ")");
                // 如果任务完成，返回true
                if ("COMPLETED".equals(status)) {
                    return true;
                }
                // 如果任务失败，返回false
                if ("FAILED".equals(status)) {
                    log.info("任务执行失败");
                    return false;
                }
                // 等待一段时间后重试
                Thread.sleep(RETRY_INTERVAL);
            }
            log.info("等待任务完成超时");
            return false;
        } catch (Exception e) {
            log.info("查询任务状态异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 从知识库中删除文档
     * 
     * @param fileId 文件ID
     * @param indexId 知识库ID
     * @return 删除结果
     */
    public static UploadResult deleteFileFromKnowledgeBase(String fileId, String indexId,String DEFAULT_MODEL_ID) {
        UploadResult result = new UploadResult();
        Client client;
        try {
            // 创建客户端
            client = getClient();
            // 创建删除文档请求
            DeleteIndexDocumentRequest deleteRequest = new DeleteIndexDocumentRequest()
                    .setIndexId(indexId)
                    .setDocumentIds(Collections.singletonList(fileId));
            // 运行时选项
            RuntimeOptions runtime = new RuntimeOptions();
            Map<String, String> headers = new HashMap<>();
            // 调用API删除文档
            DeleteIndexDocumentResponse deleteResponse = client.deleteIndexDocumentWithOptions(
                    DEFAULT_MODEL_ID, deleteRequest, headers, runtime);
            // 检查响应
            if (deleteResponse.getStatusCode() == 200 && deleteResponse.getBody().getSuccess()) {
                log.info("文档删除成功");
                // 设置结果
                result.setIsSuccess(true);
                result.setCode(String.valueOf(deleteResponse.getStatusCode()));
                result.setStatus("SUCCESS");
                result.setRequestId(deleteResponse.getBody().getRequestId());
                result.setMessage("文档删除成功");
                // 设置数据
                JSONObject data = new JSONObject();
                data.put("deletedDocuments", deleteResponse.getBody().getData().getDeletedDocument());
                data.put("fileId", fileId);
                data.put("indexId", indexId);
                result.setData(data);
            } else {
                result.setIsSuccess(false);
                result.setCode(String.valueOf(deleteResponse.getStatusCode()));
                result.setStatus("FAILED");
                result.setRequestId(deleteResponse.getBody().getRequestId());
                result.setMessage("删除文档失败: " + deleteResponse.getBody().getMessage());
            }
            return result;
        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setMessage("从知识库删除文档异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 从数据管理中删除文件
     *
     * @param fileId 文件ID
     * @return 删除结果
     */
    public static UploadResult deleteFileFromDataCenter(String fileId,String DEFAULT_MODEL_ID) {
        UploadResult result = new UploadResult();
        Client client;
        try {
            // 创建客户端
            client = getClient();
            // 运行时选项
            RuntimeOptions runtime = new RuntimeOptions();
            Map<String, String> headers = new HashMap<>();
            // 调用API删除文件
            DeleteFileResponse deleteResponse = client.deleteFileWithOptions(
                    fileId, DEFAULT_MODEL_ID, headers, runtime);
            // 检查响应
            if (deleteResponse.getStatusCode() == 200 && deleteResponse.getBody().getSuccess()) {
                log.info("数据管理文件删除成功，文件ID: " + fileId);
                // 设置结果
                result.setIsSuccess(true);
                result.setCode(String.valueOf(deleteResponse.getStatusCode()));
                result.setStatus("SUCCESS");
                result.setRequestId(deleteResponse.getBody().getRequestId());
                result.setMessage("数据管理文件删除成功");
                // 设置数据
                JSONObject data = new JSONObject();
                data.put("fileId", fileId);
                result.setData(data);
            } else {
                result.setIsSuccess(false);
                result.setCode(String.valueOf(deleteResponse.getStatusCode()));
                result.setStatus("FAILED");
                result.setRequestId(deleteResponse.getBody().getRequestId());
                result.setMessage("数据管理文件删除失败: " + deleteResponse.getBody().getMessage());
            }
            return result;
        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setMessage("从数据管理删除文件异常: " + e.getMessage());
            return result;
        }
    }

    /**
     * 查询知识库文件ID集合
     * @param indexId 知识库ID
     * @return 文件ID集合
     */
    public static List<String> listKnowledgeBaseFileIds(String workspaceId,String indexId,Integer pageNumber,Integer pageSize) {
        List<String> fileIds = new ArrayList<>();
        try {
            // 创建客户端
            Client client = getClient();
            // 创建检索请求
            com.aliyun.bailian20231229.models.ListIndexDocumentsRequest listIndexDocumentsRequest = new com.aliyun.bailian20231229.models.ListIndexDocumentsRequest()
                    .setIndexId(indexId)
                    .setPageNumber(pageNumber)
                    .setPageSize(pageSize);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            java.util.Map<String, String> headers = new java.util.HashMap<>();
            // 调用API获取文档列表
            com.aliyun.bailian20231229.models.ListIndexDocumentsResponse response = client.listIndexDocumentsWithOptions(
                    workspaceId,
                    listIndexDocumentsRequest,
                    headers,
                    runtime);
            // 处理响应结果
            if (response != null && response.getStatusCode() == 200 && response.getBody().getSuccess()) {
                JSONObject responseJson = JSONObject.parseObject(JSONObject.toJSONString(response.getBody()));
                JSONObject data = responseJson.getJSONObject("data");
                if (data != null) {
                    JSONArray documents = data.getJSONArray("documents");
                    if (documents != null && !documents.isEmpty()) {
                        for (int i = 0; i < documents.size(); i++) {
                            JSONObject document = documents.getJSONObject(i);
                            String fileId = document.getString("id");
                            if (StringUtils.isNotBlank(fileId)) {
                                fileIds.add(fileId);
                            }
                        }
                    }
                }
            }
        } catch (TeaException error) {
            log.error("查询知识库文件列表TeaException异常: {}", error.getMessage());
            if (error.getData() != null && error.getData().get("Recommend") != null) {
                log.error("诊断地址: {}", error.getData().get("Recommend"));
            }
        } catch (Exception e) {
            log.error("查询知识库文件列表异常: {}", e.getMessage());
        }
        return fileIds;
    }


    /**
     * 从知识库中检索信息
     *
     * @param query 查询内容
     * @param indexId 知识库ID，如果为空则使用默认知识库ID
     * @param topK 返回结果数量
     * @return 检索结果文本列表
     */
    public static List<String> retrieveFromKnowledgeBase(String query, String indexId, Integer topK,String DEFAULT_INDEX_ID,String DEFAULT_MODEL_ID) {
        List<String> resultTexts = new ArrayList<>();
        Client client;
        try {
            // 创建客户端
            client = getClient();
            // 创建检索请求
            RetrieveRequest retrieveRequest = new RetrieveRequest()
                    .setIndexId(indexId != null ? indexId : DEFAULT_INDEX_ID)
                    .setQuery(query)
                    .setDenseSimilarityTopK(topK != null ? topK : 100);
            // 运行时选项
            RuntimeOptions runtime = new RuntimeOptions();
            Map<String, String> headers = new HashMap<>();
            // 调用API检索信息
            log.info("开始从知识库检索信息，查询：{}", query);
            RetrieveResponse retrieveResponse = client.retrieveWithOptions(
                    DEFAULT_MODEL_ID, retrieveRequest, headers, runtime);
            // 检查响应
            if (retrieveResponse.getStatusCode() == 200 && retrieveResponse.getBody().getSuccess()) {
                log.info("知识库检索成功");
                // 解析返回结果
                JSONObject responseJson = JSONObject.parseObject(JSONObject.toJSONString(retrieveResponse.getBody()));
                JSONObject data = responseJson.getJSONObject("data");
                if (data != null) {
                    JSONArray nodes = data.getJSONArray("nodes");
                    if (nodes != null && !nodes.isEmpty()) {
                        for (int i = 0; i < nodes.size(); i++) {
                            JSONObject node = nodes.getJSONObject(i);
                            String text = node.getString("text");
                            if (StringUtils.isNotBlank(text)) {
                                // 处理文本，将多行合并为单行
                                text = text.replaceAll("\\n", " ").replaceAll("\\s+", " ").trim();
                                resultTexts.add(text);
                                log.debug("检索到文本: {}", text);
                            }
                        }
                    }
                }
                log.info("共检索到{}条结果", resultTexts.size());
            } else {
                log.error("知识库检索失败: {}", retrieveResponse.getBody().getMessage());
            }
            return resultTexts;
        } catch (TeaException error) {
            log.error("知识库检索TeaException异常: {}", error.getMessage());
            if (error.getData() != null && error.getData().get("Recommend") != null) {
                log.error("诊断地址: {}", error.getData().get("Recommend"));
            }
            return resultTexts;
        } catch (Exception e) {
            log.error("知识库检索异常: {}", e.getMessage());
            return resultTexts;
        }
    }

    /**
     * 从知识库中检索信息
     *
     * @param query 查询内容
     * @param indexId 知识库ID，如果为空则使用默认知识库ID
     * @param topK 返回结果数量
     * @return 检索结果文本列表
     */
    public static Map<String,String> retrieveFromKnowledgeBaseMap(String query, String indexId, Integer topK,String DEFAULT_INDEX_ID,String DEFAULT_MODEL_ID) {
        Map<String,String> map = new HashMap<>();
        Client client;
        try {
            // 创建客户端
            client = getClient();
            // 创建检索请求
            RetrieveRequest retrieveRequest = new RetrieveRequest()
                    .setIndexId(indexId != null ? indexId : DEFAULT_INDEX_ID)
                    .setQuery(query)
                    .setDenseSimilarityTopK(topK != null ? topK : 100);
            // 运行时选项
            RuntimeOptions runtime = new RuntimeOptions();
            Map<String, String> headers = new HashMap<>();
            // 调用API检索信息
            log.info("开始从知识库检索信息，查询：{}", query);
            RetrieveResponse retrieveResponse = client.retrieveWithOptions(
                    DEFAULT_MODEL_ID, retrieveRequest, headers, runtime);
            // 检查响应
            if (retrieveResponse.getStatusCode() == 200 && retrieveResponse.getBody().getSuccess()) {
                log.info("知识库检索成功");
                // 解析返回结果
                JSONObject responseJson = JSONObject.parseObject(JSONObject.toJSONString(retrieveResponse.getBody()));
                JSONObject data = responseJson.getJSONObject("data");
                if (data != null) {
                    JSONArray nodes = data.getJSONArray("nodes");
                    if (nodes != null && !nodes.isEmpty()) {
                        for (int i = 0; i < nodes.size(); i++) {
                            JSONObject node = nodes.getJSONObject(i);
                            String text = node.getString("text");
                            String key = node.getString("text");
                            if (StringUtils.isNotBlank(text)) {
                                // 处理文本，将多行合并为单行
                                text = text.replaceAll("\\n", " ").replaceAll("\\s+", " ").trim();
                                map.put(key,text);
                                log.debug("检索到文本: {}", text);
                            }
                        }
                    }
                }
                log.info("共检索到{}条结果", map.size());
            } else {
                log.error("知识库检索失败: {}", retrieveResponse.getBody().getMessage());
            }
            return map;
        } catch (TeaException error) {
            log.error("知识库检索TeaException异常: {}", error.getMessage());
            if (error.getData() != null && error.getData().get("Recommend") != null) {
                log.error("诊断地址: {}", error.getData().get("Recommend"));
            }
            return map;
        } catch (Exception e) {
            log.error("知识库检索异常: {}", e.getMessage());
            return map;
        }
    }

    
    /**
     * 完整的知识库更新流程
     * 
     * @param filePath 文件路径
     * @return 更新结果
     */
    public static UploadResult updateKnowledgeBase(String filePath,String DEFAULT_AGENT_KEY,String DEFAULT_MODEL_ID,String DEFAULT_INDEX_ID) {
        try {
            // 1. 申请文件上传租约并上传文件
            log.info("步骤1: 申请文件上传租约并上传文件");
            UploadResult uploadResult = applyFileUploadLease(filePath,DEFAULT_AGENT_KEY,DEFAULT_MODEL_ID,DEFAULT_INDEX_ID);
            if (!uploadResult.getIsSuccess()) {
                log.error("文件上传失败: " + uploadResult.getMessage());
                return uploadResult;
            }
            // 2. 获取文件ID
            String fileId = uploadResult.getData().getString("fileId");
            if (fileId == null || fileId.isEmpty()) {
                uploadResult.setIsSuccess(false);
                uploadResult.setMessage("未获取到文件ID");
                return uploadResult;
            }
            uploadResult.setFileId(fileId);
            // 返回最终结果
            return uploadResult;
        } catch (Exception e) {
            UploadResult result = new UploadResult();
            result.setIsSuccess(false);
            result.setMessage("更新知识库异常: " + e.getMessage());
            return result;
        }

    }

    public static void main(String[] args) {

        // 测试知识库检索
//        System.out.println("======= 测试知识库检索 =======");
//        String query = "常州市到上海市，货物钢铁";
//        List<String> retrieveResults = retrieveFromKnowledgeBase(query, DEFAULT_INDEX_ID, 100);
//
//        if (!retrieveResults.isEmpty()) {
//            System.out.println("检索成功！共找到" + retrieveResults.size() + "条结果");
//            System.out.println("查询: " + query);
//            System.out.println("知识库ID: " + DEFAULT_INDEX_ID);
//            System.out.println("===== 检索结果 =====");
//            for (int i = 0; i < retrieveResults.size(); i++) {
//                System.out.println("结果 " + (i + 1) + ":");
//                System.out.println(retrieveResults.get(i));
//                System.out.println("-------------------");
//            }
//        } else {
//            System.out.println("检索失败或未找到匹配结果");
//        }

        System.out.println("========== 测试结束 ==========");



//        String filePath = "/Users/<USER>/Downloads/百炼系列手机产品介绍.docx";
//        //上传文件到数据管理库
//        UploadResult uploadResult = updateKnowledgeBase(filePath);
//        //将文件添加到知识库
//        Boolean isSuccess = uploadResult.getIsSuccess();
//        String fileId = uploadResult.getFileId();
//        if (isSuccess && StringUtils.isNotBlank(fileId)){
//            UploadResult addResult = addFileToKnowledgeBase(fileId, DEFAULT_INDEX_ID);
//            Boolean isSuccess1 = addResult.getIsSuccess();
//            if (isSuccess1){
//                //存储的是旧的failId  执行成功了在去更新
//                UploadResult delUploadResult = deleteFileFromDataCenter("file_4150bfd5aa1b48a58931ca60f21e8e33_11395025");
//                log.info("delUploadResult:{}",delUploadResult);
//                UploadResult delIndexUploadResult = deleteFileFromKnowledgeBase("file_4150bfd5aa1b48a58931ca60f21e8e33_11395025", DEFAULT_INDEX_ID);
//                log.info("delIndexUploadResult:{}",delIndexUploadResult);
//                //更新新的failId
//            }
//        }
    }
}