package com.zly.framework.security.service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.zly.project.system.service.impl.SysUserServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zly.common.constant.UserConstants;
import com.zly.common.utils.StringUtils;
import com.zly.project.system.domain.SysRole;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.service.ISysMenuService;
import com.zly.project.system.service.ISysRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * 用户权限处理
 * 
 * <AUTHOR>
 */
@Component
public class SysPermissionService
{
    private static final Logger logger = LoggerFactory.getLogger(SysPermissionService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysMenuService menuService;

    @Resource
    private SysUserServiceImpl userService;

    /**
     * 获取角色数据权限
     * 
     * @param user 用户信息
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(SysUser user)
    {
        Set<String> roles = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdmin())
        {
            roles.add("admin");
        }
        else
        {
            roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     * 
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(SysUser user)
    {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (userService.isAdmin(user.getUserId()))
        {
            perms.add("*:*:*");
        }
        else
        {
            List<SysRole> roles = user.getRoles();
            if (!CollectionUtils.isEmpty(roles))
            {
                // 设置permissions属性，以便数据权限匹配权限
                for (SysRole role : roles)
                {
                    if (StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL) && !role.isAdmin())
                    {
                        Set<String> rolePerms = extractPermissionsFromJson(role.getPermissionsJson());
                        role.setPermissions(rolePerms);
                        perms.addAll(rolePerms);
                    }
                }
            }
            else
            {
                // 通过userId查询该用户的所有角色，然后从角色的permissionsJson中获取权限
                List<SysRole> userRoles = roleService.selectRolePermissionListByUserId(user.getUserId());
                if (!CollectionUtils.isEmpty(userRoles))
                {
                    for (SysRole role : userRoles)
                    {
                        if (StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL) && !role.isAdmin())
                        {
                            Set<String> rolePerms = extractPermissionsFromJson(role.getPermissionsJson());
                            perms.addAll(rolePerms);
                        }
                    }
                }
            }
        }
        return perms;
    }


    /**
     * 获取菜单数据权限
     *
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getCheckedKeysMenuPermission(SysUser user)
    {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.isAdmin())
        {
            perms.add("*:*:*");
        }
        else
        {
            List<SysRole> roles = user.getRoles();
            if (!CollectionUtils.isEmpty(roles))
            {
                // 设置permissions属性，以便数据权限匹配权限
                for (SysRole role : roles)
                {
                    if (StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL) && !role.isAdmin())
                    {
                        Set<String> rolePerms = extractCheckedKeysPermissionsFromJson(role.getPermissionsJson());
                        role.setPermissions(rolePerms);
                        perms.addAll(rolePerms);
                    }
                }
            }
            else
            {
                // 通过userId查询该用户的所有角色，然后从角色的permissionsJson中获取权限
                List<SysRole> userRoles = roleService.selectRolePermissionListByUserId(user.getUserId());
                if (!CollectionUtils.isEmpty(userRoles))
                {
                    for (SysRole role : userRoles)
                    {
                        if (StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL) && !role.isAdmin())
                        {
                            Set<String> rolePerms = extractCheckedKeysPermissionsFromJson(role.getPermissionsJson());
                            perms.addAll(rolePerms);
                        }
                    }
                }
            }
        }
        return perms;
    }




    /**
     * 从权限JSON字符串中提取权限集合
     *
     * @param permissionsJson 权限JSON字符串
     * @return 权限集合
     */
    private Set<String> extractPermissionsFromJson(String permissionsJson)
    {
        Set<String> permissions = new HashSet<>();

        if (StringUtils.isEmpty(permissionsJson))
        {
            return permissions;
        }

        try
        {
            JsonNode rootNode = objectMapper.readTree(permissionsJson);

            // 提取 halfCheckedKeys 数组中的权限
            JsonNode btnsNode = rootNode.get("halfCheckedKeys");
            if (btnsNode != null && btnsNode.isArray())
            {
                for (JsonNode btnNode : btnsNode)
                {
                    if (btnNode.isTextual())
                    {
                        permissions.add(btnNode.asText());
                    }
                }
            }

            // 可以根据需要提取其他权限，比如 checkedKeys
            JsonNode checkedKeysNode = rootNode.get("checkedKeys");
            if (checkedKeysNode != null && checkedKeysNode.isArray())
            {
                for (JsonNode keyNode : checkedKeysNode)
                {
                    if (keyNode.isTextual())
                    {
                        String key = keyNode.asText();
                        // 只添加包含冒号的权限字符串（过滤掉菜单名称）
                        permissions.add(key);
                    }
                }
            }
        }
        catch (JsonProcessingException e)
        {
            logger.error("解析权限JSON失败: {}", permissionsJson, e);
        }

        return permissions;
    }


    /**
     * 从权限JSON字符串中提取权限集合
     *
     * @param permissionsJson 权限JSON字符串
     * @return 权限集合
     */
    private Set<String> extractCheckedKeysPermissionsFromJson(String permissionsJson)
    {
        Set<String> permissions = new HashSet<>();

        if (StringUtils.isEmpty(permissionsJson))
        {
            return permissions;
        }
        try
        {
            JsonNode rootNode = objectMapper.readTree(permissionsJson);
            // 可以根据需要提取其他权限，比如 checkedKeys
            JsonNode checkedKeysNode = rootNode.get("checkedKeys");
            if (checkedKeysNode != null && checkedKeysNode.isArray())
            {
                for (JsonNode keyNode : checkedKeysNode)
                {
                    if (keyNode.isTextual())
                    {
                        String key = keyNode.asText();
                        permissions.add(key);
                    }
                }
            }
        }
        catch (JsonProcessingException e)
        {
            logger.error("解析权限JSON失败: {}", permissionsJson, e);
        }
        return permissions;
    }
}
