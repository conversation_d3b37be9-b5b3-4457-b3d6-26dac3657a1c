package com.zly.framework.aspect;

import com.zly.common.utils.SecurityUtils;
import com.zly.framework.web.domain.BaseEntity;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.Date;

@Aspect
@Component
public class EntityFieldAutoFillAspect {
    
    // 修改切点表达式，排除所有可能导致异常的方法
    @Before("(execution(* com.zly.project.*.mapper.*.insert*(..)) || execution(* com.zly.project.*.mapper.*.add*(..))) " +
            "&& !execution(* com.zly.project.monitor.mapper.SysLogininforMapper.insertLogininfor(..)) " +
            "&& !execution(* com.zly.project.*.mapper.*.updateUserProfile(..)) " +
            "&& !execution(* com.zly.project.system.mapper.SysUserMapper.updateUser(..)) " +
            "&& !execution(* com.zly.project.system.mapper.SysUserMapper.update*(..)) " +
            "&& !execution(* com.zly.project.monitor.mapper.*.insert*(..))")
    public void beforeInsert(JoinPoint joinPoint) {
        processInsertParams(joinPoint.getArgs());
    }
    
    @Before("execution(* com.zly.project.*.mapper.*.update*(..)) " +
            "&& !execution(* com.zly.project.*.mapper.*.updateUserProfile(..)) " +
            "&& !execution(* com.zly.project.system.mapper.SysUserMapper.updateUser(..)) " +
            "&& !execution(* com.zly.project.system.mapper.SysUserMapper.update*(..))")
    public void beforeUpdate(JoinPoint joinPoint) {
        processUpdateParams(joinPoint.getArgs());
    }
    
    private void processInsertParams(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof BaseEntity) {
                BaseEntity entity = (BaseEntity) arg;
                Date now = new Date();
                
                // 安全获取用户ID，避免异常
                Long userId = null;
                try {
                    userId = SecurityUtils.getUserId();
                } catch (Exception e) {
                    // 用户未登录或其他异常情况，不设置用户ID
                }
                
                if (entity.getCreateTime() == null) {
                    entity.setCreateTime(now);
                }
                if (entity.getUpdateTime() == null) {
                    entity.setUpdateTime(now);
                }
                if (userId != null) {
                    if (entity.getCreateBy() == null) {
                        entity.setCreateBy(userId.toString());
                    }
                    if (entity.getUpdateBy() == null) {
                        entity.setUpdateBy(userId.toString());
                    }
                }
            }
        }
    }
    
    private void processUpdateParams(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof BaseEntity) {
                BaseEntity entity = (BaseEntity) arg;
                entity.setUpdateTime(new Date());
                
                // 安全获取用户ID，避免异常
                Long userId = null;
                try {
                    userId = SecurityUtils.getUserId();
                } catch (Exception e) {
                    // 用户未登录或其他异常情况，不设置用户ID
                }
                
                if (userId != null) {
                    entity.setUpdateBy(userId.toString());
                }
            }
        }
    }
}