package com.zly.framework.aspectj.lang.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/31 11:19
 * @describe
 */

@Target({ ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ClientLog {

    /**
     * 是否为关键字
     * @return
     */
    boolean isKeyWord() default false;

    /**
     * 是否需要进行对 true比对 false不比对
     * @return
     */
    boolean hidden() default true;

    /**
     * 文本
     * @return
     */
    String text() default "";
}
