package com.zly.framework.web.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 通用返回结果
 * 
 * <AUTHOR>
 */
@Setter
@Getter
public class CommonResult<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 成功状态码 */
    public static final Integer SUCCESS_CODE = 200;

    /** 失败状态码 */
    public static final Integer ERROR_CODE = 500;

    // Getter and Setter methods
    /** 状态码 */
    private Integer code;

    /** 返回消息 */
    private String msg;

    /** 返回数据 */
    private T data;

    /** 错误详情 */
    private String detailMessage;

    public CommonResult()
    {
    }

    public CommonResult(Integer code, String msg)
    {
        this.code = code;
        this.msg = msg;
    }

    public CommonResult(Integer code, String msg, T data)
    {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 成功返回
     */
    public static <T> CommonResult<T> success()
    {
        return new CommonResult<>(SUCCESS_CODE, "操作成功");
    }

    /**
     * 成功返回
     */
    public static <T> CommonResult<T> success(T data)
    {
        return new CommonResult<>(SUCCESS_CODE, "操作成功", data);
    }

    /**
     * 成功返回
     */
    public static <T> CommonResult<T> success(String message, T data)
    {
        return new CommonResult<>(SUCCESS_CODE, message, data);
    }

    /**
     * 失败返回
     */
    public static <T> CommonResult<T> error()
    {
        return new CommonResult<>(ERROR_CODE, "操作失败");
    }

    /**
     * 失败返回
     */
    public static <T> CommonResult<T> error(String message)
    {
        return new CommonResult<>(ERROR_CODE, message);
    }

    /**
     * 失败返回
     */
    public static <T> CommonResult<T> error(Integer code, String message)
    {
        return new CommonResult<>(code, message);
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess()
    {
        return SUCCESS_CODE.equals(this.code);
    }

    /**
     * 判断是否失败
     */
    public boolean isError()
    {
        return !isSuccess();
    }

    @Override
    public String toString()
    {
        return "CommonResult{" +
                "code=" + code +
                ", message='" + msg + '\'' +
                ", data=" + data +
                ", detailMessage='" + detailMessage + '\'' +
                '}';
    }
}
