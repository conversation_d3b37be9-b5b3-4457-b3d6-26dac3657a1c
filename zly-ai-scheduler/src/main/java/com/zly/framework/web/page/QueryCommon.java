package com.zly.framework.web.page;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Title QueryCommon.java
 * @Description 查询请求通用类
 * @Create 2023-02-08 17:10
 *
 * <AUTHOR>
 *
 */
@Data
public class QueryCommon {

	@ApiModelProperty(hidden = true)
	private Integer pageNum = 1; // 分页参数：当前页

	@ApiModelProperty(hidden = true)
	private Integer offSet = 0; // 分页参数：当偏移量

	@ApiModelProperty(hidden = true)
	private Integer pageSize = 10; // 分页参数：每页条数

	@ApiModelProperty(hidden = true)
	private String dateStart; // 时间查询参数：开始日期

	@ApiModelProperty(hidden = true)
	private String dateEnd; // 时间查询参数：结束日期

	@ApiModelProperty(hidden = true)
	private List<Long> projectGroupIds; // 项目分组ID集合（查询项目权限用）

	@ApiModelProperty(hidden = true)
	private List<Long> contractIds; // 用户项目权限

	@ApiModelProperty(hidden = true)
	private List<Long> userMakeCodeIds; // 用户项目权限

	private Integer clientType; // 登录类型
}
