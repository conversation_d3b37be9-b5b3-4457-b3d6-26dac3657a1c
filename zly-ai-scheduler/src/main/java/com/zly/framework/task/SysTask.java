package com.zly.framework.task;

import javax.annotation.Resource;

import com.zly.framework.config.AiModelBaiLianConfig;
import com.zly.project.cargo.service.IDailyCargoService;
import com.zly.project.driver.service.DriverProfileService;
import com.zly.project.scheduler.service.ICustomerInfoService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.zly.common.utils.DateUtils;
import com.zly.project.match.service.TaskDriverMatchService;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.zly.framework.web.domain.AjaxResult.error;
import static com.zly.framework.web.domain.AjaxResult.success;

/**
 * 定时任务调度测试
 * 
 * <AUTHOR>
 */
@Component("sysTask")
@Slf4j
public class SysTask {

	@Resource
	private TaskDriverMatchService taskDriverMatchService;

	@Resource
	private DriverProfileService driverProfileService;

	@Resource
	private AiModelBaiLianConfig aiModelBaiLianConfig;

	@Resource
	private ICustomerInfoService carrierInfoService;

	@Resource
	private IDailyCargoService dailyCargoService;
	
	@Resource
	private RedissonClient redissonClient;
	
	private static final String AUTO_CALL_TASK_LOCK = "AUTO_CALL_TASK_LOCK";
	private static final String TIMEOUT_DRIVERS_LOCK = "TIMEOUT_DRIVERS_LOCK";
	private static final String SYNC_DRIVER_PROFILES_LOCK = "SYNC_DRIVER_PROFILES_LOCK";
	private static final long LOCK_WAIT_TIME = 0;

	// 将租约时间设置为-1，启用看门狗机制，自动续期
	private static final long LOCK_LEASE_TIME = -1;

	public void autoCallTask() {
		// 创建分布式锁
		RLock lock = redissonClient.getLock(AUTO_CALL_TASK_LOCK);
		boolean locked = false;
		try {
			//尝试获取锁，不等待，启用看门狗机制自动续期
			locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
			if (locked) {
				log.info("{}获取到分布式锁，开始自动外呼任务处理", DateUtils.getTime());
				taskDriverMatchService.autoCallTask();
				log.info("{}自动外呼任务处理成功", DateUtils.getTime());
			} else {
				log.info("{}未获取到分布式锁，跳过本次自动外呼任务", DateUtils.getTime());
			}
		} catch (Exception e) {
			log.error("自动外呼任务处理失败：{}", e.getMessage());
		} finally {
			// 确保锁被释放
			if (locked && lock.isHeldByCurrentThread()) {
				lock.unlock();
				log.info("{}释放分布式锁", DateUtils.getTime());
			}
		}
	}

	public void handleTimeoutDrivers() {
		// 创建分布式锁
		RLock lock = redissonClient.getLock(TIMEOUT_DRIVERS_LOCK);
		boolean locked = false;
		try {
			// 尝试获取锁，不等待，租约时间30秒
			locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
			if (locked) {
				log.info("{}获取到分布式锁，开始司机呼叫超时处理", DateUtils.getTime());
				taskDriverMatchService.handleTimeoutDrivers();
				log.info("{}司机呼叫超时处理成功", DateUtils.getTime());
			} else {
				log.info("{}未获取到分布式锁，跳过本次自动外呼任务", DateUtils.getTime());
			}
		} catch (Exception e) {
			log.error("自动处理呼叫超时司机失败：{}", e.getMessage());
		} finally {
			// 确保锁被释放
			if (locked && lock.isHeldByCurrentThread()) {
				lock.unlock();
				log.info("{}释放分布式锁", DateUtils.getTime());
			}
		}
	}

	/**
	 * 同步司机画像数据
	 *
	 */
	public void syncDriverProfiles()
	{
		// 创建分布式锁
		RLock lock = redissonClient.getLock(SYNC_DRIVER_PROFILES_LOCK);
		boolean locked = false;
		try {
			// 尝试获取锁，不等待，启用看门狗机制自动续期
			locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
			if (locked) {
				log.info("{}获取到分布式锁，开始同步司机画像数据", DateUtils.getTime());
				// 调用同步方法
				Map<String, Object> result = driverProfileService.syncAllDriverProfiles();
				// 记录同步结果
				Integer totalCount = (Integer) result.getOrDefault("totalCount", 0);
				Integer syncCount = (Integer) result.getOrDefault("syncCount", 0);
				String message = (String) result.getOrDefault("message", "");
				log.info("定时任务 - 同步司机画像数据执行完成，总数: {}，同步成功: {}，详情: {}",
						totalCount, syncCount, message);
				// 上传数据分片到知识库
				String agentKey = aiModelBaiLianConfig.getAgentKey();
				String modelId = aiModelBaiLianConfig.getModelId();
				String indexId = aiModelBaiLianConfig.getIndexId();
				Map<String, Object> uploadDriverProfilesToKnowledgeBase = driverProfileService.uploadDriverProfilesToKnowledgeBase(agentKey,modelId,indexId);
				if (uploadDriverProfilesToKnowledgeBase != null && Boolean.TRUE.equals(result.get("success"))) {
					log.info("定时任务 - 上传数据到知识库，uploadDriverProfilesToKnowledgeBase:{}",uploadDriverProfilesToKnowledgeBase);
				} else {
					// 如果上传失败，返回错误信息
					String errorMessage = uploadDriverProfilesToKnowledgeBase != null && result.get("message") != null ?
							result.get("message").toString() : "上传司机画像到知识库失败";
					log.info("定时任务 - 上传数据到知识库，异常信息:{}",errorMessage);
				}
				log.info("{}同步司机画像数据处理成功", DateUtils.getTime());
			} else {
				log.info("{}未获取到分布式锁，跳过本次同步司机画像数据任务", DateUtils.getTime());
			}
		} catch (Exception e) {
			log.error("定时任务 - 同步司机画像数据执行异常: ", e);
		} finally {
			// 确保锁被释放
			if (locked && lock.isHeldByCurrentThread()) {
				lock.unlock();
				log.info("{}释放分布式锁", DateUtils.getTime());
			}
		}
	}


	/**
	 * 每天凌晨执行货主状态更新任务
	 */
	public void updateCarrierStatus() {
		log.info("开始执行货主状态更新定时任务");
		try {
			carrierInfoService.updateCustomerStatusDaily();
		} catch (Exception e) {
			log.error("货主状态更新定时任务执行异常", e);
		}
		log.info("货主状态更新定时任务执行结束");
	}

	/**
	 * 每五分钟执行一次自动下架处理
	 * 1. 过了计划装车最晚时间的24小时，自动下架
	 * 2. 需求车辆数量满足后，当天23:59自动下架
	 */
	public void autoOfflineProcess() {
		log.info("开始执行自动下架的定时任务");
		try {
			dailyCargoService.autoOfflineProcess();
		} catch (Exception e) {
			log.error("自动下架定时任务执行异常", e);
		}
		log.info("自动下架定时任务执行结束");
	}

}
