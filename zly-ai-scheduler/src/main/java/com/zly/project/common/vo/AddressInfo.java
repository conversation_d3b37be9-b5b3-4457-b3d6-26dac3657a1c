package com.zly.project.common.vo;

import com.zly.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title AddressInfo
 * @Description 地址信息实体类
 * @Create 2019-11-21 16:22
 *
 * <AUTHOR>
 *
 */
@Data
public class AddressInfo {

	private Long id;
	/** 单位名称 */
	private String corporationName;
	/** 详细地址 */
	private String addressDetail;
	/** 经度 */
	private String longitude;
	/** 维度 */
	private String latitude;
	/** 省份 */
	private String province;
	/** 市 */
	private String city;
	/** 区 */
	private String district;
	/** 地点(前端地图需要) */
	private String location;
	/** 联系人 */
	private String person;
	/** 联系电话 */
	private String phone;

	/** 收货地点的国家行政区划代码或国别代码 */
	@Excel(name = "收货地点的国家行政区划代码或国别代码")
	@ApiModelProperty("收货地点的国家行政区划代码或国别代码")
	private String countrySubdivisionCode;

	/** 省编码 */
	@Excel(name = "省编码")
	@ApiModelProperty("省编码")
	private String provinceCode;

	/** 市区编码 */
	@Excel(name = "市区编码")
	@ApiModelProperty("市区编码")
	private String cityCode;

	/** 区编码 */
	@Excel(name = "区编码")
	@ApiModelProperty("区编码")
	private String areaCode;

	/** 区 */
	@Excel(name = "区")
	@ApiModelProperty("区")
	private String area;

}
