package com.zly.project.scheduler.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 货主信息响应VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@ApiModel("货主信息响应VO对象")
@Data
public class CustomerInfoRespVo {
    
    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 货主名称 */
    @ApiModelProperty(value = "货主名称")
    private String customerName;

    /** 统一社会信用代码 */
    @ApiModelProperty(value = "统一社会信用代码")
    private String socialCreditCode;

    /** 企业联系人 */
    @ApiModelProperty(value = "企业联系人")
    private String contactPerson;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 运输线路 */
    @ApiModelProperty(value = "运输线路")
    private String transportRoutes;

    /** 调度员 */
    @ApiModelProperty(value = "调度员")
    private Long dispatcher;

    @ApiModelProperty(value = "调度员姓名")
    private String dispatcherName;

    /** 合作状态（1-在合作中，2-待合作，3-未开始，4-已结束，5-即将到期） */
    @ApiModelProperty(value = "合作状态（1-在合作中，2-待合作，3-未开始，4-已结束，5-即将到期）")
    private String cooperationStatus;
    
    /** 合作状态描述 */
    @ApiModelProperty(value = "合作状态描述")
    private String cooperationStatusDesc;
    
    /** 到期剩余天数 */
    @ApiModelProperty(value = "到期剩余天数")
    private Long remainingDays;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    /** 更新人 */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 合作开始时间 */
    @ApiModelProperty(value = "合作开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startDate;

    /** 合作结束时间 */
    @ApiModelProperty(value = "合作结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endDate;
    
    /** 承运合同列表 */
    @ApiModelProperty(value = "承运合同列表")
    private List<CustomerContractRespVo> contracts;
} 