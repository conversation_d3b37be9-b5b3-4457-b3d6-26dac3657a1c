package com.zly.project.scheduler.controller.vo;

import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.page.PageDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 货主信息请求VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("货主信息请求VO对象")
@Data
public class CustomerInfoPageReqVo extends PageDomain {

    /** 货主名称 */
    @ApiModelProperty(value = "货主名称")
    private String customerName;

    @ApiModelProperty(notes = "审核状态",value = "审核状态 1:审核中,2:审核成功(待开户),3:审核驳回,4:已开户",allowableValues = "1:审核中,2:审核成功(待开户),3:审核驳回,4:已开户")
    private Integer approveState;

    @ApiModelProperty(value = "合同状态（-1 未上传  1：正常已上报   2：已过期）")
    private Integer customerForwarderContractState;

    /** 状态（0：有效，1：无效 -1：删除） */
    @Excel(name = "状态", readConverterExp = "0=：有效，1：无效,-=1：删除")
    @ApiModelProperty(notes = "状态", value = "状态（0：有效，1：无效 -1：删除）",allowableValues = "0:有效,1:无效, -1:删除")
    private Integer state;

} 