package com.zly.project.scheduler.service.impl;

import com.zly.common.utils.SecurityUtils;
import com.zly.project.scheduler.controller.vo.CustomerContractEditVo;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import com.zly.project.scheduler.domain.CustomerContract;
import com.zly.project.scheduler.domain.CustomerInfo;
import com.zly.project.scheduler.mapper.CustomerContractMapper;
import com.zly.project.scheduler.mapper.CustomerInfoMapper;
import com.zly.project.scheduler.service.ICustomerContractService;
import com.zly.project.scheduler.service.ICustomerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.BASE_SERVER_ERROR;

/**
 * 货主合约服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Service
public class CustomerContractServiceImpl implements ICustomerContractService {

    @Resource
    private ICustomerInfoService carrierInfoService;

    @Resource
    private CustomerContractMapper customerContractMapper;

    @Resource
    private CustomerInfoMapper customerInfoMapper;
    
    /**
     * 更新合约状态
     * 
     * @param contract 合约信息
     * @param newStatus 新的合约状态
     * @return 结果
     */
    @Override
    public int updateContractStatus(CustomerContract contract, String newStatus) {
        if (contract == null || newStatus == null) {
            return 0;
        }
        
        // 检查状态是否需要更新
        if (!newStatus.equals(contract.getContractStatus())) {
            // 记录旧状态用于日志
            String oldStatus = contract.getContractStatus();
            
            // 创建新对象进行更新，避免直接修改传入参数
            CustomerContract updateContract = new CustomerContract();
            updateContract.setId(contract.getId());
            updateContract.setContractStatus(newStatus);
            // 更新状态
            try {
                String username = SecurityUtils.getUsername();
                updateContract.setUpdateBy(username);
            }catch (Exception e){
                log.info("updateContractStatus,获取更新人失败，定时器执行,e->{}",e.getMessage());
                updateContract.setUpdateBy(String.valueOf(0));
            }
            updateContract.setUpdateTime(new Date());
            
            // 执行更新
            int rows = customerContractMapper.updateCustomerContract(updateContract);
            
            // 更新原对象的状态，使其保持一致
            if (rows > 0) {
                contract.setContractStatus(newStatus);
                log.info("更新货主合同[{}]状态: {} -> {}", contract.getId(), oldStatus, newStatus);
            }
            
            return rows;
        }
        
        return 0;
    }

    /**
     * 编辑货主合约
     *
     * @param contractEditVo 合约编辑信息
     * @return 结果
     */
    @Override
    @Transactional
    public int editCustomerContract(CustomerContractEditVo contractEditVo) {
        // 参数校验
        if (contractEditVo == null || contractEditVo.getId() == null || 
            contractEditVo.getCustomerId() == null ||
            contractEditVo.getStartDate() == null || 
            contractEditVo.getEndDate() == null) {
            throw exception(BASE_SERVER_ERROR, "编辑参数不完整，请检查");
        }

        CustomerInfoRespVo customerInfoRespVo = carrierInfoService.selectCustomerInfoById(contractEditVo.getCustomerId());
        if (customerInfoRespVo == null) {
            log.error("货主[{}]信息不存在，无法更新合作状态", contractEditVo.getCustomerId());
            throw exception(BASE_SERVER_ERROR, "货主信息不存在，无法更新合作状态");
        }
        String cooperationStatus = customerInfoRespVo.getCooperationStatus();
        if ("4".equals(cooperationStatus)){
            throw exception(BASE_SERVER_ERROR, "货主合作状态已结束，无法更新合约");
        }
        // 校验结束日期不早于开始日期
        if (contractEditVo.getEndDate().before(contractEditVo.getStartDate())) {
            throw exception(BASE_SERVER_ERROR, "合约结束日期不能早于开始日期");
        }
        
        // 查询原合约信息
        CustomerContract existingContract = customerContractMapper.selectCustomerContractById(contractEditVo.getId());
        if (existingContract == null) {
            throw exception(BASE_SERVER_ERROR, "合约信息不存在");
        }
        
        // 校验合约所属货主
        if (!existingContract.getCustomerId().equals(contractEditVo.getCustomerId())) {
            throw exception(BASE_SERVER_ERROR, "合约不属于指定货主");
        }
        
        // 校验合约状态是否允许编辑（只有待生效和生效中的合约可以编辑）
        String contractStatus = existingContract.getContractStatus();
        if (!"1".equals(contractStatus) && !"2".equals(contractStatus)) {
            throw exception(BASE_SERVER_ERROR, "当前合约状态不允许编辑，只有待生效或生效中状态才能执行此操作");
        }
        
        // 检查新的合约时间是否与其他合约重叠
        if (checkContractTimeOverlap(
                contractEditVo.getCustomerId(),
                contractEditVo.getId(),
                contractEditVo.getStartDate(), 
                contractEditVo.getEndDate())) {
            throw exception(BASE_SERVER_ERROR, "新合约时间与已有合约时间重叠，请调整");
        }
        
        // 更新合约信息
        CustomerContract updateContract = new CustomerContract();
        updateContract.setId(contractEditVo.getId());
        updateContract.setStartDate(contractEditVo.getStartDate());
        updateContract.setEndDate(contractEditVo.getEndDate());
        
        // 如果有附件路径，也一并更新
        if (contractEditVo.getAttachmentPath() != null && !contractEditVo.getAttachmentPath().isEmpty()) {
            updateContract.setAttachmentPath(contractEditVo.getAttachmentPath());
        }
        
        // 重新计算合约状态
        String newStatus = carrierInfoService.determineContractStatus(contractEditVo.getStartDate(), contractEditVo.getEndDate());
        updateContract.setContractStatus(newStatus);
        
        // 设置更新人和更新时间
        updateContract.setUpdateBy(SecurityUtils.getUsername());
        updateContract.setUpdateTime(new Date());
        
        // 执行更新
        int result = customerContractMapper.updateCustomerContract(updateContract);
        
        // 更新货主的合作状态
        if (result > 0) {
            // 调用货主合作状态更新
            cooperationStatus = carrierInfoService.determineCooperationStatus(contractEditVo.getStartDate(), contractEditVo.getEndDate());
            updateCarrierCooperationStatus(contractEditVo.getCustomerId(),cooperationStatus);
            log.info("货主合约[{}]编辑成功，新状态：{}", contractEditVo.getId(), newStatus);
        }
        
        return result;
    }
    
    /**
     * 检查合约时间是否重叠
     *
     * @param customerId 货主ID
     * @param contractId 当前合约ID（排除自身）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return true-重叠 false-不重叠
     */
    @Override
    public boolean checkContractTimeOverlap(Long customerId, Long contractId, Date startDate, Date endDate) {
        // 调整开始日期和结束日期
        Date adjustedStartDate = CustomerInfoServiceImpl.getStartDate(startDate);
        Date adjustedEndDate = CustomerInfoServiceImpl.getEndDate(endDate);
        
        // 获取货主所有合约
        CustomerContract queryContract = new CustomerContract();
        queryContract.setCustomerId(customerId);
        List<CustomerContract> contracts = customerContractMapper.selectCustomerContractList(queryContract);
        
        if (contracts != null && !contracts.isEmpty()) {
            for (CustomerContract contract : contracts) {
                // 排除自身
                if (contract.getId().equals(contractId)) {
                    continue;
                }
                
                Date existingStartDate = CustomerInfoServiceImpl.getStartDate(contract.getStartDate());
                Date existingEndDate = CustomerInfoServiceImpl.getEndDate(contract.getEndDate());
                
                // 检查时间重叠
                // 新合约开始日期在已有合约的时间范围内
                boolean startOverlap = !adjustedStartDate.before(existingStartDate) && !adjustedStartDate.after(existingEndDate);
                // 新合约结束日期在已有合约的时间范围内
                boolean endOverlap = !adjustedEndDate.before(existingStartDate) && !adjustedEndDate.after(existingEndDate);
                // 新合约完全包含已有合约
                boolean containsExisting = adjustedStartDate.before(existingStartDate) && adjustedEndDate.after(existingEndDate);
                
                if (startOverlap || endOverlap || containsExisting) {
                    return true; // 存在重叠
                }
            }
        }
        
        return false; // 不存在重叠
    }
    
    /**
     * 更新货主合作状态
     *
     * @param customerId 货主ID
     * @param cooperationStatus 合作状态
     */
    private void updateCarrierCooperationStatus(Long customerId,String cooperationStatus) {
        // 查询货主信息
        CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(customerId);
        if (customerInfo == null) {
            log.error("货主[{}]信息不存在，无法更新合作状态", customerId);
            return;
        }
        // 如果状态有变化，更新货主合作状态
        if (!cooperationStatus.equals(customerInfo.getCooperationStatus())) {
            CustomerInfo updateInfo = new CustomerInfo();
            updateInfo.setId(customerId);
            updateInfo.setCooperationStatus(cooperationStatus);
            updateInfo.setUpdateBy(SecurityUtils.getUsername());
            updateInfo.setUpdateTime(new Date());
            
            customerInfoMapper.updateCustomerInfo(updateInfo);
            log.info("货主[{}]合作状态更新: {} -> {}", customerId, customerInfo.getCooperationStatus(), cooperationStatus);
        }
    }

} 