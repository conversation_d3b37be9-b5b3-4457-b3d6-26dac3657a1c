package com.zly.project.scheduler.mapper;

import java.util.List;

import com.zly.project.scheduler.controller.vo.CustomerInfoPageReqVo;
import com.zly.project.scheduler.domain.CustomerInfo;

/**
 * 货主信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface CustomerInfoMapper
{
    /**
     * 查询货主信息
     * 
     * @param id 货主信息主键
     * @return 货主信息
     */
    public CustomerInfo selectCustomerInfoById(Long id);

    /**
     * 查询货主信息列表
     * 
     * @param customerInfo 货主信息
     * @return 货主信息集合
     */
    public List<CustomerInfo> selectCustomerInfoList(CustomerInfoPageReqVo customerInfo);

    /**
     * 新增货主信息
     * 
     * @param customerInfo 货主信息
     * @return 结果
     */
    public int insertCustomerInfo(CustomerInfo customerInfo);

    /**
     * 修改货主信息
     * 
     * @param customerInfo 货主信息
     * @return 结果
     */
    public int updateCustomerInfo(CustomerInfo customerInfo);

    /**
     * 删除货主信息
     * 
     * @param id 货主信息主键
     * @return 结果
     */
    public int deleteCustomerInfoById(Long id);

    /**
     * 批量删除货主信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerInfoByIds(Long[] ids);
    
    /**
     * 通过统一社会信用代码查询货主信息
     *
     * @param socialCreditCode 统一社会信用代码
     * @return 货主信息
     */
    public CustomerInfo selectCustomerInfoBySocialCreditCode(String socialCreditCode);

    /**
     * 查询非指定状态的货主信息列表
     * 
     * @param customerInfo 货主信息（包含要排除的状态）
     * @return 货主信息集合
     */
    public List<CustomerInfo> selectCustomerInfoListExcludeStatus(CustomerInfo customerInfo);


    /**
     * 根据合作状态列表查询货主信息
     *
     * @param statusList 合作状态列表
     * @return 货主信息集合
     */
    List<CustomerInfo> selectCustomerInfoByStatusList(List<String> statusList);
}