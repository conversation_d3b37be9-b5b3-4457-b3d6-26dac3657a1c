package com.zly.project.scheduler.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.ClientLog;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户服务管理对象 customer_service_info
 *
 * <AUTHOR>
 * @date 2022-08-01
 */
@Data
public class ContractServiceInfo extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**  */
	@ApiModelProperty(value = "关联表id", notes = "关联表id")
	private Long id;

	/** 项目合约id */
	@Excel(name = "项目合约id")
	@ApiModelProperty(value = "项目合约id", notes = "项目合约id")
	private Long contractId;

	/** 项目合约名称 */
	@Excel(name = "项目合约名称")
	@ApiModelProperty(value = "项目合约名称", notes = "项目合约名称")
	private String contractName;

	/** 开通状态（0 未开通 1 开通） */
	@ApiModelProperty(notes ="开通状态",value = "开通状态 -1 已删除 0 未开通  1 开通", allowableValues = "-1:已删除,0:未开通,1:开通")
	@Excel(name = "开通状态", readConverterExp = "0=开通,1=,开=通")
	private Integer openState;

	/** 服务类型 */
	@Excel(name = "服务id")
	@ApiModelProperty(value = "服务id", notes = "服务id")
	private Long serviceId;

	@ApiModelProperty(value = "审批人", notes = "审批人")
	private String auditingBy;

	@ApiModelProperty(value = "审批通过时间", notes = "审批通过时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date auditingTime;

	@ApiModelProperty(value = "保险费率", notes = "保险费率")
	private BigDecimal rate;

	@Excel(name = "服务名称")
	@ApiModelProperty(value = "服务名称", notes = "服务名称")
	@ClientLog(isKeyWord = true,hidden = false)
	private String serviceName;

}
