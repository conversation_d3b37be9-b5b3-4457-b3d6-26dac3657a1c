package com.zly.project.scheduler.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 远程客户信息响应VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("远程客户信息响应VO")
public class RemoteCustomerInfoVo {

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("客户ID")
    private String id;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("联系人")
    private String contact;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("账户号")
    private String accountNo;

    @ApiModelProperty("银行代码")
    private String bankCode;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("状态")
    private Integer state;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("合同状态")
    private Integer contractState;

    @ApiModelProperty("发票抬头")
    private String billHead;

    @ApiModelProperty("税号")
    private String taxNo;

    @ApiModelProperty("单位地址")
    private String unitAddress;

    @ApiModelProperty("电话")
    private String telephone;

    @ApiModelProperty("银行")
    private String bank;

    @ApiModelProperty("银行账户")
    private String bankAccount;

    @ApiModelProperty("审批状态")
    private Integer approveState;

    @ApiModelProperty("资源类型")
    private Integer resourceType;

    @ApiModelProperty("集团类型")
    private Integer groupType;

    @ApiModelProperty("货运代理人ID")
    private String freightForwarderId;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("是否平台")
    private Integer isPlatform;

    @ApiModelProperty("客户货运代理合同状态")
    private Integer customerForwarderContractState;
}
