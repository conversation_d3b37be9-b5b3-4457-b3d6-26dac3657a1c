package com.zly.project.scheduler.api.vo;

import com.zly.framework.web.page.QueryCommon;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 网货端项目查询请求对象 framework_contract
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Data
public class ContractSrarchReq extends QueryCommon {

	@ApiModelProperty("自己公司的托运人ID")
	private Long selfCustomerId;

	@ApiModelProperty("上游公司ID")
	private Long customerId;

	@ApiModelProperty("上游公司名称")
	private String customerName;

	@ApiModelProperty("项目编号")
	private String contractCode;

	@ApiModelProperty("项目名称")
	private String contractName;

	@ApiModelProperty("签约状态")
	private Integer approveState;

	@ApiModelProperty("项目合约状态（0.未开始 1.合作中 2.已结束(合约过期、关闭/禁用项目、从转包链删除，都是已结束)")
	private Integer state;

	@ApiModelProperty("是否税务上报项目 0是 1不是")
	private Integer isTaxUploaded;

	@ApiModelProperty("网货id")
	private Long freightForwarderId;

	@ApiModelProperty("是否只查询网络货运模式的项目，默认false")
	private Boolean isWhm = false;

}
