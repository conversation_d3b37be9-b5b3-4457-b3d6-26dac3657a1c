package com.zly.project.scheduler.mapper;

import java.util.List;
import com.zly.project.scheduler.domain.SchedulerCallPush;

/**
 * 话单推送记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface SchedulerCallPushMapper 
{
    /**
     * 查询话单推送记录
     * 
     * @param id 话单推送记录主键
     * @return 话单推送记录
     */
    public SchedulerCallPush selectSchedulerCallPushById(Long id);

    /**
     * 查询话单推送记录列表
     * 
     * @param schedulerCallPush 话单推送记录
     * @return 话单推送记录集合
     */
    public List<SchedulerCallPush> selectSchedulerCallPushList(SchedulerCallPush schedulerCallPush);

    /**
     * 新增话单推送记录
     * 
     * @param schedulerCallPush 话单推送记录
     * @return 结果
     */
    public int insertSchedulerCallPush(SchedulerCallPush schedulerCallPush);

    /**
     * 修改话单推送记录
     * 
     * @param schedulerCallPush 话单推送记录
     * @return 结果
     */
    public int updateSchedulerCallPush(SchedulerCallPush schedulerCallPush);

    /**
     * 删除话单推送记录
     * 
     * @param id 话单推送记录主键
     * @return 结果
     */
    public int deleteSchedulerCallPushById(Long id);

    /**
     * 批量删除话单推送记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchedulerCallPushByIds(Long[] ids);
}
