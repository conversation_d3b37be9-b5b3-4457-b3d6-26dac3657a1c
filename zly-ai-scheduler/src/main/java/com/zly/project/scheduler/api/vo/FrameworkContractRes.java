package com.zly.project.scheduler.api.vo;

import com.zly.project.scheduler.domain.ContractServiceInfo;
import com.zly.project.scheduler.domain.FrameworkContract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目合约对象 framework_contract
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Data
public class FrameworkContractRes extends FrameworkContract {

	@ApiModelProperty(value = "项目合约的签约主体托运人名称", notes = "项目合约的签约主体托运人名称")
	private String customerName;

	@ApiModelProperty(value = "项目合约的垫资人名称", notes = "项目合约的垫资人名称")
	private String prepayName;

	@ApiModelProperty(value = "项目合约的网络货运人名称", notes = "项目合约的网络货运人名称")
	private String freightForwarderName;

	@ApiModelProperty(value = "项目关联的增值服务", notes = "项目关联的增值服务")
	private List<ContractServiceInfo> serviceList;

	@ApiModelProperty(value = "上游公司ID", notes = "转包关系中，上游公司的ID")
	private Long upstreamCustomerId;

	@ApiModelProperty(value = "上游公司名称", notes = "转包关系中，上游公司的名称")
	private String upstreamCustomerName;

	@ApiModelProperty(value = "上游费率", notes = "转包关系中，本级给上游设置的费率")
	private BigDecimal upstreamRate;

	@ApiModelProperty(value = "下游费率", notes = "转包关系中，下游给本级设置的费率")
	private BigDecimal downstreamRate;

	@ApiModelProperty(value = "货主费率", notes = "货主费率")
	private BigDecimal customerRate;

	@ApiModelProperty(value = "保险费率", notes = "保险费率")
	private BigDecimal insuranceRate;

	@ApiModelProperty(value = "项目类型", notes = "1：托运人创建项目 2：集团转包项目 3：网络货运人自己的项目")
	private Integer contractType;

	/**
	 * 转包链项目状态
	 */
	private Integer subcontractState;

	@ApiModelProperty(value = "客户id")
	private Long customerId;

	@ApiModelProperty(value = "当前托运人是否为垫资人 0 是 1 不是", notes = "当前托运人是否为垫资人 0 是 1 不是")
	private Integer isPrepayer;

	@ApiModelProperty(value = "客户合约状态", hidden = true)
	private Integer contractStatus;

	/**
	 * 托运人保险是否开通 0未开通 1开通
	 */
	@ApiModelProperty(value = "托运人保险是否开通 0未开通 1开通")
	private Integer tenantIsInsurance;

	@ApiModelProperty(value = "业务模式名称", notes = "业务模式名称")
	private String businessModelName;

	@ApiModelProperty("是否可修改网络货运人 0 是 1 否")
	private Integer canChangeForwarder;

	@ApiModelProperty("转包链中状态 0 正常 1 已删除")
	private Integer subchainState;

	@ApiModelProperty("是否可修改费率 0 是 1 否")
	private Integer canChangeRate;

	@ApiModelProperty("系统类型 0:公里 1水运")
	private Integer systemType;
}
