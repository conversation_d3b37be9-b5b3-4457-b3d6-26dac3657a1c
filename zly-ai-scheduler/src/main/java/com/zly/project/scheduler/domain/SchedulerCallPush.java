package com.zly.project.scheduler.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;

/**
 * 话单推送记录对象 scheduler_call_push
 * 
 * <AUTHOR>
 * @date 2025-03-25
 */
public class SchedulerCallPush extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Excel(name = "主键ID")
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private String userId;

    /** 软电话拨打的一个唯一id */
    @Excel(name = "软电话拨打的一个唯一id")
    private String callid;

    /** 话术id */
    @Excel(name = "话术id")
    private Integer groupId;

    /** 话术组名称 */
    @Excel(name = "话术组名称")
    private String groupName;

    /** 外呼任务id */
    @Excel(name = "外呼任务id")
    private String taskId;

    /** 外呼任务名称 */
    @Excel(name = "外呼任务名称")
    private String taskName;

    /** 客户公海id */
    @Excel(name = "客户公海id")
    private String customerId;

    /** 呼叫号码 */
    @Excel(name = "呼叫号码")
    private String telephone;

    /** 省 */
    @Excel(name = "省")
    private String province;

    /** 市 */
    @Excel(name = "市")
    private String city;

    /** 号码运营商 */
    @Excel(name = "号码运营商")
    private String operator;

    /** 话单状态(0:失败 1:成功) */
    @Excel(name = "话单状态(0:失败 1:成功)")
    private Integer status;

    /** 如果话单失败,记录失败原因 */
    @Excel(name = "如果话单失败,记录失败原因")
    private String statusStr;

    /** 呼叫时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "呼叫时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date calldate;

    /** 应答时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "应答时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date answerdate;

    /** 挂断时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "挂断时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date hangupdate;

    /** 拨打时长(秒) */
    @Excel(name = "拨打时长(秒)")
    private Integer duration;

    /** 通话时长(秒) */
    @Excel(name = "通话时长(秒)")
    private Integer bill;

    /** 意向标签等级(1A 2B 3C 4D 5E 6F) */
    @Excel(name = "意向标签等级(1A 2B 3C 4D 5E 6F)")
    private Integer intentionResults;

    /** 交互次数 */
    @Excel(name = "交互次数")
    private Integer rounds;

    /** 通话评分 */
    @Excel(name = "通话评分")
    private Integer score;

    /** 评分id */
    @Excel(name = "评分id")
    private String levelId;

    /** 评分名称 */
    @Excel(name = "评分名称")
    private String levelName;

    /** 通话记录机器人标签名 */
    @Excel(name = "通话记录机器人标签名")
    private String tags;

    /** 通话录音地址 */
    @Excel(name = "通话录音地址")
    private String voiceUrl;

    /** 通话录音内容 */
    @Excel(name = "通话录音内容")
    private String voiceText;

    /** 我方系统业务标识 */
    @Excel(name = "我方系统业务标识")
    private String extra;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }

    public void setCallid(String callid) 
    {
        this.callid = callid;
    }

    public String getCallid() 
    {
        return callid;
    }

    public void setGroupId(Integer groupId) 
    {
        this.groupId = groupId;
    }

    public Integer getGroupId() 
    {
        return groupId;
    }

    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    public String getGroupName() 
    {
        return groupName;
    }

    public void setTaskId(String taskId) 
    {
        this.taskId = taskId;
    }

    public String getTaskId() 
    {
        return taskId;
    }

    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }

    public void setCustomerId(String customerId) 
    {
        this.customerId = customerId;
    }

    public String getCustomerId() 
    {
        return customerId;
    }

    public void setTelephone(String telephone) 
    {
        this.telephone = telephone;
    }

    public String getTelephone() 
    {
        return telephone;
    }

    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }

    public void setOperator(String operator) 
    {
        this.operator = operator;
    }

    public String getOperator() 
    {
        return operator;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setStatusStr(String statusStr) 
    {
        this.statusStr = statusStr;
    }

    public String getStatusStr() 
    {
        return statusStr;
    }

    public void setCalldate(Date calldate) 
    {
        this.calldate = calldate;
    }

    public Date getCalldate() 
    {
        return calldate;
    }

    public void setAnswerdate(Date answerdate) 
    {
        this.answerdate = answerdate;
    }

    public Date getAnswerdate() 
    {
        return answerdate;
    }

    public void setHangupdate(Date hangupdate) 
    {
        this.hangupdate = hangupdate;
    }

    public Date getHangupdate() 
    {
        return hangupdate;
    }

    public void setDuration(Integer duration) 
    {
        this.duration = duration;
    }

    public Integer getDuration() 
    {
        return duration;
    }

    public void setBill(Integer bill) 
    {
        this.bill = bill;
    }

    public Integer getBill() 
    {
        return bill;
    }

    public void setIntentionResults(Integer intentionResults) 
    {
        this.intentionResults = intentionResults;
    }

    public Integer getIntentionResults() 
    {
        return intentionResults;
    }

    public void setRounds(Integer rounds) 
    {
        this.rounds = rounds;
    }

    public Integer getRounds() 
    {
        return rounds;
    }

    public void setScore(Integer score) 
    {
        this.score = score;
    }

    public Integer getScore() 
    {
        return score;
    }

    public void setLevelId(String levelId) 
    {
        this.levelId = levelId;
    }

    public String getLevelId() 
    {
        return levelId;
    }

    public void setLevelName(String levelName) 
    {
        this.levelName = levelName;
    }

    public String getLevelName() 
    {
        return levelName;
    }

    public void setTags(String tags) 
    {
        this.tags = tags;
    }

    public String getTags() 
    {
        return tags;
    }

    public void setVoiceUrl(String voiceUrl) 
    {
        this.voiceUrl = voiceUrl;
    }

    public String getVoiceUrl() 
    {
        return voiceUrl;
    }

    public void setVoiceText(String voiceText) 
    {
        this.voiceText = voiceText;
    }

    public String getVoiceText() 
    {
        return voiceText;
    }

    public void setExtra(String extra) 
    {
        this.extra = extra;
    }

    public String getExtra() 
    {
        return extra;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("callid", getCallid())
            .append("groupId", getGroupId())
            .append("groupName", getGroupName())
            .append("taskId", getTaskId())
            .append("taskName", getTaskName())
            .append("customerId", getCustomerId())
            .append("telephone", getTelephone())
            .append("province", getProvince())
            .append("city", getCity())
            .append("operator", getOperator())
            .append("status", getStatus())
            .append("statusStr", getStatusStr())
            .append("calldate", getCalldate())
            .append("answerdate", getAnswerdate())
            .append("hangupdate", getHangupdate())
            .append("duration", getDuration())
            .append("bill", getBill())
            .append("intentionResults", getIntentionResults())
            .append("rounds", getRounds())
            .append("score", getScore())
            .append("levelId", getLevelId())
            .append("levelName", getLevelName())
            .append("tags", getTags())
            .append("voiceUrl", getVoiceUrl())
            .append("voiceText", getVoiceText())
            .append("extra", getExtra())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
