package com.zly.project.scheduler.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 货主合作结束请求VO
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
public class CustomerCooperationEndVo {
    
    /**
     * 货主ID
     */
    @NotNull(message = "货主ID不能为空")
    @ApiModelProperty(value = "货主ID")
    private Long customerId;
    
    /**
     * 结束原因
     */
    @ApiModelProperty(value = "结束原因预留")
    private String endReason;
} 