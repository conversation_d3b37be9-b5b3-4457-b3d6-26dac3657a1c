package com.zly.project.scheduler.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 货主承运合同响应VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@ApiModel("货主承运合同响应VO对象")
@Data
public class CustomerContractRespVo {
    
    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 货主ID */
    @ApiModelProperty(value = "货主ID")
    private Long customerId;

    /** 合作开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "合作开始时间")
    private Date startDate;

    /** 合作结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "合作结束时间")
    private Date endDate;

    /** 合约状态（1-待生效，2-生效中，3-已结束） */
    @ApiModelProperty(value = "合约状态（1-待生效，2-生效中，3-已结束）")
    private String contractStatus;
    
    /** 合约状态描述 */
    @ApiModelProperty(value = "合约状态描述")
    private String contractStatusDesc;

    /** 合同附件路径 */
    @ApiModelProperty(value = "合同附件路径")
    private String attachmentPath;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;
} 