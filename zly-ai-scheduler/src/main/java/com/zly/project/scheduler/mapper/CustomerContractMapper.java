package com.zly.project.scheduler.mapper;

import java.util.List;
import com.zly.project.scheduler.domain.CustomerContract;

/**
 * 货主承运合同Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface CustomerContractMapper
{
    /**
     * 查询货主承运合同
     * 
     * @param id 货主承运合同主键
     * @return 货主承运合同
     */
    public CustomerContract selectCustomerContractById(Long id);

    /**
     * 查询货主承运合同列表
     * 
     * @param customerContract 货主承运合同
     * @return 货主承运合同集合
     */
    public List<CustomerContract> selectCustomerContractList(CustomerContract customerContract);

    /**
     * 查询货主的所有承运合同
     * 
     * @param customerId 货主ID
     * @return 货主承运合同集合
     */
    public List<CustomerContract> selectCustomerContractsByCustomerId(Long customerId);

    /**
     * 新增货主承运合同
     * 
     * @param customerContract 货主承运合同
     * @return 结果
     */
    public int insertCustomerContract(CustomerContract customerContract);

    /**
     * 修改货主承运合同
     * 
     * @param customerContract 货主承运合同
     * @return 结果
     */
    public int updateCustomerContract(CustomerContract customerContract);

    /**
     * 删除货主承运合同
     * 
     * @param id 货主承运合同主键
     * @return 结果
     */
    public int deleteCustomerContractById(Long id);

    /**
     * 批量删除货主承运合同
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerContractByIds(Long[] ids);

    /**
     * 根据多个货主ID查询所有合约
     *
     * @param customerIds 货主ID列表
     * @return 合约列表
     */
    public List<CustomerContract> selectCustomerContractsByCustomerIds(List<Long> customerIds);
} 