package com.zly.project.scheduler.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.project.scheduler.domain.SchedulerCallPush;
import com.zly.project.scheduler.service.ISchedulerCallPushService;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.web.page.TableDataInfo;

/**
 * 话单推送记录Controller
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
@RestController
@RequestMapping("/scheduler/push")
public class SchedulerCallPushController extends BaseController
{
    @Autowired
    private ISchedulerCallPushService schedulerCallPushService;

    /**
     * 查询话单推送记录列表
     */
    @PreAuthorize("@ss.hasPermi('scheduler:push:list')")
    @GetMapping("/list")
    public TableDataInfo list(SchedulerCallPush schedulerCallPush)
    {
        startPage();
        List<SchedulerCallPush> list = schedulerCallPushService.selectSchedulerCallPushList(schedulerCallPush);
        return getDataTable(list);
    }

    /**
     * 导出话单推送记录列表
     */
    @PreAuthorize("@ss.hasPermi('scheduler:push:export')")
    @Log(title = "话单推送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SchedulerCallPush schedulerCallPush)
    {
        List<SchedulerCallPush> list = schedulerCallPushService.selectSchedulerCallPushList(schedulerCallPush);
        ExcelUtil<SchedulerCallPush> util = new ExcelUtil<SchedulerCallPush>(SchedulerCallPush.class);
        util.exportExcel(response, list, "话单推送记录数据");
    }

    /**
     * 获取话单推送记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('scheduler:push:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(schedulerCallPushService.selectSchedulerCallPushById(id));
    }

    /**
     * 新增话单推送记录
     */
    @PreAuthorize("@ss.hasPermi('scheduler:push:add')")
    @Log(title = "话单推送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SchedulerCallPush schedulerCallPush)
    {
        return toAjax(schedulerCallPushService.insertSchedulerCallPush(schedulerCallPush));
    }

    /**
     * 修改话单推送记录
     */
    @PreAuthorize("@ss.hasPermi('scheduler:push:edit')")
    @Log(title = "话单推送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SchedulerCallPush schedulerCallPush)
    {
        return toAjax(schedulerCallPushService.updateSchedulerCallPush(schedulerCallPush));
    }

    /**
     * 删除话单推送记录
     */
    @PreAuthorize("@ss.hasPermi('scheduler:push:remove')")
    @Log(title = "话单推送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(schedulerCallPushService.deleteSchedulerCallPushByIds(ids));
    }
}
