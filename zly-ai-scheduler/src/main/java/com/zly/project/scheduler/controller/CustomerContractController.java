package com.zly.project.scheduler.controller;

import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.scheduler.controller.vo.CustomerContractEditVo;
import com.zly.project.scheduler.service.ICustomerContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 货主合约Controller
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Api(tags = "管理后台 - 货主合约Controller")
@RestController
@RequestMapping("/scheduler/customer/contract")
public class CustomerContractController extends BaseController {
    
    @Resource
    private ICustomerContractService carrierContractService;
    
    /**
     * 编辑货主合约
     */
    @ApiOperation("编辑货主合约")
    @PreAuthorize("@ss.hasPermi('scheduler:carrier:contract:edit')")
    @Log(title = "编辑货主合约", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult editContract(@Valid @RequestBody CustomerContractEditVo contractEditVo) {
        return toAjax(carrierContractService.editCustomerContract(contractEditVo));
    }
    
    /**
     * 检查合约时间是否重叠
     */
    @ApiOperation("检查合约时间是否重叠")
    @PreAuthorize("@ss.hasPermi('scheduler:carrier:contract:check')")
    @GetMapping("/checkOverlap")
    public AjaxResult checkTimeOverlap(
            @RequestParam("customerId") Long customerId,
            @RequestParam(value = "contractId", required = false) Long contractId,
            @RequestParam("startDate") Long startDate,
            @RequestParam("endDate") Long endDate) {
        
        java.util.Date startDateTime = new java.util.Date(startDate);
        java.util.Date endDateTime = new java.util.Date(endDate);
        
        boolean isOverlap = carrierContractService.checkContractTimeOverlap(
                customerId,
                contractId == null ? 0L : contractId,
                startDateTime, 
                endDateTime);
        
        return AjaxResult.success().put("overlap", isOverlap);
    }
} 