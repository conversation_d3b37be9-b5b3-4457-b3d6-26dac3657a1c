package com.zly.project.scheduler.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zly.project.scheduler.mapper.SchedulerCallPushMapper;
import com.zly.project.scheduler.domain.SchedulerCallPush;
import com.zly.project.scheduler.service.ISchedulerCallPushService;

/**
 * 话单推送记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
@Service
public class SchedulerCallPushServiceImpl implements ISchedulerCallPushService 
{
    @Autowired
    private SchedulerCallPushMapper schedulerCallPushMapper;

    /**
     * 查询话单推送记录
     * 
     * @param id 话单推送记录主键
     * @return 话单推送记录
     */
    @Override
    public SchedulerCallPush selectSchedulerCallPushById(Long id)
    {
        return schedulerCallPushMapper.selectSchedulerCallPushById(id);
    }

    /**
     * 查询话单推送记录列表
     * 
     * @param schedulerCallPush 话单推送记录
     * @return 话单推送记录
     */
    @Override
    public List<SchedulerCallPush> selectSchedulerCallPushList(SchedulerCallPush schedulerCallPush)
    {
        return schedulerCallPushMapper.selectSchedulerCallPushList(schedulerCallPush);
    }

    /**
     * 新增话单推送记录
     * 
     * @param schedulerCallPush 话单推送记录
     * @return 结果
     */
    @Override
    public int insertSchedulerCallPush(SchedulerCallPush schedulerCallPush)
    {
        return schedulerCallPushMapper.insertSchedulerCallPush(schedulerCallPush);
    }

    /**
     * 修改话单推送记录
     * 
     * @param schedulerCallPush 话单推送记录
     * @return 结果
     */
    @Override
    public int updateSchedulerCallPush(SchedulerCallPush schedulerCallPush)
    {
        return schedulerCallPushMapper.updateSchedulerCallPush(schedulerCallPush);
    }

    /**
     * 批量删除话单推送记录
     * 
     * @param ids 需要删除的话单推送记录主键
     * @return 结果
     */
    @Override
    public int deleteSchedulerCallPushByIds(Long[] ids)
    {
        return schedulerCallPushMapper.deleteSchedulerCallPushByIds(ids);
    }

    /**
     * 删除话单推送记录信息
     * 
     * @param id 话单推送记录主键
     * @return 结果
     */
    @Override
    public int deleteSchedulerCallPushById(Long id)
    {
        return schedulerCallPushMapper.deleteSchedulerCallPushById(id);
    }
}
