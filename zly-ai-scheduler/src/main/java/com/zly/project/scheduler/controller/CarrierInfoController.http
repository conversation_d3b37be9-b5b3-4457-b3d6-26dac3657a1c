### 获取货主列表
POST http://localhost:8080/scheduler/customer/list
Authorization: Bearer {{authorization}}
Content-Type: application/json
Accept: application/json

{}

### 添加货主信息
POST http://localhost:8080/scheduler/carrier
Authorization: Bearer {{authorization}}
Content-Type: application/json
Accept: application/json

{
  "carrierName": "测试货主",
  "contactPerson": "联系人",
  "contactPhone": "13800138000",
  "cooperationStatus": "1"
}

### 使用这个文件时，请在右上角选择环境(development或production)

### 添加每日货源
POST http://localhost:8080/cargo/daily
Authorization: Bearer {{authorization}}
Content-Type: application/json

{
  "customerId": 102,
  "cargoName": "钢材",
  "cargoType": "建材",
  "packagingMethod": "散装",
  "cargoQuantity": 20,
  "cargoUnit": "吨",
  "loadingProvinceCode": "110000",
  "loadingProvinceName": "北京市",
  "loadingCityCode": "110100",
  "loadingCityName": "北京市",
  "loadingDistrictCode": "110105",
  "loadingDistrictName": "朝阳区",
  "loadingAddress": "朝阳路88号",
  "unloadingProvinceCode": "310000",
  "unloadingProvinceName": "上海市",
  "unloadingCityCode": "310100",
  "unloadingCityName": "上海市",
  "unloadingDistrictCode": "310115",
  "unloadingDistrictName": "浦东新区",
  "unloadingAddress": "浦东大道100号",
  "loadingDateType": "1",
  "loadingTimeStart": "09:00",
  "loadingTimeEnd": "18:00",
  "vehicleType": "平板车",
  "vehicleLength": 10.0,
  "vehicleCount": 2,
  "specialRequirements": "需要防雨措施",
  "isFrequentSource": "0",
  "setFrequentSource": "0"
}

### 查询每日货源列表
GET http://localhost:8080/cargo/daily/list
Authorization: Bearer {{authorization}}
Content-Type: application/json

{
  "status": "0",
  "pageNum": 1,
  "pageSize": 10
}

### 获取每日货源详情
GET http://localhost:8080/cargo/daily/1
Authorization: Bearer {{authorization}}
Content-Type: application/json

### 更新每日货源状态（上架/下架）
PUT http://localhost:8080/cargo/daily/status
Authorization: Bearer {{authorization}}
Content-Type: application/json

{
  "id": 1,
  "status": "0"
}

### 更新每日货源信息
PUT http://localhost:8080/cargo/daily
Authorization: Bearer {{authorization}}
Content-Type: application/json

{
  "id": 1,
  "customerId": 1,
  "cargoName": "钢材（更新）",
  "cargoType": "建材",
  "packagingMethod": "散装",
  "cargoQuantity": 25,
  "cargoUnit": "吨",
  "loadingProvinceCode": "110000",
  "loadingProvinceName": "北京市",
  "loadingCityCode": "110100",
  "loadingCityName": "北京市",
  "loadingDistrictCode": "110105",
  "loadingDistrictName": "朝阳区",
  "loadingAddress": "朝阳路88号",
  "unloadingProvinceCode": "310000",
  "unloadingProvinceName": "上海市",
  "unloadingCityCode": "310100",
  "unloadingCityName": "上海市",
  "unloadingDistrictCode": "310115",
  "unloadingDistrictName": "浦东新区",
  "unloadingAddress": "浦东大道100号",
  "loadingDateType": "1",
  "loadingTimeStart": "08:00",
  "loadingTimeEnd": "17:00",
  "vehicleType": "平板车",
  "vehicleCount": 3,
  "specialRequirements": "需要防雨措施，小心轻放",
  "isFrequentSource": "0",
  "setFrequentSource": "0"
}

### 上传承运司机信息
POST http://localhost:8080/cargo/daily/carrier-driver
Authorization: Bearer {{authorization}}
Content-Type: application/json

{
  "cargoId": 1,
  "driverName": "张三",
  "idCard": "110101199001011234",
  "phoneNumber": "13900001234",
  "plateNumber": "京A12345",
  "plateColor": "1"
}

### 取消承运司机合作
PUT http://localhost:8080/cargo/carrier-driver/cancel/1
Authorization: Bearer {{authorization}}
Content-Type: application/json

### 根据货源ID查询承运司机列表
GET http://localhost:8080/cargo/carrier-driver/list/cargo/1
Authorization: Bearer {{authorization}}
Content-Type: application/json

### 更新承运司机状态
PUT http://localhost:8080/cargo/carrier-driver/status
Authorization: Bearer {{authorization}}
Content-Type: application/json

{
  "id": 1,
  "carrierStatus": "1"
}


### 组装调度任务参数
GET http://localhost:8080/cargo/daily/2/schedule-task
Authorization: Bearer {{authorization}}
Content-Type: application/json


### 创建调度计划
POST http://localhost:8080/scheduler/task-plan/create
Authorization: Bearer {{authorization}}
Content-Type: application/json

{
  "startPoint": "上海市浦东新区",
  "endPoint": "北京市朝阳区",
  "cargoName": "钢铁",
  "vehicleType": "平板车",
  "vehicleLength": 19.7,
  "vehicleCount": 5,
  "initiator": "张三",
  "planStartTime": "2025-08-01 08:00:00",
  "planEndTime": "2025-08-02 18:00:00",
  "estimatedCost": 12000,
  "requirements": "车辆要带斗篷，司机要有运输经验",
  "dispatcherName": "李四",
  "dispatcherPhone": "13800138000",
  "communicationMethod": 2,
  "isManualPause": 1,
  "isAutoPause": 1,
  "sessionType": 1,
  "planContent": "常州到江苏南京的餐巾纸，需要 9 米 6 的平板车，明天上午 9 点装货，单车计划运价 2000 元",
  "dailyCargoId": 1
}

