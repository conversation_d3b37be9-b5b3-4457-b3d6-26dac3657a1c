package com.zly.project.scheduler.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 货主合约编辑请求VO
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
public class CustomerContractEditVo {
    
    /**
     * 合约ID
     */
    @ApiModelProperty(value = "合约ID")
    @NotNull(message = "合约ID不能为空")
    private Long id;
    
    /**
     * 货主ID
     */
    @ApiModelProperty(value = "货主ID")
    @NotNull(message = "货主ID不能为空")
    private Long customerId;
    
    /**
     * 合约开始日期
     */
    @ApiModelProperty(value = "合约开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "合约开始日期不能为空")
    private Date startDate;
    
    /**
     * 合约结束日期
     */
    @ApiModelProperty(value = "合约结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "合约结束日期不能为空")
    private Date endDate;
    
    /**
     * 合同附件路径
     */
    @ApiModelProperty(value = "合同附件路径")
    private String attachmentPath;
} 