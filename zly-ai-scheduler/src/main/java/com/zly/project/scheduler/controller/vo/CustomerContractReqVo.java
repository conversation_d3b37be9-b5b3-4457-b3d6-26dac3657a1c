package com.zly.project.scheduler.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @ClassName: CarrierContractReqVo
 * @Description:
 * @Author: cdh
 * @Date: 2025/6/27 12:54
 **/
@Data
public class CustomerContractReqVo {

	@ApiModelProperty(value = "货主ID")
	private Long customerId;

	/** 合作开始时间 */
	@ApiModelProperty(value = "合作开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@NotNull(message = "合作开始时间不能为空")
	private Date startDate;

	/** 合作结束时间 */
	@ApiModelProperty(value = "合作结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@NotNull(message = "合作结束时间不能为空")
	private Date endDate;


	/** 合约状态（1-待生效，2-生效中，3-已结束） */
	@ApiModelProperty(value = " 合约状态（1-待生效，2-生效中，3-已结束）")
	private String contractStatus;

	/** 合同附件路径 */
	@ApiModelProperty(value = "合同附件路径")
	private String attachmentPath;


}
