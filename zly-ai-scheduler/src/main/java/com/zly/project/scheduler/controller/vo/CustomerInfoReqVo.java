package com.zly.project.scheduler.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 货主信息请求VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@ApiModel("货主信息请求VO对象")
@Data
public class CustomerInfoReqVo {

    private Long id;

    /** 货主名称 */
    @NotBlank(message = "货主名称不能为空")
    @ApiModelProperty(value = "货主名称")
    private String customerName;

    /** 统一社会信用代码 */
    @NotBlank(message = "统一社会信用代码不能为空")
    @ApiModelProperty(value = "统一社会信用代码")
    private String socialCreditCode;

    /** 企业联系人 */
    @NotBlank(message = "企业联系人不能为空")
    @ApiModelProperty(value = "企业联系人")
    private String contactPerson;

    /** 联系电话 */
    @NotBlank(message = "联系电话不能为空")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 运输线路 */
    @ApiModelProperty(value = "运输线路")
    private String transportRoutes;

    /** 调度员 */
    @ApiModelProperty(value = "调度员")
    private Long dispatcher;

    /** 合作状态 */
    @ApiModelProperty(value = "合作状态")
    private String cooperationStatus;

    @ApiModelProperty(value = "项目合约信息")
    private CustomerContractReqVo customerContractReqVo;
} 