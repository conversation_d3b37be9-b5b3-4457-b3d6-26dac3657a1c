package com.zly.project.scheduler.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.qiniu.http.Response;
import com.zly.common.utils.QiNiuUtil;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.TextUtil;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.framework.config.CustomConfig;
import com.zly.project.plan.controller.vo.TaskPlanRespVO;
import com.zly.project.plan.convert.TaskPlanConvert;
import com.zly.project.plan.domain.TaskPlanDO;
import com.zly.project.scheduler.controller.vo.*;
import com.zly.project.scheduler.domain.CustomerContract;
import com.zly.project.scheduler.domain.CustomerInfo;
import com.zly.project.scheduler.mapper.CustomerContractMapper;
import com.zly.project.scheduler.mapper.CustomerInfoMapper;
import com.zly.project.scheduler.service.ICustomerContractService;
import com.zly.project.scheduler.service.ICustomerInfoService;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.common.utils.DateUtils.getDate;
import static com.zly.enums.ErrorCodeConstants.BASE_SERVER_ERROR;

/**
 * 货主信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Service
public class CustomerInfoServiceImpl implements ICustomerInfoService
{
    @Resource
    private CustomerInfoMapper customerInfoMapper;

    @Resource
    private CustomerContractMapper customerContractMapper;

    @Resource
    private ICustomerContractService carrierContractService;

    @Resource
    private CustomConfig customConfig;

    /**
     * 查询货主信息
     * 
     * @param id 货主信息主键
     * @return 货主信息响应对象
     */
    @Override
    public CustomerInfoRespVo selectCustomerInfoById(Long id)
    {
        // 查询货主信息
        CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(id);
        if (customerInfo == null) {
            throw exception(BASE_SERVER_ERROR, "货主信息不存在");
        }
        
        // 创建响应VO
        CustomerInfoRespVo respVo = new CustomerInfoRespVo();
        BeanUtil.copyProperties(customerInfo, respVo);
        Map<String,SysUser> sysUserListByCreateByList = sysUserService.getSysUserListByCreateByList(new ArrayList<String>(){{
            add(String.valueOf(customerInfo.getDispatcher()));
        }});
        SysUser sysUser = sysUserListByCreateByList.get(String.valueOf(customerInfo.getDispatcher()));
        if (sysUser !=null){
            respVo.setDispatcherName(sysUser.getNickName());
        }
        // 查询货主的承运合同
        CustomerContract queryContract = new CustomerContract();
        queryContract.setCustomerId(id);
        List<CustomerContract> contracts = customerContractMapper.selectCustomerContractList(queryContract);

        String cooperationStatus = customerInfo.getCooperationStatus();
        if ("4".equals(cooperationStatus)){
            if (contracts != null && !contracts.isEmpty()) {
                List<CustomerContractRespVo> contractRespVos = getCarrierContractRespVos(contracts);
                respVo.setContracts(contractRespVos.stream().sorted(Comparator.comparing(CustomerContractRespVo::getCreateTime).reversed()).collect(Collectors.toList()));
            }else {
                // 没有合同，设置默认状态
                respVo.setCooperationStatus("2"); // 已结束
                respVo.setCooperationStatusDesc(getCooperationStatusDesc("2"));
            }
        }else {
            // 根据合同重新计算合作状态
            if (contracts != null && !contracts.isEmpty()) {
                determineCooperationStatusFromContracts(respVo, contracts);
                // 更新货主合作状态
                updateCarrierInfoStatus(respVo, customerInfo);
                List<CustomerContractRespVo> contractRespVos = getCarrierContractRespVos(contracts);
                respVo.setContracts(contractRespVos.stream().sorted(Comparator.comparing(CustomerContractRespVo::getCreateTime).reversed()).collect(Collectors.toList()));
            }else {
                // 没有合同，设置默认状态
                respVo.setCooperationStatus("2"); // 已结束
                respVo.setCooperationStatusDesc(getCooperationStatusDesc("2"));
            }
        }
        return respVo;
    }

    @NotNull
    private List<CustomerContractRespVo> getCarrierContractRespVos(List<CustomerContract> contracts) {
        log.info("getCarrierContractRespVos,contracts:{}",contracts);
		return contracts.stream().map(contract -> {
            CustomerContractRespVo contractRespVo = new CustomerContractRespVo();
            BeanUtil.copyProperties(contract, contractRespVo);
            contractRespVo.setContractStatusDesc(getSelf().getContractStatusDesc(contract.getContractStatus()));
            return contractRespVo;
        }).collect(Collectors.toList());
    }

    /**
     * 更新货主合作状态
     * @param respVo vo
     * @param customerInfo 货主信息
     */
    private void updateCarrierInfoStatus(CustomerInfoRespVo respVo, CustomerInfo customerInfo) {
        if (!respVo.getCooperationStatus().equals(customerInfo.getCooperationStatus())) {
            // 状态发生变化，更新数据库
            CustomerInfo updateInfo = new CustomerInfo();
            updateInfo.setId(customerInfo.getId());
            updateInfo.setCooperationStatus(respVo.getCooperationStatus());
            // 更新状态
            try {
                String username = SecurityUtils.getUsername();
                updateInfo.setUpdateBy(username);
            }catch (Exception e){
                log.info("updateCarrierInfoStatus,获取更新人失败，定时器执行,e->{}",e.getMessage());
                updateInfo.setUpdateBy(String.valueOf(0));
            }
            updateInfo.setUpdateTime(new Date());
            customerInfoMapper.updateCustomerInfo(updateInfo);
            log.info("更新货主[{}]合作状态: {} -> {}", customerInfo.getId(),
                    customerInfo.getCooperationStatus(), respVo.getCooperationStatus());
        }
    }

    /**
     * 获取合作状态描述
     * 
     * @param status 状态码
     * @return 状态描述
     */
    private String getCooperationStatusDesc(String status) {
        if (status == null) {
            return "";
        }
        
        switch (status) {
            case "1":
                return "合作中";
            case "2":
                return "待合作";
            case "3":
                return "未开始";
            case "4":
                return "已结束";
            case "5":
                return "即将到期";
            default:
                return "";
        }
    }
    
    /**
     * 获取合同状态描述
     * 
     * @param status 状态码
     * @return 状态描述
     */
    private String getContractStatusDesc(String status) {
        if (status == null) {
            return "";
        }
        
        switch (status) {
            case "1":
                return "待生效";
            case "2":
                return "生效中";
            case "3":
                return "已结束";
            default:
                return "";
        }
    }

    @Resource
    private ISysUserService sysUserService;

    /**
     * 查询货主信息列表
     * 
     * @param customerInfo 货主信息
     * @return 货主信息响应对象集合
     */
    @Override
    public List<CustomerInfoRespVo> selectCustomerInfoList(CustomerInfoPageReqVo customerInfo)
    {
        // 查询货主信息列表
        List<CustomerInfo> customerInfoList = customerInfoMapper.selectCustomerInfoList(customerInfo);
        
        if (customerInfoList == null || customerInfoList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> dispatcherIdList = customerInfoList.stream().map(CustomerInfo::getDispatcher).map(String::valueOf).collect(Collectors.toList());
        Map<String, SysUser> sysUserListByCreateByList = sysUserService.getSysUserListByCreateByList(dispatcherIdList);
        // 转换为响应VO
        List<CustomerInfoRespVo> respVoList = new ArrayList<>();
        for (CustomerInfo info : customerInfoList) {
            CustomerInfoRespVo respVo = new CustomerInfoRespVo();
            BeanUtil.copyProperties(info, respVo);

            SysUser sysUser = sysUserListByCreateByList.get(String.valueOf(info.getDispatcher()));
            if (sysUser !=null){
                respVo.setDispatcherName(sysUser.getNickName());
            }
            // 查询货主的承运合同
            CustomerContract queryContract = new CustomerContract();
            queryContract.setCustomerId(info.getId());
            List<CustomerContract> contracts = customerContractMapper.selectCustomerContractList(queryContract);
            if (contracts != null && !contracts.isEmpty()) {
                // 计算合作状态
                //状态设置为已结束无需更新状态
                String cooperationStatus = info.getCooperationStatus();
                if ("4".equals(cooperationStatus)){
                    respVo.setCooperationStatusDesc(getCooperationStatusDesc("4"));
                    //使用stream流查找结束日期最晚的合同
                    CustomerContract latestContract = contracts.stream()
                            .max((c1, c2) -> c1.getEndDate().compareTo(c2.getEndDate()))
                            .orElse(new CustomerContract());
					respVo.setStartDate(DateUtil.format(latestContract.getStartDate(), "yyyy-MM-dd"));
					respVo.setEndDate(DateUtil.format(latestContract.getEndDate(), "yyyy-MM-dd"));

					respVoList.add(respVo);
                    continue;
                }
                determineCooperationStatusFromContracts(respVo, contracts);
            } else {
                // 没有合同，设置默认状态待合作
                respVo.setCooperationStatus("2"); // 已结束
                respVo.setCooperationStatusDesc(getCooperationStatusDesc("2"));
            }
            updateCarrierInfoStatus(respVo, info);
            respVoList.add(respVo);
        }
        return respVoList;
    }

    /**
     * 新增货主信息
     * 
     * @param customerInfoReqVo 货主信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertCustomerInfo(CustomerInfoReqVo customerInfoReqVo)
    {
        // 校验社会信用代码是否唯一
        if (!getSelf().checkSocialCreditCodeUnique(customerInfoReqVo.getSocialCreditCode()))
        {
            throw exception(BASE_SERVER_ERROR, "新增货主失败，货主已存在，请勿重复保存");
        }
        CustomerContractReqVo customerContractReqVo = customerInfoReqVo.getCustomerContractReqVo();
        if (customerContractReqVo == null){
            throw exception(BASE_SERVER_ERROR, "合作信息不能为空");
        }
        Date startDate = customerContractReqVo.getStartDate();
        Date endDate = customerContractReqVo.getEndDate();
        if (startDate == null || endDate == null){
            throw exception(BASE_SERVER_ERROR, "合作有效期时间不能为空");
        }
        Date now = new Date();
        if (startDate.before(getStartDate(now))) {
            throw exception(BASE_SERVER_ERROR, "合约开始日期不能早于当前日期");
        }
        // 创建新的实体类并复制属性
        CustomerInfo customerInfo = new CustomerInfo();
        BeanUtil.copyProperties(customerInfoReqVo, customerInfo);
        customerInfo.setId(TextUtil.generateId());
        // 设置创建人和创建时间
        customerInfo.setCreateBy(SecurityUtils.getUsername());
        customerInfo.setCreateTime(new Date());
        // 根据合作日期设置合作状态
        String cooperationStatus = getSelf().determineCooperationStatus(startDate, endDate);
        customerInfo.setCooperationStatus(cooperationStatus);
        // 插入货主信息
        int i = customerInfoMapper.insertCustomerInfo(customerInfo);
        // 同时将合同信息添加到carrier_contract表中
        CustomerContract customerContract = new CustomerContract();
        customerContract.setId(TextUtil.generateId());
        customerContract.setCustomerId(customerInfo.getId()); // 设置货主ID
        customerContract.setStartDate(startDate);
        customerContract.setEndDate(endDate);
        customerContract.setContractStatus(determineContractStatus(startDate, endDate));
        customerContract.setAttachmentPath(customerContractReqVo.getAttachmentPath());
        customerContract.setCreateBy(SecurityUtils.getUsername());
        customerContract.setCreateTime(new Date());
        int j = customerContractMapper.insertCustomerContract(customerContract);
        return i > 0 && j > 0 ? 1 : 0;
    }
    
    /**
     * 根据开始和结束日期确定合作状态
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 合作状态代码
     */
    @Override
    public String determineCooperationStatus(Date startDate, Date endDate) {
        // 获取当前日期
        Date now = new Date();
        
        // 调整开始日期为当天的00:00:00
        Date adjustedStartDate = getStartDate(startDate);
        // 调整结束日期为当天的23:59:59
        Date adjustedEndDate = getEndDate(endDate);

        // 当前日期早于开始日期，合作未开始
        if (now.before(adjustedStartDate)) {
            return "3"; // 未开始
        }
        // 当前日期晚于结束日期，合作已结束
        if (now.after(adjustedEndDate)) {
            return "4"; // 已结束
        }
        
        // 计算剩余天数（按日期计算）
        long diffInDays = DateUtil.betweenDay(now, adjustedEndDate, false);
        
        // 如果剩余天数小于等于30天，即将到期
        if (diffInDays <= 30) {
            return "5"; // 即将到期
        }
        // 其他情况，在合作中
        return "1"; // 在合作中
    }
    
    /**
     * 根据开始和结束日期确定合同状态
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 合同状态代码
     */
    public String determineContractStatus(Date startDate, Date endDate) {
        // 获取当前日期
        Date now = new Date();
        
        // 调整开始日期为当天的00:00:00
        Date adjustedStartDate = getStartDate(startDate);

        // 调整结束日期为当天的23:59:59
        Date adjustedEndDate = getEndDate(endDate);

        // 当前日期早于开始日期，合同待生效
        if (now.before(adjustedStartDate)) {
            return "1"; // 待生效
        }
        // 当前日期晚于结束日期，合同已结束
        if (now.after(adjustedEndDate)) {
            return "3"; // 已结束
        }
        // 其他情况，合同生效中
        return "2"; // 生效中
    }


    /**
     * 修改货主信息
     * 
     * @param customerInfoEditVo 货主信息编辑对象
     * @return 结果
     */
    @Override
    public int updateCustomerInfo(CustomerInfoEditVo customerInfoEditVo)
    {
        // 获取现有货主信息
        CustomerInfo existingCustomerInfo = customerInfoMapper.selectCustomerInfoById(customerInfoEditVo.getId());
        if (existingCustomerInfo == null) {
            throw exception(BASE_SERVER_ERROR, "修改货主失败，货主不存在");
        }
        
        // 只更新允许修改的字段：企业联系人、联系电话、运输线路
        existingCustomerInfo.setContactPerson(customerInfoEditVo.getContactPerson());
        existingCustomerInfo.setContactPhone(customerInfoEditVo.getContactPhone());
        existingCustomerInfo.setTransportRoutes(customerInfoEditVo.getTransportRoutes());
        existingCustomerInfo.setDispatcher(customerInfoEditVo.getDispatcher());
        
        // 设置更新人和更新时间
        existingCustomerInfo.setUpdateBy(SecurityUtils.getUsername());
        existingCustomerInfo.setUpdateTime(new Date());
        
        // 更新货主信息
        return customerInfoMapper.updateCustomerInfo(existingCustomerInfo);
    }

    /**
     * 删除货主信息信息
     * 
     * @param id 货主信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerInfoById(Long id)
    {
        return customerInfoMapper.deleteCustomerInfoById(id);
    }

    /**
     * 批量删除货主信息
     * 
     * @param ids 需要删除的货主信息主键
     * @return 结果
     */
    @Override
    public int deleteCustomerInfoByIds(Long[] ids)
    {
        return customerInfoMapper.deleteCustomerInfoByIds(ids);
    }
    
    /**
     * 校验社会信用代码是否唯一
     *
     * @param socialCreditCode 统一社会信用代码
     * @return 结果
     */
    @Override
    public boolean checkSocialCreditCodeUnique(String socialCreditCode)
    {
        return checkSocialCreditCodeUnique(socialCreditCode, null);
    }
    
    /**
     * 校验社会信用代码是否唯一
     *
     * @param socialCreditCode 统一社会信用代码
     * @param id 货主ID
     * @return true - 唯一 / false - 不唯一
     */
    @Override
    public boolean checkSocialCreditCodeUnique(String socialCreditCode, Long id)
    {
        CustomerInfo info = customerInfoMapper.selectCustomerInfoBySocialCreditCode(socialCreditCode);
		return info == null || (id != null && id.equals(info.getId()));
	}

    /**
     * 根据合同列表确定合作状态
     * 
     * @param respVo 货主响应VO
     * @param contracts 合同列表
     */
    private void determineCooperationStatusFromContracts(CustomerInfoRespVo respVo, List<CustomerContract> contracts) {
        Date now = new Date();
        
        boolean hasActiveContract = false;
        boolean hasPendingContract = false;
        boolean allContractsExpired = false; // 标记是否所有合同都已过期

        long minRemainingDays = Long.MAX_VALUE;
        
        // 用于记录当前状态对应的最新合同
        CustomerContract latestActiveContract = null;
        CustomerContract latestPendingContract = null;
        CustomerContract latestExpiredContract = null;

        for (CustomerContract contract : contracts) {
            Date startDate = contract.getStartDate();
            Date endDate = contract.getEndDate();

            // 调整开始日期为当天的00:00:00
            Date adjustedStartDate = getStartDate(startDate);
            // 调整结束日期为当天的23:59:59
            Date adjustedEndDate = getEndDate(endDate);
            if (now.before(adjustedStartDate)) {
                // 合同待生效
                hasPendingContract = true;
                allContractsExpired = false; // 有待生效合同，所以不是所有合同都过期
                // 更新最新的待生效合同
                if (latestPendingContract == null || startDate.after(latestPendingContract.getStartDate())) {
                    latestPendingContract = contract;
                }
                // 检查合同状态是否需要更新
                String newContractStatus = "1"; // 待生效
                carrierContractService.updateContractStatus(contract, newContractStatus);
            } else if (now.after(adjustedEndDate)) {
                // 合同已结束
                log.info("货主合同已失效");
                allContractsExpired = true;  // 显式标记
                // 更新最新的已结束合同
                if (latestExpiredContract == null || endDate.after(latestExpiredContract.getEndDate())) {
                    latestExpiredContract = contract;
                }
                // 检查合同状态是否需要更新
                String newContractStatus = "3"; // 已结束
                carrierContractService.updateContractStatus(contract, newContractStatus);
            } else {
                // 合同生效中
                hasActiveContract = true;
                allContractsExpired = false; // 有生效中合同，所以不是所有合同都过期

                // 更新最新的活跃合同
                if (latestActiveContract == null || endDate.after(latestActiveContract.getEndDate())) {
                    latestActiveContract = contract;
                }
                // 检查合同状态是否需要更新
                String newContractStatus = "2"; // 生效中
                carrierContractService.updateContractStatus(contract, newContractStatus);
                // 计算剩余天数（按日期计算）
                long diffInDays = DateUtil.betweenDay(now, adjustedEndDate, false);

                if (diffInDays < minRemainingDays) {
                    minRemainingDays = diffInDays;
                }
            }
        }
        
        // 设置合作状态
        if (hasActiveContract) {
            // 使用最新的活跃合同的日期
            respVo.setStartDate(DateUtil.format(latestActiveContract.getStartDate(), "yyyy-MM-dd"));
            respVo.setEndDate(DateUtil.format(latestActiveContract.getEndDate(), "yyyy-MM-dd"));

            if (minRemainingDays <= 30) {
                // 即将到期
                respVo.setCooperationStatus("5");
                respVo.setCooperationStatusDesc(getCooperationStatusDesc("5"));
                respVo.setRemainingDays(minRemainingDays);
            } else {
                // 在合作中
                respVo.setCooperationStatus("1");
                respVo.setCooperationStatusDesc(getCooperationStatusDesc("1"));
                respVo.setRemainingDays(minRemainingDays);
            }
        } else if (hasPendingContract) {
            // 未开始 - 使用最新的待生效合同日期
            respVo.setStartDate(DateUtil.format(latestPendingContract.getStartDate(), "yyyy-MM-dd"));
            respVo.setEndDate(DateUtil.format(latestPendingContract.getEndDate(), "yyyy-MM-dd"));
            respVo.setCooperationStatus("3");
            respVo.setCooperationStatusDesc(getCooperationStatusDesc("3"));
        } else if (allContractsExpired) {
            // 已结束 - 使用最新的已结束合同日期
            respVo.setStartDate(DateUtil.format(latestExpiredContract.getStartDate(), "yyyy-MM-dd"));
            respVo.setEndDate(DateUtil.format(latestExpiredContract.getEndDate(), "yyyy-MM-dd"));
            respVo.setCooperationStatus("4");
            respVo.setCooperationStatusDesc(getCooperationStatusDesc("4"));
        }
    }

    /**
     * 开始时间添加默认00
     * @param startDate 开始时间
     * @return Date
     */
    @NotNull
    public static Date getStartDate(Date startDate) {
        return getDate(startDate);
    }

    /**
     * 结束时间添加默认23：:59：:59
     * @param endDate 结束时间
     * @return Date
     */
    @NotNull
    public static Date getEndDate(Date endDate) {
        Calendar calEnd = getCalendar(endDate);
        return calEnd.getTime();
    }

    @NotNull
    public static Calendar getEndCalendar(Date endDate) {
		return getCalendar(endDate);
    }

    @NotNull
    private static Calendar getCalendar(Date endDate) {
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(endDate);
        calEnd.set(Calendar.HOUR_OF_DAY, 23);
        calEnd.set(Calendar.MINUTE, 59);
        calEnd.set(Calendar.SECOND, 59);
        calEnd.set(Calendar.MILLISECOND, 999);
        return calEnd;
    }

    /**
     * 货主合作续约
     * 
     * @param customerId 货主ID
     * @param startDate 新合约开始日期
     * @param endDate 新合约结束日期
     * @param attachmentPath 合同附件路径（非必填）
     * @return 结果
     */
    @Override
    @Transactional
    public int renewCustomerContract(Long customerId, Date startDate, Date endDate, String attachmentPath) {
        // 参数校验
        if (customerId == null || startDate == null || endDate == null) {
            throw exception(BASE_SERVER_ERROR, "续约参数不完整，请检查");
        }

        if (endDate.before(startDate)) {
            throw exception(BASE_SERVER_ERROR, "合约结束日期不能早于开始日期");
        }
        
        // 查询货主信息
        CustomerInfoRespVo carrierInfo = selectCustomerInfoById(customerId);
        if (carrierInfo == null) {
            throw exception(BASE_SERVER_ERROR, "货主信息不存在");
        }
        
        // 校验货主状态是否允许续约（合作中、即将到期、已结束）
        String status = carrierInfo.getCooperationStatus();
        if (!"1".equals(status) && !"4".equals(status) && !"5".equals(status)) {
            throw exception(BASE_SERVER_ERROR, "当前货主状态不允许进行合作续约");
        }
        
        // 获取货主所有合约
        CustomerContract queryContract = new CustomerContract();
        queryContract.setCustomerId(customerId);
        List<CustomerContract> contracts = customerContractMapper.selectCustomerContractList(queryContract);
        
        // 调整开始日期和结束日期
        Date adjustedStartDate = getStartDate(startDate);
        Date adjustedEndDate = getEndDate(endDate);
        
        // 校验新合约开始日期不能小于当前日期
        Date now = new Date();
        if (adjustedStartDate.before(getStartDate(now))) {
            throw exception(BASE_SERVER_ERROR, "合约开始日期不能早于当前日期");
        }
        
        // 校验合约时间不能重叠
        if (contracts != null && !contracts.isEmpty()) {
            for (CustomerContract contract : contracts) {
                Date existingStartDate = getStartDate(contract.getStartDate());
                Date existingEndDate = getEndDate(contract.getEndDate());
                
                // 检查时间重叠
                // 新合约开始日期在已有合约的时间范围内
                boolean startOverlap = !adjustedStartDate.before(existingStartDate) && !adjustedStartDate.after(existingEndDate);
                // 新合约结束日期在已有合约的时间范围内
                boolean endOverlap = !adjustedEndDate.before(existingStartDate) && !adjustedEndDate.after(existingEndDate);
                // 新合约完全包含已有合约
                boolean containsExisting = adjustedStartDate.before(existingStartDate) && adjustedEndDate.after(existingEndDate);
                
                if (startOverlap || endOverlap || containsExisting) {
                    throw exception(BASE_SERVER_ERROR, "新合约时间与已有合约时间重叠，请调整");
                }
            }
            
            // 获取最新的合约结束日期
            Date latestEndDate = contracts.stream()
                    .map(contract -> getEndDate(contract.getEndDate()))
                    .max(Date::compareTo)
                    .orElse(null);
            
            // 校验新合约开始日期是否大于最新合约结束日期
            if (latestEndDate != null && adjustedStartDate.before(latestEndDate)) {
                throw exception(BASE_SERVER_ERROR, "新合约开始日期必须晚于已有合约的结束日期");
            }
        }
        
        // 创建新合约
        CustomerContract newContract = new CustomerContract();
        newContract.setCustomerId(customerId);
        newContract.setStartDate(startDate);
        newContract.setEndDate(endDate);
        newContract.setAttachmentPath(attachmentPath);
        
        // 计算合约状态
        String contractStatus = getSelf().determineContractStatus(startDate, endDate);
        newContract.setContractStatus(contractStatus);
        
        // 设置创建人和创建时间
        newContract.setCreateBy(SecurityUtils.getUsername());
        newContract.setCreateTime(new Date());
        
        // 插入新合约
        int result = customerContractMapper.insertCustomerContract(newContract);
        
        // 更新货主合作状态
        if (result > 0) {
            // 重新查询所有合约（包括新添加的）
            List<CustomerContract> updatedContracts = customerContractMapper.selectCustomerContractList(queryContract);
            
            // 创建货主响应VO用于计算新的合作状态
            CustomerInfoRespVo respVo = new CustomerInfoRespVo();
            respVo.setId(customerId);
            
            // 计算新的合作状态
            determineCooperationStatusFromContracts(respVo, updatedContracts);
            
            // 更新货主合作状态
            CustomerInfo updateInfo = new CustomerInfo();
            updateInfo.setId(customerId);
            updateInfo.setCooperationStatus(respVo.getCooperationStatus());
            updateInfo.setUpdateBy(SecurityUtils.getUsername());
            updateInfo.setUpdateTime(new Date());
            customerInfoMapper.updateCustomerInfo(updateInfo);
            
            log.info("货主[{}]续约成功，合约ID：{}，新状态：{}", customerId, newContract.getId(), respVo.getCooperationStatus());
        }
        
        return result;
    }

    /**
     * 结束货主合作
     * 
     * @param customerId 货主ID
     * @param endReason 结束原因（可选）
     * @return 结果
     */
    @Override
    @Transactional
    public int endCustomerCooperation(Long customerId, String endReason) {
        // 参数校验
        if (customerId == null) {
            throw exception(BASE_SERVER_ERROR, "货主ID不能为空");
        }
        
        // 查询货主信息
        CustomerInfoRespVo carrierInfo = selectCustomerInfoById(customerId);
        if (carrierInfo == null) {
            throw exception(BASE_SERVER_ERROR, "货主信息不存在");
        }
        
        // 校验货主状态是否允许结束合作（只有合作中、即将到期状态可以结束）
        String status = carrierInfo.getCooperationStatus();
        if (!"1".equals(status) && !"5".equals(status)) {
            throw exception(BASE_SERVER_ERROR, "当前货主状态不允许结束合作，只有合作中或即将到期状态才能执行此操作");
        }
        
        // 获取货主所有合约
        CustomerContract queryContract = new CustomerContract();
        queryContract.setCustomerId(customerId);
        List<CustomerContract> contracts = customerContractMapper.selectCustomerContractList(queryContract);
        
        // 当前日期
        Date now = new Date();
        int updatedContracts = 0;
        
        // 更新所有有效合约状态为已结束
        if (contracts != null && !contracts.isEmpty()) {
            for (CustomerContract contract : contracts) {
                // 只处理待生效或生效中的合约
                if ("1".equals(contract.getContractStatus()) || "2".equals(contract.getContractStatus())) {
                    // 更新合约状态为已结束
                    CustomerContract updateContract = new CustomerContract();
                    updateContract.setId(contract.getId());
                    updateContract.setContractStatus("3"); // 已结束
                    updateContract.setUpdateBy(SecurityUtils.getUsername());
                    updateContract.setUpdateTime(now);
                    
                    // 如果有结束原因，也一并更新
                    if (StringUtils.hasText(endReason)) {
                        updateContract.setRemark(endReason);
                    }
                    
                    int result = customerContractMapper.updateCustomerContract(updateContract);
                    if (result > 0) {
                        updatedContracts++;
                        log.info("货主[{}]合约[{}]已手动结束", customerId, contract.getId());
                    }
                }
            }
        }
        
        // 更新货主合作状态为已结束
        CustomerInfo updateInfo = new CustomerInfo();
        updateInfo.setId(customerId);
        updateInfo.setCooperationStatus("4"); // 已结束
        updateInfo.setUpdateBy(SecurityUtils.getUsername());
        updateInfo.setUpdateTime(now);
        
        // 如果有结束原因，也一并更新
        if (StringUtils.hasText(endReason)) {
            updateInfo.setRemark(endReason);
        }
        
        int result = customerInfoMapper.updateCustomerInfo(updateInfo);
        
        if (result > 0) {
            log.info("货主[{}]合作已手动结束，共更新{}个合约状态", customerId, updatedContracts);
        }
        
        return result;
    }

    /**
     * 查询合作中的货主列表
     *
     * @return 合作中的货主列表
     */
    @Override
    public List<CustomerInfoRespVo> selectActiveCustomerList(Integer type) {
        // 创建查询条件，只查询合作状态为"合作中"(1)或"即将到期"(5)的货主
        List<String> statusList = new ArrayList<>();
        if (Objects.equals(type,1)){
            statusList.add("1"); // 在合作中
            statusList.add("5"); // 即将到期
        }else if (Objects.equals(type,2)){
            statusList.add("1"); // 在合作中
            statusList.add("5"); // 即将到期
            statusList.add("2"); // 待合作
        }
        // 调用Mapper查询数据库
        List<CustomerInfo> customerInfoList = customerInfoMapper.selectCustomerInfoByStatusList(statusList);

        // 将结果转换为响应VO对象
        List<CustomerInfoRespVo> resultList = new ArrayList<>();
        if (customerInfoList != null && !customerInfoList.isEmpty()) {
            for (CustomerInfo info : customerInfoList) {
                CustomerInfoRespVo respVo = new CustomerInfoRespVo();
                BeanUtils.copyProperties(info, respVo);
                // 设置合作状态描述
                respVo.setCooperationStatusDesc(getCooperationStatusDesc(info.getCooperationStatus()));
                resultList.add(respVo);
            }
        }

        return resultList;
    }



    /**
     * 定时任务，每日更新货主合作状态及合约状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCustomerStatusDaily() {
        log.info("开始执行货主合作状态和合约状态更新定时任务");
        
        // 查询所有非已结束状态的货主
        CustomerInfo query = new CustomerInfo();
        query.setCooperationStatus("4"); // 已结束状态，后面需要排除此状态
        List<CustomerInfo> totalCarrierList = customerInfoMapper.selectCustomerInfoListExcludeStatus(query);
        
        if (totalCarrierList == null || totalCarrierList.isEmpty()) {
            log.info("没有需要更新状态的货主数据");
            return;
        }
        
        log.info("共查询到{}条非已结束状态的货主数据", totalCarrierList.size());
        
        // 分批处理逻辑
        int batchSize = 100; // 每批处理100条
        int totalSize = totalCarrierList.size();
        
        // 大于500条则分批处理
        if (totalSize > 500) {
            int totalBatches = (totalSize + batchSize - 1) / batchSize; // 向上取整计算总批次
            log.info("数据量超过500，将分{}批次处理，每批{}条", totalBatches, batchSize);
            
            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                int startIndex = batchIndex * batchSize;
                int endIndex = Math.min(startIndex + batchSize, totalSize);
                
                List<CustomerInfo> batchList = totalCarrierList.subList(startIndex, endIndex);
                processBatch(batchList, batchIndex + 1, totalBatches);
            }
        } else {
            // 数据量较少，直接处理
            processBatch(totalCarrierList, 1, 1);
        }
        
        log.info("货主合作状态和合约状态更新定时任务执行完成");
    }

    /**
     * 处理一批货主数据的状态更新
     * 
     * @param batchList 批次数据列表
     * @param currentBatch 当前批次
     * @param totalBatches 总批次
     */
    private void processBatch(List<CustomerInfo> batchList, int currentBatch, int totalBatches) {
        log.info("开始处理第{}/{}批货主状态更新，本批次数据量: {}", currentBatch, totalBatches, batchList.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        try {
            // 获取所有需要处理的货主ID
            List<Long> customerIds = batchList.stream()
                    .map(CustomerInfo::getId)
                    .collect(Collectors.toList());
            
            // 一次性查询所有合约
            List<CustomerContract> allContracts = customerContractMapper.selectCustomerContractsByCustomerIds(customerIds);
            
            // 按货主ID对合约进行分组
            Map<Long, List<CustomerContract>> contractsByCustomerId = allContracts.stream()
                    .collect(Collectors.groupingBy(CustomerContract::getCustomerId));
            
            log.info("已获取{}个货主的合约数据，共{}条合约记录", contractsByCustomerId.size(), allContracts.size());
            
            // 处理每个货主数据
            for (CustomerInfo customerInfo : batchList) {
                try {
                    Long customerId = customerInfo.getId();
                    
                    // 获取当前货主的所有合约
                    List<CustomerContract> contracts = contractsByCustomerId.getOrDefault(customerId, new ArrayList<>());
                    
                    // 创建响应VO用于计算状态
                    CustomerInfoRespVo respVo = new CustomerInfoRespVo();
                    BeanUtil.copyProperties(customerInfo, respVo);
                    
                    if (!contracts.isEmpty()) {
                        // 计算合作状态
                        determineCooperationStatusFromContracts(respVo, contracts);
                    } else {
                        // 没有合同，设置默认状态待合作
                        respVo.setCooperationStatus("2");
                        respVo.setCooperationStatusDesc(getCooperationStatusDesc("2"));
                    }
                    
                    // 更新货主状态
                    updateCarrierInfoStatus(respVo, customerInfo);
                    successCount++;
                } catch (Exception e) {
                    log.error("更新货主[{}]状态时发生错误", customerInfo.getId(), e);
                    failureCount++;
                }
            }
        } catch (Exception e) {
            log.error("批量处理货主状态时发生错误", e);
            failureCount += batchList.size();
        }
        
        log.info("第{}/{}批货主状态更新完成，成功: {}，失败: {}", 
                currentBatch, totalBatches, successCount, failureCount);
    }

    /**
     * 通用文件上传方法
     * 
     * @param file 上传的文件
     * @param fileType 文件类型（用于确定存储路径，如contract、invoice等）
     * @return 文件访问URL
     */
    public String uploadFile(MultipartFile file, String fileType) {
        if (file == null || file.isEmpty()) {
            throw exception(BASE_SERVER_ERROR, "上传文件不能为空");
        }

        try {
            // 获取文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            // 生成唯一文件名
            String fileName = fileType + "_" + System.currentTimeMillis() + fileExtension;
            // 存储路径
            String key = fileType + "/" + fileName;

            // 上传文件到七牛云
            Response response = QiNiuUtil.uploadByFile(file, key);
            if (response.isOK()) {
                // 返回文件访问URL
                String fileUrl = customConfig.getQiniuDomain() + key;
                log.info("文件上传成功，OSS地址：{}", fileUrl);
                return fileUrl;
            } else {
                log.error("文件上传失败");
                throw exception(BASE_SERVER_ERROR, "文件上传失败");
            }
        } catch (Exception e) {
            log.error("文件上传异常", e);
            throw exception(BASE_SERVER_ERROR, "文件上传异常：" + e.getMessage());
        }
    }

    /**
     * 上传货主合同文件
     * 
     * @param file 上传的文件
     * @return 文件访问URL
     */
    public String uploadContractFile(MultipartFile file) {
        return uploadFile(file, "customer_contract");
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private CustomerInfoServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
} 