package com.zly.project.scheduler.service;

import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.scheduler.api.vo.ContractSimpleRes;
import com.zly.project.scheduler.api.vo.ContractSrarchReq;
import com.zly.project.scheduler.api.vo.FrameworkContractRes;

import java.util.List;

/**
 * 货主信息远程接口服务
 *
 * <AUTHOR>
 */
public interface ApiFrameworkContractService {

	/**
	 * 查询项目合约
	 * @param req  请求参数
	 * @return TableDataInfo<FrameworkContractRes>
	 */
	TableDataInfo<FrameworkContractRes> platformList(ContractSrarchReq req);

	/**
	 * 平台端项目合约查询框列表
	 * @param projectName
	 * @param resourceCustomerId
	 * @return
	 */
	CommonResult<List<ContractSimpleRes>> platformSearchList(String projectName,Long resourceCustomerId);
}
