package com.zly.project.scheduler.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 货主信息编辑VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@ApiModel("货主信息编辑VO对象")
@Data
public class CustomerInfoEditVo {
    
    /** 主键ID */
    @NotNull(message = "货主ID不能为空")
    @ApiModelProperty(value = "货主ID")
    private Long id;

    /** 企业联系人 */
    @NotBlank(message = "企业联系人不能为空")
    @ApiModelProperty(value = "企业联系人")
    private String contactPerson;

    /** 联系电话 */
    @NotBlank(message = "联系电话不能为空")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 运输线路 */
    @ApiModelProperty(value = "运输线路")
    private String transportRoutes;

    /** 调度员 */
    @ApiModelProperty(value = "调度员")
    private Long dispatcher;
} 