package com.zly.project.scheduler.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 货主承运合同对象 customer_contract
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public class CustomerContract extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 货主ID */
    @Excel(name = "货主ID")
    @NotNull(message = "货主ID不能为空")
    private Long customerId;

    /** 合作开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合作开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "合作开始时间不能为空")
    private Date startDate;

    /** 合作结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合作结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "合作结束时间不能为空")
    private Date endDate;

    /** 合约状态（1-待生效，2-生效中，3-已结束） */
    @Excel(name = "合约状态", readConverterExp = "1=待生效,2=生效中,3=已结束")
    private String contractStatus;

    /** 合同附件路径 */
    @Excel(name = "合同附件路径")
    private String attachmentPath;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCustomerId(Long customerId)
    {
        this.customerId = customerId;
    }

    public Long getCustomerId()
    {
        return customerId;
    }
    public void setStartDate(Date startDate) 
    {
        this.startDate = startDate;
    }

    public Date getStartDate() 
    {
        return startDate;
    }
    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    public Date getEndDate() 
    {
        return endDate;
    }
    public void setContractStatus(String contractStatus) 
    {
        this.contractStatus = contractStatus;
    }

    public String getContractStatus() 
    {
        return contractStatus;
    }
    public void setAttachmentPath(String attachmentPath) 
    {
        this.attachmentPath = attachmentPath;
    }

    public String getAttachmentPath() 
    {
        return attachmentPath;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("customerId", getCustomerId())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("contractStatus", getContractStatus())
            .append("attachmentPath", getAttachmentPath())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 