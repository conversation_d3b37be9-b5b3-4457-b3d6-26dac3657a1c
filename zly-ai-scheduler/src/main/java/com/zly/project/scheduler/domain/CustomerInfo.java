package com.zly.project.scheduler.domain;

import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 货主信息对象 carrier_info
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerInfo extends BaseEntity
{

    /** 主键ID */
    private Long id;

    /** 货主名称 */
    @Excel(name = "货主名称")
    @NotBlank(message = "货主名称不能为空")
    private String customerName;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    @NotBlank(message = "统一社会信用代码不能为空")
    private String socialCreditCode;

    /** 企业联系人 */
    @Excel(name = "企业联系人")
    @NotBlank(message = "企业联系人不能为空")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    /** 运输线路 */
    @Excel(name = "运输线路")
    private String transportRoutes;

    /** 调度员 */
    @Excel(name = "调度员")
    private Long dispatcher;

    /** 合作状态（1-在合作中，2-待合作，3-未开始，4-已结束，5-即将到期） */
    @Excel(name = "合作状态", readConverterExp = "1=在合作中,2=待合作,3=未开始,4=已结束,5=即将到期(30天)")
    private String cooperationStatus;

} 