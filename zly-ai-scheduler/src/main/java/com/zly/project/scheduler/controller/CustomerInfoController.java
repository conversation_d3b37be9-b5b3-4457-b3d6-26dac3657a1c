package com.zly.project.scheduler.controller;

import java.util.List;

import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.project.cargo.service.IDailyCargoService;
import com.zly.project.scheduler.controller.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.project.scheduler.service.ICustomerInfoService;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 货主信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Api(tags = "管理后台 - 货主信息Controller")
@RestController
@RequestMapping("/scheduler/customer")
public class CustomerInfoController extends BaseController
{
    @Autowired
    private ICustomerInfoService carrierInfoService;

    @Resource
    private IDailyCargoService dailyCargoService;

    /**
     * 查询货主信息列表
     */
    @ApiOperation("查询货主信息列表")
    @PreAuthorize("@ss.hasPermi('scheduler:customer:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody CustomerInfoPageReqVo pageReqVo)
    {
        startPageByPageDomain(pageReqVo);
        List<CustomerInfoRespVo> list = carrierInfoService.selectCustomerInfoList(pageReqVo);
        return getDataTable(list);
    }

    /**
     * 获取货主信息详细信息
     */
    @ApiOperation("获取货主信息详细信息")
    @PreAuthorize("@ss.hasPermi('scheduler:customer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(carrierInfoService.selectCustomerInfoById(id));
    }

    /**
     * 新增货主信息
     */
    @ApiOperation("新增货主信息")
    @PreAuthorize("@ss.hasPermi('scheduler:customer:add')")
    @Log(title = "货主信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody CustomerInfoReqVo customerInfoReqVo)
    {
        return toAjax(carrierInfoService.insertCustomerInfo(customerInfoReqVo));
    }

    /**
     * 修改货主信息
     */
    @ApiOperation("修改货主信息")
    @PreAuthorize("@ss.hasPermi('scheduler:customer:edit')")
    @Log(title = "货主信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody CustomerInfoEditVo customerInfoEditVo)
    {
        return toAjax(carrierInfoService.updateCustomerInfo(customerInfoEditVo));
    }

    /**
     * 删除货主信息
     */
    @ApiOperation("删除货主信息")
    @PreAuthorize("@ss.hasPermi('scheduler:customer:remove')")
    @Log(title = "货主信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(carrierInfoService.deleteCustomerInfoByIds(ids));
    }
    
    /**
     * 货主合约续约
     */
    @ApiOperation("货主合约续约")
    @PreAuthorize("@ss.hasPermi('scheduler:customer:renew')")
    @Log(title = "货主合约续约", businessType = BusinessType.INSERT)
    @PostMapping("/renew")
    public AjaxResult renewContract(@Valid @RequestBody CustomerContractRenewVo renewVo)
    {
        return toAjax(carrierInfoService.renewCustomerContract(
                renewVo.getCustomerId(),
                renewVo.getStartDate(), 
                renewVo.getEndDate(), 
                renewVo.getAttachmentPath()));
    }
    
    /**
     * 结束货主合作
     */
    @ApiOperation("结束货主合作")
    @PreAuthorize("@ss.hasPermi('scheduler:customer:end')")
    @Log(title = "结束货主合作", businessType = BusinessType.UPDATE)
    @PostMapping("/end")
    public AjaxResult endCooperation(@Valid @RequestBody CustomerCooperationEndVo endVo)
    {
        return toAjax(carrierInfoService.endCustomerCooperation(
                endVo.getCustomerId(),
                endVo.getEndReason()));
    }


    /**
     * 获取合作中的货主列表，用于添加每日货源时选择
     */
    @ApiOperation("获取合作中的货主列表")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:activeCustomer:list')")
    @GetMapping("/customers/{type}")
    public AjaxResult listActiveCustomers(@PathVariable("type") Integer type) {
        List<CustomerInfoRespVo> list = dailyCargoService.getActiveCustomerList(type);
        return AjaxResult.success(list);
    }


    /**
     * 测试定时任务，每日更新货主合作状态及合约状态
     */
    @Anonymous
    @ApiOperation("测试定时任务，每日更新货主合作状态及合约状态")
    @PostMapping("/updateCustomerStatusDaily")
    public AjaxResult updateCustomerStatusDaily() {
        carrierInfoService.updateCustomerStatusDaily();
        return AjaxResult.success("每日更新货主合作状态及合约状态执行成功");
    }
} 