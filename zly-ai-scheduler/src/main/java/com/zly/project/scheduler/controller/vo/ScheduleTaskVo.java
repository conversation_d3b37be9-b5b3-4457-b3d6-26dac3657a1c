package com.zly.project.scheduler.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 调度任务VO对象
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@ApiModel("调度任务VO对象")
@Data
public class ScheduleTaskVo {
    
    @ApiModelProperty(value = "每日货源ID")
    private Long cargoId;
    
    @ApiModelProperty(value = "货主ID")
    private Long customerId;
    
    @ApiModelProperty(value = "货主名称")
    private String customerName;
    
    @ApiModelProperty(value = "货主联系人")
    private String contactPerson;
    
    @ApiModelProperty(value = "货主联系电话")
    private String contactPhone;
    
    @ApiModelProperty(value = "货物名称")
    private String cargoName;
    
    @ApiModelProperty(value = "货物类型")
    private String cargoType;
    
    @ApiModelProperty(value = "包装方式")
    private String packagingMethod;

    @ApiModelProperty(value = "货物数量(吨)")
    private BigDecimal cargoWeight;

    @ApiModelProperty(value = "货物数量(方)")
    private BigDecimal cargoVolume;
    
    // 运输路线信息
    @ApiModelProperty(value = "发货地(省市区详细地址)")
    private String loadingFullAddress;

    /** 装货省编码 */
    private String loadingProvinceCode;

    /** 装货省名称 */
    @Excel(name = "装货省名称")
    private String loadingProvinceName;

    /** 装货市编码 */
    private String loadingCityCode;

    /** 装货市名称 */
    @Excel(name = "装货市名称")
    private String loadingCityName;

    /** 装货区编码 */
    private String loadingDistrictCode;

    /** 装货区名称 */
    @Excel(name = "装货区名称")
    private String loadingDistrictName;

    /** 装货详细地址 */
    @Excel(name = "装货详细地址")
    private String loadingAddress;

    
    @ApiModelProperty(value = "收货地(省市区详细地址)")
    private String unloadingFullAddress;

    /** 卸货省编码 */
    private String unloadingProvinceCode;

    /** 卸货省名称 */
    @Excel(name = "卸货省名称")
    private String unloadingProvinceName;

    /** 卸货市编码 */
    private String unloadingCityCode;

    /** 卸货市名称 */
    @Excel(name = "卸货市名称")
    private String unloadingCityName;

    /** 卸货区编码 */
    private String unloadingDistrictCode;

    /** 卸货区名称 */
    @Excel(name = "卸货区名称")
    private String unloadingDistrictName;

    /** 卸货详细地址 */
    @Excel(name = "卸货详细地址")
    private String unloadingAddress;

    @ApiModelProperty(value = "起始地点",  example = "上海市浦东新区")
    private String startPoint;

    @ApiModelProperty(value = "目的地点",  example = "北京市朝阳区")
    private String endPoint;
    
    // 时间信息
    @ApiModelProperty(value = "装货日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loadingDate;
    
    @ApiModelProperty(value = "装货时间格式类型(1-按天计算,2-按小时计算)")
    private String timeFormatType;
    
    @ApiModelProperty(value = "最早装货时间")
    private String loadingTimeStart;
    
    @ApiModelProperty(value = "最晚装货时间")
    private String loadingTimeEnd;
    
    @ApiModelProperty(value = "格式化后的装货时间(如: 6.4 9:00-17:00)")
    private String formattedLoadingTime;
    
    // 车辆要求
    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;
    
    @ApiModelProperty(value = "车辆数量")
    private Integer vehicleCount;

    @ApiModelProperty(value = "车辆长度")
    private Double vehicleLength;
    
    @ApiModelProperty(value = "特殊要求")
    private String specialRequirements;
} 