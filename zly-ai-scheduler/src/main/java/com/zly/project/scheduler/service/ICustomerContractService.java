package com.zly.project.scheduler.service;

import com.zly.project.scheduler.controller.vo.CustomerContractEditVo;
import com.zly.project.scheduler.domain.CustomerContract;

import java.util.Date;

/**
 * 货主合约服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface ICustomerContractService {
    
    /**
     * 更新合约状态
     * 
     * @param contract 合约信息
     * @param newStatus 新的合约状态
     * @return 结果
     */
    int updateContractStatus(CustomerContract contract, String newStatus);
    
    /**
     * 编辑货主合约
     * 
     * @param contractEditVo 合约编辑信息
     * @return 结果
     */
    int editCustomerContract(CustomerContractEditVo contractEditVo);
    
    /**
     * 检查合约时间是否重叠
     * 
     * @param customerId 货主ID
     * @param contractId 当前合约ID（排除自身）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return true-重叠 false-不重叠
     */
    boolean checkContractTimeOverlap(Long customerId, Long contractId, Date startDate, Date endDate);


} 