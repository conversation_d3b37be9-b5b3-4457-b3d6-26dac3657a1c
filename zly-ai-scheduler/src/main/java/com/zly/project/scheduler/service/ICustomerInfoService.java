package com.zly.project.scheduler.service;

import java.util.Date;
import java.util.List;

import com.zly.project.scheduler.controller.vo.CustomerInfoPageReqVo;
import com.zly.project.scheduler.controller.vo.CustomerInfoReqVo;
import com.zly.project.scheduler.controller.vo.CustomerInfoEditVo;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 货主信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface ICustomerInfoService
{
    /**
     * 查询货主信息
     * 
     * @param id 货主信息主键
     * @return 货主信息响应对象
     */
     CustomerInfoRespVo selectCustomerInfoById(Long id);

    /**
     * 查询货主信息列表
     * 
     * @param customerInfo 货主信息
     * @return 货主信息响应对象集合
     */
     List<CustomerInfoRespVo> selectCustomerInfoList(CustomerInfoPageReqVo customerInfo);

    /**
     * 新增货主信息
     * 
     * @param customerInfoReqVo 货主信息
     * @return 结果
     */
     int insertCustomerInfo(CustomerInfoReqVo customerInfoReqVo);

    /**
     * 修改货主信息
     * 
     * @param customerInfoEditVo 货主信息编辑对象
     * @return 结果
     */
     int updateCustomerInfo(CustomerInfoEditVo customerInfoEditVo);

    /**
     * 删除货主信息
     * 
     * @param id 货主信息主键
     * @return 结果
     */
     int deleteCustomerInfoById(Long id);

    /**
     * 批量删除货主信息
     * 
     * @param ids 需要删除的货主信息主键集合
     * @return 结果
     */
     int deleteCustomerInfoByIds(Long[] ids);
     
    /**
     * 校验社会信用代码是否唯一
     *
     * @param socialCreditCode 统一社会信用代码
     * @return 结果
     */
     boolean checkSocialCreditCodeUnique(String socialCreditCode);
     
    /**
     * 校验社会信用代码是否唯一
     *
     * @param socialCreditCode 统一社会信用代码
     * @param id 货主ID
     * @return 结果
     */
     boolean checkSocialCreditCodeUnique(String socialCreditCode, Long id);
     
    /**
     * 货主合作续约
     * 
     * @param customerId 货主ID
     * @param startDate 新合约开始日期
     * @param endDate 新合约结束日期
     * @param attachmentPath 合同附件路径（非必填）
     * @return 结果
     */
     int renewCustomerContract(Long customerId, java.util.Date startDate, java.util.Date endDate, String attachmentPath);
     
    /**
     * 结束货主合作
     * 
     * @param customerId 货主ID
     * @param endReason 结束原因（可选）
     * @return 结果
     */
     int endCustomerCooperation(Long customerId, String endReason);

    /**
     * 计算合约状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    String determineContractStatus(Date startDate, Date endDate);

    /**
     * 根据日期计算合作状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return String
     */
    String determineCooperationStatus(Date startDate, Date endDate);

    /**
     * 定时任务，每日更新货主合作状态及合约状态
     */
    void updateCustomerStatusDaily();

    /**
     * 查询合作状态的货主集合
     * @return List<CarrierInfoRespVo>
     */
    List<CustomerInfoRespVo> selectActiveCustomerList(Integer type);

    /**
     * 通用文件上传方法
     * 
     * @param file 上传的文件
     * @param fileType 文件类型（用于确定存储路径，如contract、invoice等）
     * @return 文件访问URL
     */
    String uploadFile(MultipartFile file, String fileType);

    /**
     * 上传货主合同文件
     * 
     * @param file 上传的文件
     * @return 文件访问URL
     */
    String uploadContractFile(MultipartFile file);
}