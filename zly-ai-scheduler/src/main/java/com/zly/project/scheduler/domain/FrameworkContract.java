package com.zly.project.scheduler.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.ClientLog;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目对象 framework_contract
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FrameworkContract extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 项目合约编号 */
    @Excel(name = "项目编号")
    @ApiModelProperty(notes = "项目合约编号", value = "项目合约编号")
    private String contractCode;

    /** 项目合约名称 */
    @Excel(name = "项目名称")
    @ApiModelProperty(notes = "项目合约名称", value = "项目合约编号")
    private String contractName;

    /** 负责人 */
    @Excel(name = "负责人")
    @ApiModelProperty(notes = "负责人", value = "负责人")
    private String responsiblePerson;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(notes = "联系人", value = "联系人")
    private String contactPerson;

    /** 联系方式 */
    @Excel(name = "联系方式")
    @ApiModelProperty(notes = "联系方式", value = "联系方式")
    private String contactPhone;

    /** 项目合约状态 */
    @Excel(name = "项目合约状态")
    @ApiModelProperty(notes = "项目状态", value = "项目状态")
    private Integer contractState;

    /** 费率 */
    @ClientLog
    @Excel(name = "项目对签约主体的费率")
    @ApiModelProperty(notes = "项目对签约主体设置的服务费率", value = "项目对签约主体设置的服务费率")
    private BigDecimal rate;

    /** 项目合约状态（1：启用   2：禁用） */
    @Excel(name = "项目合约状态", readConverterExp = "1=：启用,2=：禁用")
    @ApiModelProperty(notes = "项目合约状态", value = "项目合约状态（1：启用   2：禁用）",allowableValues ="1:启用 , 2:禁用" )
    private Integer state;

    /** 客户id */
    @Excel(name = "客户id")
    @ApiModelProperty(notes = "签约主体托运人的客户id", value = "签约主体托运人的客户id")
    private Long lastCustomerId;

    /** 客户名称 */
    @Excel(name = "签约主体托运人的客户名称")
    @ApiModelProperty(notes = "签约主体托运人的客户名称", value = "签约主体托运人的客户名称")
    private String lastCustomerName;

    /** 法人代表 */
    @Excel(name = "法人代表")
    @ApiModelProperty(notes = "法人代表", value = "法人代表")
    private String legalPerson;

    /** 注册地址 */
    @Excel(name = "注册地址")
    @ApiModelProperty(notes = "注册地址", value = "注册地址")
    private String registerAddr;

    /** 摘要 */
    @Excel(name = "摘要")
    @ApiModelProperty(notes = "摘要", value = "摘要")
    private String abstracts;

    /** 签约时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(notes = "签约时间", value = "签约时间")
    @Excel(name = "签约时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signingTime;

    /** 租户id */
    @Excel(name = "托运人id")
    @ApiModelProperty(notes = "托运人id", value = "托运人id")
    private Long tenantId;


    /** 项目合约审核状态（1：待提交   2：审核中 3：审核未通过 4：审核通过 5：禁用） */
    @Excel(name = "项目合约签约状态（原为审核状态）", readConverterExp = "1=：未提交,2=：签约中,3=：签约失败,4=：签约通过,5=：禁用")
    @ApiModelProperty(notes = "项目合约签约状态（原为审核状态）", value = "项目合约签约状态（原为审核状态）",allowableValues = "1:未提交,2:签约中,3:签约失败,4:签约通过,5:禁用")
    private Integer approveState;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(notes = "开始时间", value = "开始时间")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(notes = "结束时间", value = "结束时间")
    private Date endTime;

    /** 客户合约id */
    @Excel(name = "客户合约id")
    @ApiModelProperty(notes = "客户合约id", value = "客户合约id")
    private Long customerContractId;

    /** 客户合约名称 */
    @Excel(name = "客户合约名称")
    @ApiModelProperty(notes = "客户合约名称", value = "客户合约名称")
    private String customerContractName;

    /** 合约类型 1：自营项目 2：集团转包项目 */
    @Excel(name = "合约类型", readConverterExp = "1：托运人创建项目 2：集团转包项目 3：网络货运人自己的项目")
    @ApiModelProperty(notes = "合约类型", value = " 1：托运人创建项目 2：集团端创建转包项目 3：代运营端创建项目",allowableValues = "1:托运人创建项目 ,2:集团端创建转包项目 ,3:代运营端创建项目")
    private Integer contractType;

    /** 网络货运人id */
    @Excel(name = "网络货运人id")
    @ApiModelProperty(notes = "网络货运人id", value = "网络货运人id")
    private Long freightForwarderId;

    /** 是否为垫资项目： 0 不是 1 是 */
    @Excel(name = "是否为垫资项目", readConverterExp = "0=：不是,1=：是")
    @ApiModelProperty(notes = "是否为垫资项目", value = "是否为垫资项目 0=：不是,1=：是",allowableValues = "0:不是,1:是")
    private Integer isPrepay;

    /** 是否需要审核：0 需要 1 不需要 */
    @Excel(name = "是否需要审核", readConverterExp = "0=：需要,1=：不需要")
    @ApiModelProperty(notes = "是否需要审核", value = "是否需要审核 0=：需要,1=：不需要",allowableValues = "0:需要,1:不需要")
    private Integer needAuditing;

    /** 保险状态：1 审核中 2 审核完成 3 无需审核 */
    @Excel(name = "保险状态：1 审核中 2 审核完成 3 无需审核", readConverterExp = "保险状态：1 审核中 2 审核完成 3 无需审核")
    @ApiModelProperty(notes = "保险状态", value = "保险状态：1 审核中 2 审核完成 3 无需审核",allowableValues = "1:审核中 ,2:审核完成 ,3:无需审核")
    private Integer insuranceState;

    /** 转包链修改生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(notes = "转包链修改生效时间", value = "转包链修改生效时间")
    @Excel(name = "转包链修改生效时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectTime;

    /** 转包链是否已设置： 0 已设置 1 未设置 */
    @Excel(name = "转包链是否已设置", readConverterExp = "0=：已设置,1=：未设置")
    @ApiModelProperty(notes = "转包链是否已设置", value = "转包链是否已设置： 0 已设置 1 未设置",allowableValues = "0:已设置 ,1:未设置")
    private Integer subchainState;

    @ApiModelProperty(value = "业务模式id")
    private Long businessModelId;

    @ApiModelProperty(notes ="小雨点推送状态",value = "小雨点推送状态0无需推送 1推送中 2推送失败 3推送成功",allowableValues = "0:无需推送,1:推送中, 2:推送失败,3:推送成功")
    private Integer xydUploadState;

    @ApiModelProperty(notes = "小雨点审核状态",value = "小雨点审核状态0无需审核 1审核中 2审核不通过 3审核通过",allowableValues = "0:无需审核,1:审核中, 2:审核不通过,3:审核通过")
    private Integer xydApproveState;

    @ApiModelProperty(notes = "模式类型（固定）",value = "模式类型（固定） 1：标准 2：托盘 3：代运营  4：小雨点融资 5：下游垫资  6：上游垫资",allowableValues = "1:标准 ,2:托盘 ,3:代运营  ,4:小雨点融资 ,5:下游垫资  ,6:上游垫资")
    private Integer modelClassify;

	@ApiModelProperty(notes = "是否上传税务",value = "是否上传税务，0：是，1：否",allowableValues = "0:是,1:否")
    private Integer isTaxUploaded;

    @ApiModelProperty(value = "运单实时性时间")
    private Integer effectiveTime;

    @ApiModelProperty(notes = "时间单位",value = "时间单位 0天 1小时",allowableValues = "0:天,1:小时")
    private Integer timeUnit;
}
