package com.zly.project.scheduler.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zly.common.constant.ApiUrlConstants;
import com.zly.common.constant.HttpStatus;
import com.zly.common.utils.RemoteApiUtil;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.cargo.service.impl.FrequentCargoServiceImpl;
import com.zly.project.scheduler.controller.vo.CustomerInfoPageReqVo;
import com.zly.project.scheduler.controller.vo.CustomerInfoRes;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import com.zly.project.scheduler.controller.vo.RemoteCustomerInfoVo;
import com.zly.project.scheduler.service.ApiCustomerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.BASE_SERVER_ERROR;

/**
 * 货主信息远程接口服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ApiCustomerInfoServiceImpl implements ApiCustomerInfoService {
    @Resource
    private RemoteApiUtil remoteApiUtil;

    @Override
    public TableDataInfo<RemoteCustomerInfoVo> getCustomerInfoList(CustomerInfoPageReqVo pageReqVo) {
        try {
            log.info("开始调用获取货主信息列表接口，请求参数：{}", pageReqVo);
            JSONObject jsonObject = remoteApiUtil.postWithSign(ApiUrlConstants.getBaseUrl()+ApiUrlConstants.CUSTOMER_INFO_LIST, pageReqVo);
            String code = jsonObject.getStr("code");
            if ("200".equals(code)) {
                // 正常返回
                long total = jsonObject.getLong("total", 0L);
                Object rowsObj = jsonObject.get("rows");
                return new TableDataInfo<>(JSONUtil.toList(JSONUtil.toJsonStr(rowsObj), RemoteCustomerInfoVo.class), total, HttpStatus.SUCCESS, "查询成功");
            } else {
                String msg = jsonObject.getStr("msg", "远程接口调用失败");
                throw exception(BASE_SERVER_ERROR, msg);
            }
        } catch (Exception e) {
            log.error("调用获取货主信息列表接口异常", e);
            throw exception(BASE_SERVER_ERROR, "调用获取货主信息列表接口异常: " + e.getMessage());
        }
    }

    @Override
    public CommonResult<CustomerInfoRes> getCustomerInfoById(Long id) {
        try {
            log.info("开始调用获取托运人信息详情接口，ID: {}", id);
            // 构建请求URL
            String url = ApiUrlConstants.getBaseUrl() + ApiUrlConstants.CUSTOMER_INFO_DETAIL + "/" + id;

            // 使用GET请求获取详情，需要携带token
            java.util.Map<String, Object> params = new java.util.HashMap<>();
            JSONObject jsonObject = remoteApiUtil.getWithSign(url, params);

            String code = jsonObject.getStr("code");
            if ("200".equals(code)) {
                // 正常返回
                Object dataObj = jsonObject.get("data");
                if (dataObj != null) {
                    CustomerInfoRes customerInfo = JSONUtil.toBean(JSONUtil.toJsonStr(dataObj), CustomerInfoRes.class);
                    log.info("成功获取托运人信息详情");
                    return CommonResult.success(customerInfo);
                } else {
                    log.warn("托运人信息详情数据为空");
                    return CommonResult.error("托运人信息详情数据为空");
                }
            } else {
                String msg = jsonObject.getStr("msg", "远程接口调用失败");
                log.error("获取托运人信息详情失败: {}", msg);
                return CommonResult.error(msg);
            }
        } catch (Exception e) {
            log.error("调用获取托运人信息详情接口异常，ID: " + id, e);
            return CommonResult.error("调用获取托运人信息详情接口异常: " + e.getMessage());
        }
    }

    @Override
    public CustomerInfoRes getCustomerRespVoInfoById(Long customerId) {
        CommonResult<CustomerInfoRes> customerInfoById = getSelf().getCustomerInfoById(customerId);
        if (customerInfoById.isSuccess()){
            return customerInfoById.getData();
        }else {
            throw exception(BASE_SERVER_ERROR, "货主信息不存在");
        }
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private ApiCustomerInfoServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}