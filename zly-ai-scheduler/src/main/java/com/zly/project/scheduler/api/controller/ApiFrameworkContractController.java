package com.zly.project.scheduler.api.controller;

import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.scheduler.api.vo.ContractSimpleRes;
import com.zly.project.scheduler.api.vo.ContractSrarchReq;
import com.zly.project.scheduler.api.vo.FrameworkContractRes;
import com.zly.project.scheduler.service.ApiFrameworkContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 项目合约管理
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Api(value = "项目合约管理--v4.3.17", tags = "项目合约管理--v4.3.17")
@RestController
@RequestMapping("/framework/contract")
public class ApiFrameworkContractController extends BaseController {
	@Resource
	private ApiFrameworkContractService apiFrameworkContractService;

	@PreAuthorize("@ss.hasPermi('api:framework:contract:platformList')")
	@PostMapping("/platform/list")
	@ApiOperation(value = "平台PC端查询项目合约列表", notes = "平台PC端查询项目合约列表")
	public TableDataInfo<FrameworkContractRes> platformList(@RequestBody ContractSrarchReq req) {
		return apiFrameworkContractService.platformList(req);
	}

	@PreAuthorize("@ss.hasPermi('api:framework:contract:platformSearchList')")
	@PostMapping("/platform/platformSearchList")
	@ApiOperation(value = "平台端项目合约查询框列表", notes = "平台PC端查询项目合约列表")
	public CommonResult<List<ContractSimpleRes>> platformSearchList(String projectName,Long resourceCustomerId) {
		return apiFrameworkContractService.platformSearchList(projectName,resourceCustomerId);
	}
}
