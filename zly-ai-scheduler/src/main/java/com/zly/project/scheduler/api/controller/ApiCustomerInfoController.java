package com.zly.project.scheduler.api.controller;

import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.scheduler.controller.vo.CustomerInfoPageReqVo;
import com.zly.project.scheduler.controller.vo.CustomerInfoRes;
import com.zly.project.scheduler.controller.vo.RemoteCustomerInfoVo;
import com.zly.project.scheduler.service.ApiCustomerInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 货主信息接口 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("api/scheduler/customer/info")
public class ApiCustomerInfoController {

    @Resource
    private ApiCustomerInfoService apiCustomerInfoService;

    /**
     * 获取货主信息列表
     *
     * @param pageReqVo 分页请求参数
     * @return 货主信息列表
     */
    @PreAuthorize("@ss.hasPermi('api:customer:info:list')")
    @PostMapping("/list")
    public TableDataInfo<RemoteCustomerInfoVo> list(@RequestBody CustomerInfoPageReqVo pageReqVo) {
        return apiCustomerInfoService.getCustomerInfoList(pageReqVo);
    }

    /**
     * 根据ID获取托运人信息详细信息
     *
     * @param id 托运人ID
     * @return 托运人详细信息
     */
    @PreAuthorize("@ss.hasPermi('api:customer:info:query')")
    @GetMapping("/info/{id}")
    public CommonResult<CustomerInfoRes> getInfo(@PathVariable("id") Long id) {
        return apiCustomerInfoService.getCustomerInfoById(id);
    }
}