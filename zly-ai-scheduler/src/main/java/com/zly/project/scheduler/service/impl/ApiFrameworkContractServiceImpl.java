package com.zly.project.scheduler.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zly.common.constant.ApiUrlConstants;
import com.zly.common.constant.HttpStatus;
import com.zly.common.utils.RemoteApiUtil;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.scheduler.api.vo.ContractSimpleRes;
import com.zly.project.scheduler.api.vo.ContractSrarchReq;
import com.zly.project.scheduler.api.vo.FrameworkContractRes;
import com.zly.project.scheduler.service.ApiFrameworkContractService;
import com.zly.project.system.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.BASE_SERVER_ERROR;

/**
 * @ClassName: ApiFrameworkContractServiceImpl
 * @Description: 项目签约服务类
 * @Author: cdh
 * @Date: 2025/7/16 16:34
 **/
@Slf4j
@Service
public class ApiFrameworkContractServiceImpl implements ApiFrameworkContractService {

	@Resource
	private RemoteApiUtil remoteApiUtil;

	@Resource
	private ObjectMapper objectMapper;

	@Override
	public TableDataInfo<FrameworkContractRes> platformList(ContractSrarchReq req) {
		try {
			log.info("开始调用签名签约列表接口，请求参数：{}", req);
			JSONObject jsonObject = remoteApiUtil.postWithSign(ApiUrlConstants.getBaseUrl() + ApiUrlConstants.PLATFORM_LIST, req);
			String code = jsonObject.getStr("code");
			if ("200".equals(code)) {
				// 正常返回
				long total = jsonObject.getLong("total", 0L);
				Object rowsObj = jsonObject.get("rows");
				return new TableDataInfo(JSONUtil.toList(JSONUtil.toJsonStr(rowsObj), FrameworkContractRes.class), total, HttpStatus.SUCCESS,"查询成功");
			} else {
				String msg = jsonObject.getStr("msg", "远程接口调用失败");
				throw exception(BASE_SERVER_ERROR, msg);
			}
		} catch (Exception e) {
			log.error("调用获取项目签约列表接口异常", e);
			throw exception(BASE_SERVER_ERROR, "调用获取项目签约列表接口异常: " + e.getMessage());
		}
	}

	@Override
	public CommonResult<List<ContractSimpleRes>> platformSearchList(String projectName, Long resourceCustomerId) {
		try {
			log.info("开始调用签名签约列表接口，请求参数：projectName:{},resourceCustomerId:{}", projectName,resourceCustomerId);
			HashMap<String, Object> param = new HashMap<>();
			if (StringUtils.isNotBlank(projectName)){
				param.put("projectName",projectName);
			}
			if (resourceCustomerId != null){
				param.put("resourceCustomerId",resourceCustomerId);
			}
			JSONObject jsonObject = remoteApiUtil.postWithSign(ApiUrlConstants.getBaseUrl() + ApiUrlConstants.PLATFORM_SEARCH_LIST, null, param);
			CommonResult<List<ContractSimpleRes>> resultObj = objectMapper.readValue(jsonObject.toString(), new TypeReference<CommonResult<List<ContractSimpleRes>>>() {});
			if (resultObj != null && resultObj.isSuccess())
			{
				log.info("平台端项目合约查询框列表,远程接口调用成功，返回数据: {}", resultObj);
				return resultObj;
			}
			else
			{
				String errorMsg = resultObj != null ? resultObj.getMsg() : "远程接口返回异常";
				log.error("平台端项目合约查询框列表,远程接口调用失败: {}", errorMsg);
				return CommonResult.error(errorMsg);
			}
		} catch (Exception e) {
			log.error("平台端项目合约查询框列表接口异常", e);
			throw exception(BASE_SERVER_ERROR, "平台端项目合约查询框列表接口异常: " + e.getMessage());
		}
	}
}
