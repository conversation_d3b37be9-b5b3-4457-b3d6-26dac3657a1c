package com.zly.project.scheduler.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 货主合约续约请求VO
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
public class CustomerContractRenewVo {
    
    /**
     * 货主ID
     */
    @NotNull(message = "货主ID不能为空")
    @ApiModelProperty(value = "货主ID")
    private Long customerId;
    
    /**
     * 合约开始日期
     */
    @NotNull(message = "合约开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "合约开始日期")
    private Date startDate;
    
    /**
     * 合约结束日期
     */
    @NotNull(message = "合约结束日期不能为空")
    @ApiModelProperty(value = "合约结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    
    /**
     * 合同附件路径
     */
    @ApiModelProperty(value = "合同附件路径")
    private String attachmentPath;
} 