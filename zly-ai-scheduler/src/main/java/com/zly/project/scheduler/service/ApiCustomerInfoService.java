package com.zly.project.scheduler.service;

import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.scheduler.controller.vo.CustomerInfoPageReqVo;
import com.zly.project.scheduler.controller.vo.CustomerInfoRes;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import com.zly.project.scheduler.controller.vo.RemoteCustomerInfoVo;

/**
 * 货主信息远程接口服务
 *
 * <AUTHOR>
 */
public interface ApiCustomerInfoService {

    /**
     * 获取货主信息列表
     *
     * @param pageReqVo 分页请求参数
     * @return 货主信息列表和分页数据
     */
    TableDataInfo<RemoteCustomerInfoVo> getCustomerInfoList(CustomerInfoPageReqVo pageReqVo);

    /**
     * 根据ID获取托运人信息详细信息
     *
     * @param id 托运人ID
     * @return 托运人详细信息
     */
    CommonResult<CustomerInfoRes> getCustomerInfoById(Long id);

    /**
     * 根据ID获取托运人信息详细信息
     * @param customerId
     * @return
     */
    CustomerInfoRes getCustomerRespVoInfoById(Long customerId);
}