package com.zly.project.tool.gen.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlCreateTableStatement;
import com.zly.common.core.text.Convert;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.sql.SqlUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.config.GenConfig;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.tool.gen.domain.GenTable;
import com.zly.project.tool.gen.domain.GenTableColumn;
import com.zly.project.tool.gen.service.IGenTableColumnService;
import com.zly.project.tool.gen.service.IGenTableService;

/**
 * 代码生成 操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tool/gen")
public class GenController extends BaseController {
	@Autowired
	private IGenTableService genTableService;

	@Autowired
	private IGenTableColumnService genTableColumnService;

	/**
	 * 查询代码生成列表
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:list')")
	@GetMapping("/list")
	public TableDataInfo genList(GenTable genTable) {
		startPage();
		List<GenTable> list = genTableService.selectGenTableList(genTable);
		return getDataTable(list);
	}

	/**
	 * 获取代码生成信息
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:query')")
	@GetMapping(value = "/{talbleId}")
	public AjaxResult getInfo(@PathVariable Long talbleId) {
		GenTable table = genTableService.selectGenTableById(talbleId);
		List<GenTable> tables = genTableService.selectGenTableAll();
		List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(talbleId);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("info", table);
		map.put("rows", list);
		map.put("tables", tables);
		return success(map);
	}

	/**
	 * 查询数据库列表
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:list')")
	@GetMapping("/db/list")
	public TableDataInfo dataList(GenTable genTable) {
		startPage();
		List<GenTable> list = genTableService.selectDbTableList(genTable);
		return getDataTable(list);
	}

	/**
	 * 查询数据表字段列表
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:list')")
	@GetMapping(value = "/column/{tableId}")
	public TableDataInfo columnList(Long tableId) {
		TableDataInfo dataInfo = new TableDataInfo();
		List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(tableId);
		dataInfo.setRows(list);
		dataInfo.setTotal(list.size());
		return dataInfo;
	}

	/**
	 * 导入表结构（保存）
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:import')")
	@Log(title = "代码生成", businessType = BusinessType.IMPORT)
	@PostMapping("/importTable")
	public AjaxResult importTableSave(String tables) {
		String[] tableNames = Convert.toStrArray(tables);
		// 查询表信息
		List<GenTable> tableList = genTableService.selectDbTableListByNames(tableNames);
		genTableService.importGenTable(tableList, SecurityUtils.getUsername());
		return success();
	}

	/**
	 * 创建表结构（保存）
	 */
	@PreAuthorize("@ss.hasRole('admin')")
	@Log(title = "创建表", businessType = BusinessType.OTHER)
	@PostMapping("/createTable")
	public AjaxResult createTableSave(String sql) {
		try {
			SqlUtil.filterKeyword(sql);
			List<SQLStatement> sqlStatements = SQLUtils.parseStatements(sql, DbType.mysql);
			List<String> tableNames = new ArrayList<>();
			for (SQLStatement sqlStatement : sqlStatements) {
				if (sqlStatement instanceof MySqlCreateTableStatement) {
					MySqlCreateTableStatement createTableStatement = (MySqlCreateTableStatement) sqlStatement;
					if (genTableService.createTable(createTableStatement.toString())) {
						String tableName = createTableStatement.getTableName().replaceAll("`", "");
						tableNames.add(tableName);
					}
				}
			}
			List<GenTable> tableList = genTableService.selectDbTableListByNames(tableNames.toArray(new String[tableNames.size()]));
			String operName = SecurityUtils.getUsername();
			genTableService.importGenTable(tableList, operName);
			return AjaxResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return AjaxResult.error("创建表结构异常");
		}
	}

	/**
	 * 修改保存代码生成业务
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:edit')")
	@Log(title = "代码生成", businessType = BusinessType.UPDATE)
	@PutMapping
	public AjaxResult editSave(@Validated @RequestBody GenTable genTable) {
		genTableService.validateEdit(genTable);
		genTableService.updateGenTable(genTable);
		return success();
	}

	/**
	 * 删除代码生成
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:remove')")
	@Log(title = "代码生成", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tableIds}")
	public AjaxResult remove(@PathVariable Long[] tableIds) {
		genTableService.deleteGenTableByIds(tableIds);
		return success();
	}

	/**
	 * 预览代码
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:preview')")
	@GetMapping("/preview/{tableId}")
	public AjaxResult preview(@PathVariable("tableId") Long tableId) throws IOException {
		Map<String, String> dataMap = genTableService.previewCode(tableId);
		return success(dataMap);
	}

	/**
	 * 生成代码（下载方式）
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:code')")
	@Log(title = "代码生成", businessType = BusinessType.GENCODE)
	@GetMapping("/download/{tableName}")
	public void download(HttpServletResponse response, @PathVariable("tableName") String tableName) throws IOException {
		byte[] data = genTableService.downloadCode(tableName);
		genCode(response, data);
	}

	/**
	 * 生成代码（自定义路径）
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:code')")
	@Log(title = "代码生成", businessType = BusinessType.GENCODE)
	@GetMapping("/genCode/{tableName}")
	public AjaxResult genCode(@PathVariable("tableName") String tableName) {
		if (!GenConfig.isAllowOverwrite()) {
			return AjaxResult.error("【系统预设】不允许生成文件覆盖到本地");
		}
		genTableService.generatorCode(tableName);
		return success();
	}

	/**
	 * 同步数据库
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:edit')")
	@Log(title = "代码生成", businessType = BusinessType.UPDATE)
	@GetMapping("/synchDb/{tableName}")
	public AjaxResult synchDb(@PathVariable("tableName") String tableName) {
		genTableService.synchDb(tableName);
		return success();
	}

	/**
	 * 批量生成代码
	 */
	@PreAuthorize("@ss.hasPermi('tool:gen:code')")
	@Log(title = "代码生成", businessType = BusinessType.GENCODE)
	@GetMapping("/batchGenCode")
	public void batchGenCode(HttpServletResponse response, String tables) throws IOException {
		String[] tableNames = Convert.toStrArray(tables);
		byte[] data = genTableService.downloadCode(tableNames);
		genCode(response, data);
	}

	/**
	 * 生成zip文件
	 */
	private void genCode(HttpServletResponse response, byte[] data) throws IOException {
		response.reset();
		response.addHeader("Access-Control-Allow-Origin", "*");
		response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
		response.setHeader("Content-Disposition", "attachment; filename=\"zly.zip\"");
		response.addHeader("Content-Length", "" + data.length);
		response.setContentType("application/octet-stream; charset=UTF-8");
		IOUtils.write(data, response.getOutputStream());
	}
}