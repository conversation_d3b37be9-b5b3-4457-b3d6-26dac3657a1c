package com.zly.project.outbound.domain;

import java.util.List;

import lombok.Data;

/**
 * 添加号码
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
public class TaskNumber {

	private String task_id; // 任务id

	private List<TaskDetail> data;// 电话呼叫内容

	@Data
	public static class TaskDetail {
		private String name;// 任务名称
		private String phone;// 电话
		private Integer sort;// 排序
		private String extra;// 扩展信息
		private List<TaskComponents> components;// 语音内容
	}

	@Data
	public static class TaskComponents {
		private String id;// 变量ID
		private String value;// 变量值
	}

}
