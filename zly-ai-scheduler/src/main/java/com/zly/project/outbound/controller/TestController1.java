package com.zly.project.outbound.controller;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.zly.common.core.MsgModel;
import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.match.domain.DispatchCallDO;
import com.zly.project.outbound.domain.ResetNumber;
import com.zly.project.outbound.domain.TaskNumber;
import com.zly.project.outbound.service.IBeeBirdService;

@Anonymous
@RestController
@RequestMapping("/apitest")
public class TestController1 extends BaseController {

	@Resource
	private IBeeBirdService beeBirdService;

	@GetMapping("/getFieldList")
	public AjaxResult getFieldList() {
		return success(beeBirdService.getFieldList());
	}

	@GetMapping("/getTaskList")
	public AjaxResult getTaskList(Integer status) {
		return success(beeBirdService.getTaskList(status));
	}

	@GetMapping("/taskStart/{taskId}")
	public AjaxResult taskStart(@PathVariable("taskId") String taskId) {
		return beeBirdService.taskStart(taskId);
	}

	@GetMapping("/taskStop/{taskId}")
	public AjaxResult taskStop(@PathVariable("taskId") String taskId) {
		return beeBirdService.taskStop(taskId);
	}

	@PostMapping("/taskNumber")
	public AjaxResult taskNumber(@RequestBody TaskNumber taskNumber) {
		return beeBirdService.taskNumber(taskNumber);
	}

	@PostMapping("/resetNumber")
	public AjaxResult resetNumber(@RequestBody ResetNumber resetNumber) {
		return beeBirdService.resetNumber(resetNumber);
	}

	@PostMapping("/deleteNumber")
	public AjaxResult deleteNumber(@RequestBody ResetNumber resetNumber) {
		return beeBirdService.deleteNumber(resetNumber);
	}

	@GetMapping("/getCallOrder/{taskId}/{callId}")
	public AjaxResult getCallOrder(@PathVariable("taskId") String taskId, @PathVariable("callId") String callId) {
		return beeBirdService.getCallOrder(taskId, callId);
	}

	@GetMapping("/delTask/{taskId}")
	public AjaxResult delTask(@PathVariable("taskId") String taskId) {
		return beeBirdService.delTask(taskId);
	}

	@GetMapping("/downloadVoiceUrl")
	public AjaxResult downloadVoiceUrl() {
		DispatchCallDO dispatchCallDO = new DispatchCallDO();
		dispatchCallDO.setExternalTaskId("b7e89cfb-3e2e-4a7a-8a13-a93dc2ace907");
		dispatchCallDO.setId(402L);
		String voiceUrl = beeBirdService.downloadVoiceUrl(dispatchCallDO,
				"47.111.5.195:39999/api/agent-api/download-voice?key=VzhYUWx1VmJnQmxZTi8zSGc3OGNZL29vYXVUSXFYa1VaSzFzTHFKdTRrV2Fod2R6d0lhNzlqQUZ3VGFnRTNiV2o2SmNwYzJYV0F2UWxxVnR4R284U0NFdVF6UlpQeksvRnBYcFcrUUFrdi92Vm5tZTBRaDI5dlBSYXpPd1JFSDFDa1czbnJCVVR5a2JwbjVVWEQwTExKZ0tBYlcrVmdqaGpreHdrVlRDNEhoV040SndWTTgxenFmajZjbFlEcWd1NXlHU2tHWVlzaTVyaWUrRG1GdndDdz09");
		return success(voiceUrl);
	}

	public static void main(String[] args) throws Exception {
		String dir = "C:\\Users\\<USER>\\Desktop\\";
		String filePath = dir + "3.0pwd.txt";
		Path path = Paths.get(filePath);
		List<String> pwdList = Files.readAllLines(path);
		for (String pwd : pwdList) {
			if (pwd.startsWith("3.0")) {
				String name = pwd.substring(pwd.indexOf("【") + 1, pwd.indexOf("，账号")) + "您好，您的智联云ITMS3.0系统";
				name = name.replace("】，用户姓名", "");
				String account = pwd.substring(pwd.indexOf("账号"), pwd.indexOf("，手机号")) + "疑似使用了弱密码。为了您账号和密码的安全性，防范因弱密码引发的账户泄露及资金安全风险，请登录系统修改密码。登录地址：https://itms.56zly.com/";
				String mobile = pwd.substring(pwd.lastIndexOf("【") + 1, pwd.lastIndexOf("】"));
				if (StringUtils.isNotBlank(mobile)) {
					System.out.println(name + account);
					MsgModel model = new MsgModel();
					model.setPhone(mobile);
					model.setContent(name + account);
					model.setTaxNo("");
					String json = JSON.toJSONString(model);
					Map<String, String> map = new HashMap<>();
					map.put("acc", "tms");
					map.put("pas", "123");
					// String msgInfo = HttpClientUtils.postHttp("https://sms.56zly.com/sms/send", json, map);
					// if (StringUtils.isNotBlank(msgInfo)) {
					// System.out.println(mobile + "短信已发送");
					// }
				}
			} else {
				String name = pwd.substring(pwd.indexOf("【") + 1, pwd.indexOf("，账号")) + "您好，您的智联云4.0公路运输管理系统";
				name = name.replace("】，用户昵称", "");
				String account = pwd.substring(pwd.indexOf("账号")) + "疑似使用了弱密码。为了您账号和密码的安全性，防范因弱密码引发的账户泄露及资金安全风险，请登录系统修改密码。登录地址：https://ty.56zly.com/";
				String mobile = pwd.substring(pwd.lastIndexOf("【") + 1, pwd.lastIndexOf("】"));
				if (StringUtils.isNotBlank(mobile)) {
					System.out.println(name + account);
					MsgModel model = new MsgModel();
					model.setPhone(mobile);
					model.setContent(name + account);
					model.setTaxNo("");
					String json = JSON.toJSONString(model);
					Map<String, String> map = new HashMap<>();
					map.put("acc", "tms");
					map.put("pas", "123");
					// String msgInfo = HttpClientUtils.postHttp("https://sms.56zly.com/sms/send", json, map);
					// if (StringUtils.isNotBlank(msgInfo)) {
					// System.out.println(mobile + "短信已发送");
					// }
				}
			}
		}
	}
}
