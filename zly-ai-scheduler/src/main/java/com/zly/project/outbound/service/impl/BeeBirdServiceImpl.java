package com.zly.project.outbound.service.impl;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson2.JSONArray;
import com.qiniu.http.Response;
import com.zly.common.utils.QiNiuUtil;
import com.zly.project.match.domain.DispatchCallDO;
import com.zly.project.match.domain.TaskDriverMatchDO;
import com.zly.project.match.mapper.TaskDriverMatchMapper;
import com.zly.project.match.service.IDispatchCallService;
import com.zly.project.outbound.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.common.utils.http.HttpUtils;
import com.zly.framework.config.CustomConfig;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.outbound.domain.CallPush.Tags;
import com.zly.project.outbound.service.IBeeBirdService;
import com.zly.project.scheduler.domain.SchedulerCallPush;
import com.zly.project.scheduler.mapper.SchedulerCallPushMapper;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.BASE_SERVER_ERROR;

/**
 * 外呼系统接口服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class BeeBirdServiceImpl implements IBeeBirdService {

	protected final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	private CustomConfig customConfig;

	@Autowired
	private SchedulerCallPushMapper schedulerCallPushMapper;

	@Resource
	private IDispatchCallService dispatchCallService;
	@Resource
	private TaskDriverMatchMapper taskDriverMatchMapper;

	/**
	 * 获取蜂鸟迅达变量名
	 * 
	 * @return
	 */
	@Override
	public List<VariableField> getFieldList() {
		List<VariableField> list = new ArrayList<VariableField>();
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdGetField();
		String result = HttpUtils.sendGet(url);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		if (200 == code.intValue()) {
			list = JSON.parseArray(resultJson.getString("data"), VariableField.class);
		}
		return list;
	}

	/**
	 * 获取蜂鸟迅达任务列表 status 1：暂停，3：执行中，不指定返回所有任务数据
	 */
	@Override
	public List<Task> getTaskList(Integer status) {
		List<Task> list = new ArrayList<Task>();
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdTaskList();
		String result = HttpUtils.sendGet(url);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		if (200 == code.intValue()) {
			JSONObject dataJson = JSON.parseObject(resultJson.getString("data"));
			list = JSON.parseArray(dataJson.getString("list"), Task.class);
			// 只返回指定状态的任务，任务状态 1：暂停，3：执行中
			if (null != status && null != list && !list.isEmpty()) {
				list = list.stream().filter(p -> p.getStatus().equals(status)).collect(Collectors.toList());
			}
		}
		return list;
	}

	/**
	 * 蜂鸟迅达开始任务
	 */
	@Override
	public AjaxResult taskStart(String taskId) {
		if (StringUtils.isBlank(taskId)) {
			return AjaxResult.error("请选择要开始的任务");
		}
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdTaskStart();
		String param = "task_id=" + taskId;
		String result = HttpUtils.sendGet(url, param);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		return 200 == code.intValue() ? AjaxResult.success() : AjaxResult.error(resultJson.getString("message"));
	}

	/**
	 * 蜂鸟迅达暂停任务
	 */
	@Override
	public AjaxResult taskStop(String taskId) {
		if (StringUtils.isBlank(taskId)) {
			return AjaxResult.error("请选择要暂停的任务");
		}
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdTaskStop();
		String param = "task_id=" + taskId;
		String result = HttpUtils.sendGet(url, param);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		return 200 == code.intValue() ? AjaxResult.success() : AjaxResult.error(resultJson.getString("message"));
	}

	/**
	 * 蜂鸟迅达添加号码
	 */
	@Override
	public AjaxResult taskNumber(TaskNumber taskNumber) {
		if (null == taskNumber || StringUtils.isBlank(taskNumber.getTask_id()) || null == taskNumber.getData()) {
			return AjaxResult.error("参数错误");
		}
		if (taskNumber.getData().size() > 100) {
			return AjaxResult.error("每次最多只能导入100条数据");
		}
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdTaskNumber();
		String param = JSON.toJSONString(taskNumber);
		String result = HttpUtils.sendPost(url, param);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		return 200 == code.intValue() ? AjaxResult.success(resultJson.getString("message"), resultJson.getString("data")) : AjaxResult.error(resultJson.getString("message"));
	}

	@Override
	public AjaxResult taskNumberNew(TaskNumber taskNumber) {
		if (null == taskNumber || StringUtils.isBlank(taskNumber.getTask_id()) || null == taskNumber.getData()) {
			return AjaxResult.error("参数错误");
		}
		if (taskNumber.getData().size() > 100) {
			return AjaxResult.error("每次最多只能导入100条数据");
		}
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdTaskNumberNew();
		String param = JSON.toJSONString(taskNumber);
		String result = HttpUtils.sendPost(url, param);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		return 200 == code.intValue() ? AjaxResult.success(resultJson.getString("message"), resultJson.getString("data")) : AjaxResult.error(resultJson.getString("message"));
	}

	/**
	 * 蜂鸟迅达重置号码
	 */
	@Override
	public AjaxResult resetNumber(ResetNumber resetNumber) {
		if (null == resetNumber || StringUtils.isBlank(resetNumber.getTask_id()) || null == resetNumber.getData() || resetNumber.getData().size() == 0) {
			return AjaxResult.error("参数错误");
		}
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdResetNumber();
		String param = JSON.toJSONString(resetNumber);
		String result = HttpUtils.sendPost(url, param);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		return 200 == code.intValue() ? AjaxResult.success() : AjaxResult.error(resultJson.getString("message"));
	}

	/**
	 * 蜂鸟迅达删除号码
	 */
	@Override
	public AjaxResult deleteNumber(ResetNumber resetNumber) {
		if (null == resetNumber || StringUtils.isBlank(resetNumber.getTask_id()) || null == resetNumber.getData() || resetNumber.getData().size() == 0) {
			return AjaxResult.error("参数错误");
		}
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdDeleteNumber();
		String param = JSON.toJSONString(resetNumber);
		String result = HttpUtils.sendPost(url, param);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		return 200 == code.intValue() ? AjaxResult.success() : AjaxResult.error(resultJson.getString("message"));
	}

	/**
	 * 话单推送 flag 0：失败 1：成功
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public int callPush(int status, CallPush callPush) {
		logger.info("话单推送,状态:{},{}", String.valueOf(status), JSON.toJSONString(callPush));
		SchedulerCallPush schedulerCallPush = new SchedulerCallPush();
		BeanUtils.copyProperties(callPush, schedulerCallPush);
		schedulerCallPush.setUserId(callPush.getUser_id());
		schedulerCallPush.setGroupId(callPush.getGroup().getId());
		schedulerCallPush.setGroupName(callPush.getGroup().getName());
		schedulerCallPush.setTaskId(callPush.getTask().getId());
		schedulerCallPush.setTaskName(callPush.getTask().getName());
		schedulerCallPush.setCustomerId(callPush.getCustomer_id());
		schedulerCallPush.setTelephone(callPush.getNumber_data().getNumber());
		if (null != callPush.getNumber_data().getHangupdate()) {
			schedulerCallPush.setHangupdate(callPush.getNumber_data().getHangupdate());
		}
		schedulerCallPush.setProvince(callPush.getNumber_data().getProvince());
		schedulerCallPush.setCity(callPush.getNumber_data().getCity());
		schedulerCallPush.setOperator(callPush.getNumber_data().getOperator());
		schedulerCallPush.setStatus(status);
		schedulerCallPush.setStatusStr(StringUtils.isBlank(callPush.getStatus_str()) ? "" : callPush.getStatus_str());
		schedulerCallPush.setIntentionResults(callPush.getIntention_results());
		schedulerCallPush.setLevelId(null == callPush.getLevel() || StringUtils.isBlank(callPush.getLevel().getLevel_id()) ? "" : callPush.getLevel().getLevel_id());
		schedulerCallPush.setLevelName(null == callPush.getLevel() || StringUtils.isBlank(callPush.getLevel().getLevel_name()) ? "" : callPush.getLevel().getLevel_name());
		if (null != callPush.getTags() && !callPush.getTags().isEmpty()) {
			List<String> tagList = new ArrayList<>();
			for (Tags tag : callPush.getTags()) {
				tagList.add(tag.getName());
			}
			String result = String.join(",", tagList);
			schedulerCallPush.setTags(result);
		}
		schedulerCallPush.setVoiceUrl(StringUtils.isBlank(callPush.getVoice_url()) ? "" : callPush.getVoice_url());
		String extra = callPush.getCustomer_data().getExtra();// 获取我方系统业务标识，收到后做相关业务功能 TODO
		schedulerCallPush.setExtra(extra);
		// 蜂鸟迅达获取话单录音文字
		if (status == 1) {
			String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdGetCallOrder();
			String param = "task_id=" + schedulerCallPush.getTaskId() + "&callid=" + schedulerCallPush.getCallid();
			String result = HttpUtils.sendGet(url, param);
			JSONObject resultJson = JSON.parseObject(result);
			Integer code = resultJson.getInteger("code");
			if (200 == code.intValue()) {
				schedulerCallPush.setVoiceText(resultJson.getString("data"));
			}
		}
		// 根据唯一标识extra去 scheduler_dispatch_call 调度呼叫记录表 查询该条记录数据
		handleSchedulerCallPush(status, extra, schedulerCallPush);
		SchedulerCallPush scp = new SchedulerCallPush();
		scp.setTaskId(schedulerCallPush.getTaskId());
		scp.setCallid(schedulerCallPush.getCallid());
		List<SchedulerCallPush> list = schedulerCallPushMapper.selectSchedulerCallPushList(scp);
		if (null != list && !list.isEmpty()) {
			for (SchedulerCallPush tmp : list) {
				schedulerCallPushMapper.deleteSchedulerCallPushById(tmp.getId());
			}
		}
		int rows = schedulerCallPushMapper.insertSchedulerCallPush(schedulerCallPush);
		return rows;
	}

	/**
	 * 处理调度回调数据
	 * @param status
	 * @param extra
	 * @param schedulerCallPush
	 */
	private void handleSchedulerCallPush(int status, String extra, SchedulerCallPush schedulerCallPush) {
		log.info("handleSchedulerCallPush,status:{},extra:{},schedulerCallPush:{}",status,extra,schedulerCallPush);
		try {
			// 根据extra查询调度呼叫记录
			DispatchCallDO dispatchCall = dispatchCallService.getDispatchCallByExtra(extra);
			if (dispatchCall != null) {
				TaskDriverMatchDO driverMatch = taskDriverMatchMapper.selectTaskDriverMatchById(dispatchCall.getTaskMatchId());
				Integer isOutbound = driverMatch.getIsOutbound();
				if (4 == isOutbound || 3 == isOutbound){
					log.info("handleSchedulerCallPush表示已经处理过回调数据了，一般是呼叫超时的数据需要手动调接口查询,isOutbound:{}",isOutbound);
					return;
				}
				// 判断话单状态 成功还是失败
				if (status == 1) { // 成功
					// 更新调度呼叫记录
					handleSchedulerSuccess(schedulerCallPush, dispatchCall);
				} else {
					handleSchedulerError(schedulerCallPush, dispatchCall);
				}
				logger.info("处理外呼回调成功，extra:{}, status:{}", extra, status);
			} else {
				logger.error("未找到对应的调度呼叫记录，extra:{}", extra);
				throw exception(BASE_SERVER_ERROR, "未找到对应的调度呼叫记录");
			}
		} catch (Exception e) {
			logger.error("处理外呼回调异常", e);
			throw exception(BASE_SERVER_ERROR, "处理外呼回调异常");
		}
	}

	/**
	 * 处理失败回调
	 * @param schedulerCallPush
	 * @param dispatchCall
	 */
	private void handleSchedulerError(SchedulerCallPush schedulerCallPush, DispatchCallDO dispatchCall) {
		log.info("handleSchedulerError,dispatchCall:{}",dispatchCall);
		// 更新调度呼叫记录
		dispatchCall.setCallStatus("呼叫失败");
		dispatchCall.setRemark(schedulerCallPush.getStatusStr()); // 记录失败原因
		dispatchCall.setHasIntention(0); // 失败的情况下无意向
		// 更新调度呼叫记录
		dispatchCallService.updateDispatchCall(dispatchCall);
		// 更新匹配司机状态
		if (dispatchCall.getTaskMatchId() != null) {
			TaskDriverMatchDO driverMatch = taskDriverMatchMapper.selectTaskDriverMatchById(dispatchCall.getTaskMatchId());
			if (driverMatch != null) {
				driverMatch.setIsOutbound(4); // 4表示呼叫失败
				taskDriverMatchMapper.updateTaskDriverMatchById(driverMatch);
				logger.info("更新司机[{}]状态为呼叫失败", driverMatch.getDriverName());
			}
		}
	}

	/**
	 * 处理成功的回调
	 * @param schedulerCallPush
	 * @param dispatchCall
	 */
	private void handleSchedulerSuccess(SchedulerCallPush schedulerCallPush, DispatchCallDO dispatchCall) {
		log.info("handleSchedulerSuccess,dispatchCall:{}",dispatchCall);
		dispatchCall.setCallStatus("已呼叫");
		//根据地址下载录音 并调用QiNiuUtil 工具上传到oss中 并返回新地址替换文件
		String voiceUrl = schedulerCallPush.getVoiceUrl();
		voiceUrl = downloadVoiceUrl(dispatchCall, voiceUrl);
		schedulerCallPush.setVoiceUrl(voiceUrl);
		dispatchCall.setVoiceUrl(voiceUrl);
		dispatchCall.setCallContent(schedulerCallPush.getVoiceText());
		// 判断意向等级，设置是否有意向
		Integer intentionResults = schedulerCallPush.getIntentionResults();
		boolean hasIntention = false;
		if (intentionResults != null && (intentionResults == 1)) {
			// 1A 2B 3C 这三个标签表示有意向
			dispatchCall.setHasIntention(1); // 1表示有意向
			hasIntention = true;
		}else if (intentionResults != null && intentionResults == 2){
			dispatchCall.setHasIntention(2); // 1表示有意向
			hasIntention = true;
		} else {
			dispatchCall.setHasIntention(0); // 0表示无意向
		}
		// 更新调度呼叫记录
		dispatchCallService.updateDispatchCall(dispatchCall);
		// 更新匹配司机状态
		if (dispatchCall.getTaskMatchId() != null) {
			TaskDriverMatchDO driverMatch = taskDriverMatchMapper.selectTaskDriverMatchById(dispatchCall.getTaskMatchId());
			if (driverMatch != null) {
				driverMatch.setIsOutbound(3); // 3表示已呼叫
				driverMatch.setHasIntention(dispatchCall.getHasIntention());
				taskDriverMatchMapper.updateTaskDriverMatchById(driverMatch);
				logger.info("更新司机[{}]状态为已呼叫", driverMatch.getDriverName());
				if (hasIntention && driverMatch.getTaskPlanId() != null) {
					try {
						int rows = taskDriverMatchMapper.updateInterestedDriverCount(driverMatch.getTaskPlanId());
						log.info("更新任务计划意向司机数量结果: {}", rows > 0 ? "成功" : "失败");
					} catch (Exception e) {
						log.error("更新任务计划意向司机数量异常", e);
					}
				}
			}
		}
	}

	@Override
	public String downloadVoiceUrl(DispatchCallDO dispatchCall, String voiceUrl) {
		File tempFile = null;
		try {
			if (StringUtils.isNotBlank(voiceUrl)) {
				// 检查URL是否包含协议，如果没有则添加http://
				if (!voiceUrl.startsWith("http://") && !voiceUrl.startsWith("https://")) {
					voiceUrl = "http://" + voiceUrl;
				}
				// 创建临时文件
				String fileName = String.format("%s_%s_%d.wav", dispatchCall.getExternalTaskId(), dispatchCall.getId(), System.currentTimeMillis());
				// 创建临时目录
				String tempDir = System.getProperty("java.io.tmpdir");
				tempFile = new File(tempDir, fileName);
				// 下载文件到本地
				boolean downloadSuccess = HttpUtils.downloadFileToLocal(voiceUrl, tempFile.getAbsolutePath());
				if (downloadSuccess && tempFile.exists() && tempFile.length() > 0) {
					log.info("文件下载成功，临时文件路径：{}", tempFile.getAbsolutePath());
					// 上传到OSS
					Response upload = QiNiuUtil.upload(tempFile.getAbsolutePath(), "ai_voice/"+fileName);
					if (upload != null && upload.isOK()) {
						voiceUrl = customConfig.getQiniuDomain() +"ai_voice/" +fileName; // 使用OSS地址替换原始地址
						log.info("文件上传成功，OSS地址：{}", voiceUrl);
					}
				} else {
					log.error("文件下载失败或文件为空");
				}
			}
		} catch (Exception e) {
			log.error("下载或上传录音文件失败", e);
		} finally {
			// 删除临时文件
			if (tempFile != null && tempFile.exists()) {
				boolean deleted = tempFile.delete();
				if (!deleted) {
					log.warn("临时文件删除失败：{}", tempFile.getAbsolutePath());
					// 添加到JVM退出时删除
					tempFile.deleteOnExit();
				} else {
					log.info("临时文件已删除：{}", tempFile.getAbsolutePath());
				}
			}
		}
		return voiceUrl;
	}

	/**
	 * 蜂鸟迅达获取话单录音文字
	 */
	@Override
	public AjaxResult getCallOrder(String taskId, String callId) {
		if (StringUtils.isBlank(taskId) || StringUtils.isBlank(callId)) {
			return AjaxResult.error("请选择要获取录音文字的任务和电话标识");
		}
		String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdGetCallOrder();
		String param = "task_id=" + taskId + "&callid=" + callId;
		String result = HttpUtils.sendGet(url, param);
		JSONObject resultJson = JSON.parseObject(result);
		Integer code = resultJson.getInteger("code");
		SchedulerCallPush schedulerCallPush = new SchedulerCallPush();
		schedulerCallPush.setTaskId(taskId);
		schedulerCallPush.setCallid(callId);
		List<SchedulerCallPush> list = schedulerCallPushMapper.selectSchedulerCallPushList(schedulerCallPush);
		if (null != list && !list.isEmpty()) {
			schedulerCallPush = new SchedulerCallPush();
			schedulerCallPush.setId(list.get(0).getId());
			if (200 == code.intValue()) {
				schedulerCallPush.setVoiceText(resultJson.getString("data"));
				schedulerCallPush.setUpdateTime(new Date());
				schedulerCallPushMapper.updateSchedulerCallPush(schedulerCallPush);
			}
		}
		return 200 == code.intValue() ? AjaxResult.success(resultJson.getString("message"), resultJson.getString("data")) : AjaxResult.error(resultJson.getString("message"));
	}

	@Override
	public AjaxResult getCallOrderNew(String taskId, String callId) {
		if (StringUtils.isBlank(taskId) || StringUtils.isBlank(callId)) {
			return AjaxResult.error("请选择要获取录音文字的任务和电话标识");
		}
		try {
			String url = customConfig.getBeeBirdApiUrl() + customConfig.getBeeBirdGetCallOrder();
			String param = "task_id=" + taskId + "&callid=" + callId;
			String result = HttpUtils.sendGet(url, param);
			JSONObject resultJson = JSON.parseObject(result);
			Integer code = resultJson.getInteger("code");
			return 200 == code ?
					AjaxResult.success(resultJson.getString("message"), resultJson.getString("data")) :
					AjaxResult.error(resultJson.getString("message"));
		} catch (Exception e) {
			log.error("获取通话内容异常，任务ID：{}，通话ID：{}", taskId, callId, e);
			return AjaxResult.error("获取通话内容异常：" + e.getMessage());
		}
	}

	/**
	 * 获取通话录音地址
	 *
	 * @param taskId 任务ID
	 * @param callid 通话ID
	 * @return 录音地址信息
	 */
	@Override
	public AjaxResult getCallOrderVoice(String taskId, String callid) {
		if (StringUtils.isBlank(taskId)) {
			return AjaxResult.error("任务ID不能为空");
		}
		if (StringUtils.isBlank(callid)) {
			return AjaxResult.error("通话ID不能为空");
		}
		try {
			String url = customConfig.getBeeBirdApiUrl() + "api/api/getCallOrderVoice";
			StringBuilder param = new StringBuilder();
			param.append("task_id=").append(taskId);
			param.append("&callid=").append(callid);
			String result = HttpUtils.sendGet(url, param.toString());
			log.info("获取通话录音地址，任务ID：{}，通话ID：{}，结果：{}", taskId, callid, result);
			JSONObject resultJson = JSON.parseObject(result);
			Integer code = resultJson.getInteger("code");
			if (200 == code) {
				// 成功获取录音地址
				return AjaxResult.success(resultJson.getString("message"), resultJson.getString("data"));
			} else {
				// 获取失败
				return AjaxResult.error(resultJson.getString("message"));
			}
		} catch (Exception e) {
			log.error("获取通话录音地址异常，任务ID：{}，通话ID：{}", taskId, callid, e);
			return AjaxResult.error("获取通话录音地址异常：" + e.getMessage());
		}
	}


	@Override
	public AjaxResult getPhoneDetail(String taskId, String phone) {
		if (StringUtils.isBlank(taskId) || StringUtils.isBlank(phone)) {
			return AjaxResult.error("任务ID和电话号码不能为空");
		}
		try {
			String url = customConfig.getBeeBirdApiUrl() + "api/api/numberDetial";
			String param = "task_id=" + taskId + "&phone=" + phone;
			String result = HttpUtils.sendGet(url, param);
			log.info("获取电话详情，任务ID：{}，电话：{}，结果：{}", taskId, phone, result);
			JSONObject resultJson = JSON.parseObject(result);
			Integer code = resultJson.getInteger("code");
			if (200 == code.intValue()) {
				// 成功获取电话详情
				return AjaxResult.success(resultJson.getString("message"), resultJson.getString("data"));
			} else {
				// 获取失败
				return AjaxResult.error(resultJson.getString("message"));
			}
		} catch (Exception e) {
			log.error("获取电话详情异常，任务ID：{}，电话：{}", taskId, phone, e);
			return AjaxResult.error("获取电话详情异常：" + e.getMessage());
		}
	}

	@Override
	public AjaxResult delTask(String taskId) {
		if (StringUtils.isBlank(taskId)) {
			return AjaxResult.error("任务ID不能为空");
		}
		try {
			String url = customConfig.getBeeBirdApiUrl() + "api/api/taskDelete";
			String param = "task_id=" + taskId;
			String result = HttpUtils.sendGet(url, param);
			log.info("删除任务，任务ID：{}，结果：{}", taskId, result);
			JSONObject resultJson = JSON.parseObject(result);
			Integer code = resultJson.getInteger("code");
			if (200 == code) {
				return AjaxResult.success(resultJson.getString("message"), resultJson.getString("data"));
			} else {
				// 获取失败
				return AjaxResult.error(resultJson.getString("message"));
			}
		} catch (Exception e) {
			log.error("删除任务异常，任务ID：{}", taskId, e);
			return AjaxResult.error("删除任务异常：" + e.getMessage());
		}
	}




	/**
	 * 查询通话列表
	 *
	 * @param taskId 任务ID
	 * @param keyword 关键字（电话号码）
	 * @param page 页码
	 * @param perPage 每页记录数
	 * @return 通话列表
	 */
	@Override
	public AjaxResult getOrderList(String taskId, String keyword, Integer page, Integer perPage) {
		if (StringUtils.isBlank(taskId)) {
			return AjaxResult.error("任务ID不能为空");
		}
		try {
			String url = customConfig.getBeeBirdApiUrl() + "api/api/orderList";
			StringBuilder param = new StringBuilder();
			param.append("task_id=").append(taskId);
			if (StringUtils.isNotBlank(keyword)) {
				param.append("&keyword=").append(keyword);
			}
			if (page != null) {
				param.append("&page=").append(page);
			} else {
				param.append("&page=1");
			}
			if (perPage != null) {
				param.append("&per_page=").append(perPage);
			} else {
				param.append("&per_page=10");
			}
			String result = HttpUtils.sendGet(url, param.toString());
			log.info("获取通话列表，任务ID：{}，关键字：{}，结果：{}", taskId, keyword, result);
			JSONObject resultJson = JSON.parseObject(result);
			Integer code = resultJson.getInteger("code");
			if (200 == code.intValue()) {
				// 成功获取通话列表
				return AjaxResult.success(resultJson.getString("message"), resultJson.getString("data"));
			} else {
				// 获取失败
				return AjaxResult.error(resultJson.getString("message"));
			}
		} catch (Exception e) {
			log.error("获取通话列表异常，任务ID：{}，关键字：{}", taskId, keyword, e);
			return AjaxResult.error("获取通话列表异常：" + e.getMessage());
		}
	}

	@Override
	public AjaxResult createTask(CreateTask createTask) {
		if (createTask == null) {
			return AjaxResult.error("参数不能为空");
		}
		if (StringUtils.isBlank(createTask.getName())) {
			return AjaxResult.error("任务名称不能为空");
		}
		if (createTask.getDestination_extension() == null) {
			return AjaxResult.error("目标分机号不能为空");
		}
		if (createTask.getMaximumcall() == null) {
			return AjaxResult.error("最大呼叫数不能为空");
		}
		if (createTask.getDial_time_id() == null) {
			return AjaxResult.error("拨号时间ID不能为空");
		}
		if (createTask.getTask_extras() == null || 
			StringUtils.isBlank(createTask.getTask_extras().getLine())) {
			return AjaxResult.error("任务额外参数不能为空");
		}
		try {
			Map<String, Object> requestBody = new HashMap<>();
			requestBody.put("name", createTask.getName());
			requestBody.put("destination_extension", createTask.getDestination_extension());
			requestBody.put("maximumcall", createTask.getMaximumcall());
			requestBody.put("dial_time_id", createTask.getDial_time_id());
			// 添加额外参数
			Map<String, Object> extras = new HashMap<>();
			extras.put("start_time", createTask.getTask_extras().getStart_time());
			extras.put("stop_time", createTask.getTask_extras().getStop_time());
			extras.put("line", createTask.getTask_extras().getLine());
			requestBody.put("task_extras", extras);
	
			// 调用API
			String url = customConfig.getBeeBirdApiUrl() + "api/api/taskCreate";
			String param = JSON.toJSONString(requestBody);
			String result = HttpUtils.sendPost(url, param);
			log.info("创建任务请求参数：{}，响应结果：{}", param, result);
	
			JSONObject resultJson = JSON.parseObject(result);
			Integer code = resultJson.getInteger("code");
			if (200 == code.intValue()) {
				// 成功创建任务
				Map<String, String> data = new HashMap<>();
				data.put("id", resultJson.getJSONObject("data").getString("id"));
				return AjaxResult.success("成功", data);
			} else {
				return AjaxResult.error(resultJson.getString("message"));
			}
		} catch (Exception e) {
			log.error("创建任务异常", e);
			return AjaxResult.error("创建任务异常：" + e.getMessage());
		}
	}

	@Override
	public AjaxResult getTaskInitInfo() {
		try {
			String url = customConfig.getBeeBirdApiUrl() + "api/api/taskInit";
			String result = HttpUtils.sendGet(url, "");
			log.info("获取任务创建信息，结果：{}", result);
			JSONObject resultJson = JSON.parseObject(result);
			Integer code = resultJson.getInteger("code");
			if (200 == code) {
				// 成功获取任务创建信息
				JSONObject data = resultJson.getJSONObject("data");
				// 获取拨号时间ID
				Integer dialTimeId = null;
				JSONArray taskDialTimes = data.getJSONArray("task_dial_times");
				if (taskDialTimes != null && !taskDialTimes.isEmpty()) {
					JSONObject firstDialTime = taskDialTimes.getJSONObject(0);
					dialTimeId = firstDialTime.getInteger("id");
				}
				// 获取线路ID
				String lineId = null;
				JSONArray callerLineList = data.getJSONArray("caller_line_list");
				if (callerLineList != null && !callerLineList.isEmpty()) {
					JSONObject firstLine = callerLineList.getJSONObject(0);
					lineId = firstLine.getString("line_id");
				}
				// 构建返回结果
				TaskInitInfo taskInitInfo = new TaskInitInfo();
				taskInitInfo.setDialTimeId(dialTimeId);
				taskInitInfo.setLineId(lineId);
				return AjaxResult.success("成功", taskInitInfo);
			} else {
				// 获取失败
				return AjaxResult.error(resultJson.getString("message"));
			}
		} catch (Exception e) {
			log.error("获取任务创建信息异常", e);
			return AjaxResult.error("获取任务创建信息异常：" + e.getMessage());
		}
	}
}

