package com.zly.project.outbound.service;

import java.util.List;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.match.domain.DispatchCallDO;
import com.zly.project.outbound.domain.*;

/**
 * 外呼系统接口服务
 * 
 * <AUTHOR>
 */
public interface IBeeBirdService {

	/**
	 * 获取蜂鸟迅达变量名
	 * 
	 * @return
	 */
	public List<VariableField> getFieldList();

	/**
	 * 获取蜂鸟迅达任务列表 status 1：暂停，3：执行中，不指定返回所有任务数据
	 */
	public List<Task> getTaskList(Integer status);

	/**
	 * 蜂鸟迅达开始任务
	 * 
	 * @return
	 */
	public AjaxResult taskStart(String taskId);

	/**
	 * 蜂鸟迅达暂停任务
	 * 
	 * @return
	 */
	public AjaxResult taskStop(String taskId);

	/**
	 * 蜂鸟迅达添加号码
	 * 
	 * @return
	 */
	public AjaxResult taskNumber(TaskNumber taskNumber);

	/**
	 * 蜂鸟迅达添加号码
	 *
	 * @return
	 */
	public AjaxResult taskNumberNew(TaskNumber taskNumber);

	/**
	 * 蜂鸟迅达重置号码
	 * 
	 * @return
	 */
	public AjaxResult resetNumber(ResetNumber resetNumber);

	/**
	 * 蜂鸟迅达删除号码
	 * 
	 * @return
	 */
	public AjaxResult deleteNumber(ResetNumber resetNumber);

	/**
	 * 蜂鸟迅达话单推送
	 * 
	 * @return
	 */
	public int callPush(int status, CallPush callPush);

	/**
	 * 蜂鸟迅达获取话单录音文字
	 * 
	 * @return
	 */
	public AjaxResult getCallOrder(String taskId, String callid);

	/**
	 * 蜂鸟迅达获取话单录音文字
	 *
	 * @return
	 */
	public AjaxResult getCallOrderNew(String taskId, String callid);

	/**
	 * 下载录音文件
	 * @param dispatchCall
	 * @param voiceUrl
	 * @return
	 */
	String downloadVoiceUrl(DispatchCallDO dispatchCall, String voiceUrl);

	/**
	 * 获取通话录音地址
	 *
	 * @param taskId 任务ID
	 * @param callid 通话ID
	 * @return 录音地址信息
	 */
	public AjaxResult getCallOrderVoice(String taskId, String callid);


	/**
	 * 蜂鸟迅达获取话单录音文字
	 *
	 * @return
	 */
	public AjaxResult getPhoneDetail(String taskId, String phone);

	/**
	 * 删除任务  先暂停在删除
	 * @param taskId
	 * @return
	 */
	public AjaxResult delTask(String taskId);

	/**
	 * 查询通话列表
	 *
	 * @param taskId 任务ID
	 * @param keyword 关键字（电话号码）
	 * @param page 页码
	 * @param perPage 每页记录数
	 * @return 通话列表
	 */
	public AjaxResult getOrderList(String taskId, String keyword, Integer page, Integer perPage);

	/**
	 * 创建蜂鸟迅达外呼任务
	 *
	 * @param createTask 创建任务参数
	 * @return 创建结果，包含任务ID
	 */
	public AjaxResult createTask(CreateTask createTask);

	/**
	 * 获取任务创建所需的基础信息
	 *
	 * @return 包含拨号时间ID和线路ID的结果
	 */
	public AjaxResult getTaskInitInfo();


}


