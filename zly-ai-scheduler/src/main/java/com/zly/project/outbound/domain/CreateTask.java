package com.zly.project.outbound.domain;

import java.io.Serializable;

import lombok.Data;

/**
 * 创建任务请求参数
 * 
 * <AUTHOR>
 */
@Data
public class CreateTask implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 任务名称
     */
    private String name;
    
    /**
     * 话术ID
     */
    private Integer destination_extension;
    
    /**
     * 并发数
     */
    private Integer maximumcall;
    
    /**
     * 拨号时间ID
     */
    private Integer dial_time_id;
    
    /**
     * 任务额外参数
     */
    private TaskExtras task_extras;
    
    /**
     * 任务额外参数
     */
    @Data
    public static class TaskExtras implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 开始时间
         */
        private String start_time;
        
        /**
         * 结束时间
         */
        private String stop_time;
        
        /**
         * 线路ID
         */
        private String line;
    }
}