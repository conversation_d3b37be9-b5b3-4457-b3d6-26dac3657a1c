package com.zly.project.outbound.domain;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 话单推送
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
public class CallPush {

	private Date calldate; // 呼叫时间
	private Date answerdate; // 应答时间
	private Integer bill; // 通话时长 - 秒
	private Integer duration; // 秒
	private String callid; // 软电话拨打的一个唯一id（record_id）
	private String customer_id; // 客户公海id
	private String master_user_id; // 当前通话记录归属用户的上游id(type为4情况下 与user_id值相同)
	private String user_id; // 用户id
	private Integer type; // 用户类型 4-主账号 8-子账号
	private Integer intention_results; // 意向标签等级 1A2B3C4D5E6F
	private Integer rounds; // 交互次数
	private Integer score; // 通话评分

	private NumberData number_data; // 拨叫号码信息

	@Data
	public static class NumberData {
		private String number;// 电话号码
		private Date hangupdate;// 挂断时间
		private String province;// 省
		private String city;// 市
		private String operator;// 号码运营商
	}

	private Group group; // 话术组信息

	@Data
	public static class Group {
		private Integer id;// 话术id
		private String name;// 话术组名称
	}

	private Task task; // 任务信息

	@Data
	public static class Task {
		private String id;// 外呼任务id
		private String name;// 任务名称
	}

	private User user; // 账号信息

	@Data
	public static class User {
		private String id;// 账号id
		private String name;// 账号昵称
	}

	private Level level; // 评分信息

	@Data
	public static class Level {
		private String level_id;// 评分id
		private String level_name;// 评分名称
	}

	private List<Tags> tags; // 通话记录机器人标签

	@Data
	public static class Tags {
		private String tag_id;// 标签id
		private String name;// 标签名
	}

	private CustomerData customer_data; // 客户公海信息

	@Data
	public static class CustomerData {
		private String name;// 昵称
		private String email;// 邮箱
		private String company;// 公司
		private String extra;// 添加号码时的选填extra字段，可用于匹配您自己的CRM等客资系统，如果没有传输则为null
	}

	private String voice_url;// 通话录音地址

	// 以下是失败话单推送字段
	private String number;// 电话号码
	private String numberid;//
	private Integer status; //
	private String status_str; //
}
