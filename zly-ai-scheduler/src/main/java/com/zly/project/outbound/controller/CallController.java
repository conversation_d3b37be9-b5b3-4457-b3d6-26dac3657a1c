package com.zly.project.outbound.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.outbound.domain.CallPush;
import com.zly.project.outbound.service.IBeeBirdService;

@Anonymous
@RestController
@RequestMapping("/call/api")
public class CallController extends BaseController {

	@Resource
	private IBeeBirdService beeBirdService;

	@PostMapping("/callSuccess")
	public AjaxResult callSuccess(@RequestBody CallPush callPush) {
		return toAjax(beeBirdService.callPush(1, callPush));
	}

	@PostMapping("/callFail")
	public AjaxResult callFail(@RequestBody CallPush callPush) {
		return toAjax(beeBirdService.callPush(0, callPush));
	}
}
