package com.zly.project.inquiry.domain.req;

import io.swagger.annotations.ApiModelProperty;

/**
 * 询价相关参数
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
public class InquiryReq {

	/** 运输线路(出发地) */
	@ApiModelProperty(value = "运输线路(出发地)", required = true)
	private String haulwayStart;

	/** 运输线路(目的地) */
	@ApiModelProperty(value = "运输线路(目的地)", required = true)
	private String haulwayEnd;

	/** 运输货物 */
	@ApiModelProperty(value = "运输货物", required = true)
	private String descriptionOfGoods;

	/** 货物重量/体积（二选一） */
	@ApiModelProperty(value = "货物重量/体积（二选一）", required = true)
	private String feeAmount;

	/** 货物包装 */
	@ApiModelProperty(value = "货物包装", required = false)
	private String packagingName;

	/** 运输车辆长度(米) */
	@ApiModelProperty(value = "运输车辆长度(米)", required = false)
	private String vehicleLength;

	/** 运输车辆类型 */
	@ApiModelProperty(value = "运输车辆类型", required = false)
	private String vehicleTypeName;

	/** 装货时间(起) */
	@ApiModelProperty(value = "装货时间(起)", required = false)
	private String loadDateStart;

	/** 装货时间(止) */
	@ApiModelProperty(value = "装货时间(止)", required = false)
	private String loadDateEnd;

	/** 补充说明 */
	@ApiModelProperty(value = "补充说明", required = false)
	private String remark;

	public String getHaulwayStart() {
		return haulwayStart;
	}

	public void setHaulwayStart(String haulwayStart) {
		this.haulwayStart = haulwayStart;
	}

	public String getHaulwayEnd() {
		return haulwayEnd;
	}

	public void setHaulwayEnd(String haulwayEnd) {
		this.haulwayEnd = haulwayEnd;
	}

	public String getDescriptionOfGoods() {
		return descriptionOfGoods;
	}

	public void setDescriptionOfGoods(String descriptionOfGoods) {
		this.descriptionOfGoods = descriptionOfGoods;
	}

	public String getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(String feeAmount) {
		this.feeAmount = feeAmount;
	}

	public String getPackagingName() {
		return packagingName;
	}

	public void setPackagingName(String packagingName) {
		this.packagingName = packagingName;
	}

	public String getVehicleLength() {
		return vehicleLength;
	}

	public void setVehicleLength(String vehicleLength) {
		this.vehicleLength = vehicleLength;
	}

	public String getVehicleTypeName() {
		return vehicleTypeName;
	}

	public void setVehicleTypeName(String vehicleTypeName) {
		this.vehicleTypeName = vehicleTypeName;
	}

	public String getLoadDateStart() {
		return loadDateStart;
	}

	public void setLoadDateStart(String loadDateStart) {
		this.loadDateStart = loadDateStart;
	}

	public String getLoadDateEnd() {
		return loadDateEnd;
	}

	public void setLoadDateEnd(String loadDateEnd) {
		this.loadDateEnd = loadDateEnd;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
