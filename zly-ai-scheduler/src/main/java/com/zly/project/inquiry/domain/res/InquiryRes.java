package com.zly.project.inquiry.domain.res;

/***
 * 驾车路线规划接口返回结果
 */
public class InquiryRes {
	private String code;
	private String message;
	private BasicInfo basicInfo;
	private CostStructure costStructure;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public BasicInfo getBasicInfo() {
		return basicInfo;
	}

	public void setBasicInfo(BasicInfo basicInfo) {
		this.basicInfo = basicInfo;
	}

	public CostStructure getCostStructure() {
		return costStructure;
	}

	public void setCostStructure(CostStructure costStructure) {
		this.costStructure = costStructure;
	}

	/**
	 * 运输基本情况分析
	 * 
	 * <AUTHOR>
	 *
	 */
	public class BasicInfo {
		private Haulway haulway;// 起终点信息
		private Goods goods;// 货物信息
		private Vehicle vehicle;// 车辆信息
		private String oilPrice;// 当前油价

		public Haulway getHaulway() {
			return haulway;
		}

		public void setHaulway(Haulway haulway) {
			this.haulway = haulway;
		}

		public Goods getGoods() {
			return goods;
		}

		public void setGoods(Goods goods) {
			this.goods = goods;
		}

		public Vehicle getVehicle() {
			return vehicle;
		}

		public void setVehicle(Vehicle vehicle) {
			this.vehicle = vehicle;
		}

		public String getOilPrice() {
			return oilPrice;
		}

		public void setOilPrice(String oilPrice) {
			this.oilPrice = oilPrice;
		}
	}

	public class Haulway {
		private String loadAddress;// 起点
		private String unloadAddress;// 终点
		private String loadLonlat;// 起运地经纬度
		private String unloadLonlat;// 目的地经纬度
		private String mileage;// 路线里程
		private String haulageTime;// 预计运输时间

		public String getLoadAddress() {
			return loadAddress;
		}

		public void setLoadAddress(String loadAddress) {
			this.loadAddress = loadAddress;
		}

		public String getUnloadAddress() {
			return unloadAddress;
		}

		public void setUnloadAddress(String unloadAddress) {
			this.unloadAddress = unloadAddress;
		}

		public String getLoadLonlat() {
			return loadLonlat;
		}

		public void setLoadLonlat(String loadLonlat) {
			this.loadLonlat = loadLonlat;
		}

		public String getUnloadLonlat() {
			return unloadLonlat;
		}

		public void setUnloadLonlat(String unloadLonlat) {
			this.unloadLonlat = unloadLonlat;
		}

		public String getMileage() {
			return mileage;
		}

		public void setMileage(String mileage) {
			this.mileage = mileage;
		}

		public String getHaulageTime() {
			return haulageTime;
		}

		public void setHaulageTime(String haulageTime) {
			this.haulageTime = haulageTime;
		}
	}

	public class Goods {
		private String goodsType;// 货物类型
		private String packingName;// 包装方式
		private String weightVolume;// 重量或体积
		private String amount;// 件数

		public String getGoodsType() {
			return goodsType;
		}

		public void setGoodsType(String goodsType) {
			this.goodsType = goodsType;
		}

		public String getPackingName() {
			return packingName;
		}

		public void setPackingName(String packingName) {
			this.packingName = packingName;
		}

		public String getWeightVolume() {
			return weightVolume;
		}

		public void setWeightVolume(String weightVolume) {
			this.weightVolume = weightVolume;
		}

		public String getAmount() {
			return amount;
		}

		public void setAmount(String amount) {
			this.amount = amount;
		}
	}

	public class Vehicle {
		private String vehicleType;// 车辆类型
		private String matching;// 匹配性

		public String getVehicleType() {
			return vehicleType;
		}

		public void setVehicleType(String vehicleType) {
			this.vehicleType = vehicleType;
		}

		public String getMatching() {
			return matching;
		}

		public void setMatching(String matching) {
			this.matching = matching;
		}
	}

	/**
	 * 运输成本构成
	 * 
	 * <AUTHOR>
	 *
	 */
	public class CostStructure {
		private FuelCost fuelCost;// 燃料费
		private BridgeAndRoadTolls bridgeAndRoadTolls;// 通行费
		private String salary;// 人工工资
		private Result result;// 预测金额

		public FuelCost getFuelCost() {
			return fuelCost;
		}

		public void setFuelCost(FuelCost fuelCost) {
			this.fuelCost = fuelCost;
		}

		public BridgeAndRoadTolls getBridgeAndRoadTolls() {
			return bridgeAndRoadTolls;
		}

		public void setBridgeAndRoadTolls(BridgeAndRoadTolls bridgeAndRoadTolls) {
			this.bridgeAndRoadTolls = bridgeAndRoadTolls;
		}

		public String getSalary() {
			return salary;
		}

		public void setSalary(String salary) {
			this.salary = salary;
		}

		public Result getResult() {
			return result;
		}

		public void setResult(Result result) {
			this.result = result;
		}
	}

	public class FuelCost {
		private String fuelConsumption;// 百公里油耗
		private String totalFuelConsumption;// 总燃料消耗
		private String fuelAmount;// 燃油费用

		public String getFuelConsumption() {
			return fuelConsumption;
		}

		public void setFuelConsumption(String fuelConsumption) {
			this.fuelConsumption = fuelConsumption;
		}

		public String getTotalFuelConsumption() {
			return totalFuelConsumption;
		}

		public void setTotalFuelConsumption(String totalFuelConsumption) {
			this.totalFuelConsumption = totalFuelConsumption;
		}

		public String getFuelAmount() {
			return fuelAmount;
		}

		public void setFuelAmount(String fuelAmount) {
			this.fuelAmount = fuelAmount;
		}
	}

	public class BridgeAndRoadTolls {
		private String tollAmount;// 过路费
		private String pontageAmount;// 过桥费

		public String getTollAmount() {
			return tollAmount;
		}

		public void setTollAmount(String tollAmount) {
			this.tollAmount = tollAmount;
		}

		public String getPontageAmount() {
			return pontageAmount;
		}

		public void setPontageAmount(String pontageAmount) {
			this.pontageAmount = pontageAmount;
		}
	}

	public class Result {
		private String fuelFee;// 燃油费
		private String tollFee;// 通行费
		private String minFee;// 最低运费
		private String maxFee;// 最高运费
		private String resultFee;// 本次预测运费

		public String getFuelFee() {
			return fuelFee;
		}

		public void setFuelFee(String fuelFee) {
			this.fuelFee = fuelFee;
		}

		public String getTollFee() {
			return tollFee;
		}

		public void setTollFee(String tollFee) {
			this.tollFee = tollFee;
		}

		public String getMinFee() {
			return minFee;
		}

		public void setMinFee(String minFee) {
			this.minFee = minFee;
		}

		public String getMaxFee() {
			return maxFee;
		}

		public void setMaxFee(String maxFee) {
			this.maxFee = maxFee;
		}

		public String getResultFee() {
			return resultFee;
		}

		public void setResultFee(String resultFee) {
			this.resultFee = resultFee;
		}
	}
}
