package com.zly.project.inquiry.controller;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.http.HttpUtils;
import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.framework.config.CustomConfig;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.inquiry.domain.req.InquiryReq;
import com.zly.project.inquiry.domain.res.InquiryRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 管理后台 - 运输询价
 *
 * <AUTHOR>
 */
@Api("管理后台 - 运输询价")
@RestController
@RequestMapping("/inquiry")
@Validated
@Slf4j
public class InquiryController {

	@Resource
	private CustomConfig customConfig;

	@Anonymous
	@PostMapping("/bot")
	@ApiOperation("运输询价")
	public AjaxResult bot(@RequestBody InquiryReq inquiry) {
		log.info("参数parm:{}", JSON.toJSONString(inquiry));
		if (null == inquiry) {
			return AjaxResult.error("请输入询价内容");
		}
		if (StringUtils.isBlank(inquiry.getHaulwayStart()) || StringUtils.isBlank(inquiry.getHaulwayEnd())) {
			return AjaxResult.error("运输线路不完整");
		}
		if (StringUtils.isBlank(inquiry.getDescriptionOfGoods())) {
			return AjaxResult.error("请输入运输货物");
		}
		if (StringUtils.isBlank(inquiry.getFeeAmount())) {
			return AjaxResult.error("请输入货物的重量或体积");
		}
		String question = "起点: " + inquiry.getHaulwayStart() + "，终点: " + inquiry.getHaulwayEnd() + "，运输货物: " + inquiry.getDescriptionOfGoods()
				+ (inquiry.getFeeAmount().contains("方") ? "，货物体积: " + inquiry.getFeeAmount() : "，货物重量: " + inquiry.getFeeAmount());
		if (StringUtils.isNotBlank(inquiry.getPackagingName())) {
			question += "，货物包装: " + inquiry.getPackagingName();
		}
		if (StringUtils.isNotBlank(inquiry.getVehicleTypeName())) {
			question += "，运输车辆类型: " + inquiry.getVehicleTypeName();
		}
		if (StringUtils.isNotBlank(inquiry.getVehicleLength())) {
			question += "，运输车辆长度: " + inquiry.getVehicleLength() + "米";
		}
		if (StringUtils.isNotBlank(inquiry.getLoadDateStart())) {
			question += "，装货时间起: " + inquiry.getLoadDateStart();
		}
		if (StringUtils.isNotBlank(inquiry.getLoadDateEnd())) {
			question += "，装货时间止: " + inquiry.getLoadDateEnd();
		}
		if (StringUtils.isNotBlank(inquiry.getRemark())) {
			question += "，补充说明: " + inquiry.getRemark();
		}
		String parmJson = "{\"input_value\": \"" + question + "\",\"output_type\": \"chat\",\"input_type\": \"chat\",\"output_component\": \"TextOutput-a04Xc\"}";
		String apiUrl = customConfig.getInquiryApiUrl();
		String apiKey = customConfig.getInquiryApiKey();
		log.info("调用询价api地址:{},key:{},参数:{}", apiUrl, apiKey, parmJson);
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		headerMap.put("x-api-key", apiKey);
		String result = HttpUtils.sendPost(apiUrl, headerMap, parmJson);
		log.info("调用询价api返回:{}", result);
		JSONObject root = JSON.parseObject(result);
		JSONArray outputs1 = root.getJSONArray("outputs");
		JSONObject firstOutput = outputs1.getJSONObject(0);
		JSONArray outputs2 = firstOutput.getJSONArray("outputs");
		JSONObject secondOutput = outputs2.getJSONObject(0);
		JSONArray messages = secondOutput.getJSONArray("messages");
		JSONObject messageObj = messages.getJSONObject(0);
		String innerJson = messageObj.getString("message");
		log.info("message:{}", innerJson);
		InquiryRes inquiryRes = JSON.parseObject(innerJson, InquiryRes.class);
		System.out.println("code值: " + inquiryRes.getCode());
		System.out.println("message值: " + inquiryRes.getMessage());
		System.out.println("运费结果: " + inquiryRes.getCostStructure().getResult().getResultFee());
		return "1".equals(inquiryRes.getCode()) ? AjaxResult.success("操作成功", innerJson) : AjaxResult.error(inquiryRes.getMessage());
	}

	// 工具方法：将输入流内容转为字符串
	private static String readStream(InputStream inputStream) throws IOException {
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
			StringBuilder builder = new StringBuilder();
			String line;
			while ((line = reader.readLine()) != null) {
				builder.append(line).append("\n");
			}
			return builder.toString().trim();
		}
	}
}