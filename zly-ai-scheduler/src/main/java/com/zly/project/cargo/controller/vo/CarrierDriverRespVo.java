package com.zly.project.cargo.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 承运司机响应VO
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ApiModel(value = "承运司机响应VO")
public class CarrierDriverRespVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 货源ID
     */
    @ApiModelProperty(value = "货源ID")
    private Long cargoId;

    /**
     * 调度任务ID
     */
    @ApiModelProperty(value = "调度任务ID")
    private Long taskId;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 身份证号（脱敏显示）
     */
    @ApiModelProperty(value = "身份证号（脱敏显示）")
    private String idCard;

    /**
     * 手机号（脱敏显示）
     */
    @ApiModelProperty(value = "手机号（脱敏显示）")
    private String phoneNumber;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    /**
     * 车牌颜色编码
     */
    @ApiModelProperty(value = "车牌颜色编码")
    private String plateColor;

    /**
     * 车牌颜色名称
     */
    @ApiModelProperty(value = "车牌颜色名称")
    private String plateColorName;

    /**
     * 接单时间
     */
    @ApiModelProperty(value = "接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTime;

    /**
     * 承运状态编码
     */
    @ApiModelProperty(value = "承运状态编码")
    private String carrierStatus;

    /**
     * 承运状态名称
     */
    @ApiModelProperty(value = "承运状态名称")
    private String carrierStatusName;

    /**
     * 状态(0-正常,1-已删除)
     */
    @ApiModelProperty(value = "状态(0-正常,1-已删除)")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
} 