package com.zly.project.cargo.service.impl;

import com.zly.common.utils.DateUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.enums.CarrierStatusEnum;
import com.zly.enums.ErrorCodeConstants;
import com.zly.enums.PlateColorEnum;
import com.zly.project.cargo.controller.vo.CarrierDriverRespVo;
import com.zly.project.cargo.domain.CarrierDriver;
import com.zly.project.cargo.mapper.CarrierDriverMapper;
import com.zly.project.cargo.service.ICarrierDriverService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 承运司机Service实现
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Service
public class CarrierDriverServiceImpl implements ICarrierDriverService {

    @Resource
    private CarrierDriverMapper carrierDriverMapper;

    /**
     * 新增承运司机
     *
     * @param carrierDriver 承运司机信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addCarrierDriver(CarrierDriver carrierDriver) {
        // 校验必填字段
        validateCarrierDriver(carrierDriver);
        
        // 设置默认值
        if (StringUtils.isBlank(carrierDriver.getCarrierStatus())) {
            carrierDriver.setCarrierStatus("0"); // 默认待接单
        }
        if (StringUtils.isBlank(carrierDriver.getStatus())) {
            carrierDriver.setStatus("0"); // 默认正常状态
        }
        carrierDriver.setId(TextUtil.generateId());
        // 设置创建人和创建时间
        carrierDriver.setCreateBy(SecurityUtils.getUsername());
        carrierDriver.setCreateTime(DateUtils.getNowDate());
        
        return carrierDriverMapper.insertCarrierDriver(carrierDriver);
    }

    /**
     * 校验承运司机信息
     *
     * @param carrierDriver 承运司机信息
     */
    private void validateCarrierDriver(CarrierDriver carrierDriver) {
        if (carrierDriver.getCargoId() == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货源ID不能为空");
        }
        if (StringUtils.isBlank(carrierDriver.getDriverName())) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "司机姓名不能为空");
        }
        if (StringUtils.isBlank(carrierDriver.getIdCard())) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "身份证号不能为空");
        }
        if (StringUtils.isBlank(carrierDriver.getPhoneNumber())) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "手机号不能为空");
        }
        // 校验车牌颜色是否有效
        if (!PlateColorEnum.isValid(carrierDriver.getPlateColor())) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "无效的车牌颜色");
        }
    }

    /**
     * 根据ID查询承运司机详情
     *
     * @param id 承运司机ID
     * @return 承运司机信息
     */
    @Override
    public CarrierDriverRespVo getCarrierDriverById(Long id) {
        log.info("getCarrierDriverById,id->{}",id);
        CarrierDriver carrierDriver = carrierDriverMapper.selectCarrierDriverById(id);
        if (carrierDriver == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "承运司机不存在");
        }

        // 转换为响应VO
        CarrierDriverRespVo respVo = new CarrierDriverRespVo();
        BeanUtils.copyProperties(carrierDriver, respVo);
        // 设置状态名称
        respVo.setCarrierStatusName(CarrierStatusEnum.getDescByCode(carrierDriver.getCarrierStatus()));
        respVo.setPlateColorName(PlateColorEnum.getDescByCode(carrierDriver.getPlateColor()));

        respVo.setIdCard(maskIdCard(carrierDriver.getIdCard()));
        respVo.setPhoneNumber(maskPhoneNumber(carrierDriver.getPhoneNumber()));
		return respVo;
	}

    /**
     * 编辑承运司机
     *
     * @param carrierDriver 承运司机信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCarrierDriver(CarrierDriver carrierDriver) {
        // 校验承运司机是否存在
        CarrierDriver existingDriver = carrierDriverMapper.selectCarrierDriverById(carrierDriver.getId());
        if (existingDriver == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "承运司机不存在");
        }
        
        // 设置更新人和更新时间
        carrierDriver.setUpdateBy(SecurityUtils.getUsername());
        carrierDriver.setUpdateTime(DateUtils.getNowDate());
        
        return carrierDriverMapper.updateCarrierDriver(carrierDriver);
    }

    /**
     * 查询承运司机列表
     *
     * @param carrierDriver 查询条件
     * @return 承运司机列表
     */
    @Override
    public List<CarrierDriver> selectCarrierDriverList(CarrierDriver carrierDriver) {
        return carrierDriverMapper.selectCarrierDriverList(carrierDriver);
    }
    
    /**
     * 查询承运司机列表并转换为响应VO
     *
     * @param carrierDriver 查询条件
     * @return 承运司机响应VO列表
     */
    @Override
    public List<CarrierDriverRespVo> selectCarrierDriverRespList(CarrierDriver carrierDriver) {
        log.info("查询承运司机列表，查询条件：{}", carrierDriver);
        List<CarrierDriver> driverList = carrierDriverMapper.selectCarrierDriverList(carrierDriver);
        log.info("查询到{}条承运司机记录", driverList != null ? driverList.size() : 0);
        return convertToRespVoList(driverList);
    }

    /**
     * 查询货源的承运司机数量
     *
     * @param cargoId 货源ID
     * @return 承运司机数量
     */
    @Override
    public int countDriversByCargoId(Long cargoId) {
        return carrierDriverMapper.countDriversByCargoId(cargoId);
    }

    /**
     * 根据货源ID查询承运司机列表
     *
     * @param customerId 货源ID
     * @return 承运司机列表
     */
    @Override
    public List<CarrierDriver> selectDriversByCargoId(Long customerId) {
        return carrierDriverMapper.selectDriversByCustomerId(customerId);
    }
    
    /**
     * 根据货源ID查询承运司机列表并转换为响应VO
     *
     * @param cargoId 货源ID
     * @return 承运司机响应VO列表
     */
    @Override
    public List<CarrierDriverRespVo> selectDriverRespVosByCargoId(Long cargoId) {
        log.info("查询货源ID为{}的承运司机列表", cargoId);
        if (cargoId == null) {
            log.warn("货源ID为空，返回空列表");
            return new ArrayList<>();
        }
        
        List<CarrierDriver> driverList = carrierDriverMapper.selectDriversByCustomerId(cargoId);
        log.info("查询到{}条承运司机记录", driverList != null ? driverList.size() : 0);
        return convertToRespVoList(driverList);
    }

    /**
     * 根据调度匹配司机ID查询承运司机列表
     *
     * @param taskDriverMatchId 调度匹配司机ID
     * @return 承运司机列表
     */
    @Override
    public List<CarrierDriver> selectDriversByTaskDriverMatchId(Long taskDriverMatchId) {
        return carrierDriverMapper.selectDriversByTaskDriverMatchId(taskDriverMatchId);
    }
    
    /**
     * 根据调度匹配司机ID查询承运司机列表并转换为响应VO
     *
     * @param taskDriverMatchId 调度匹配司机ID
     * @return 承运司机响应VO列表
     */
    @Override
    public List<CarrierDriverRespVo> selectDriverRespVosByTaskDriverMatchId(Long taskDriverMatchId) {
        log.info("查询调度匹配司机ID为{}的承运司机列表", taskDriverMatchId);
        if (taskDriverMatchId == null) {
            log.warn("调度匹配司机ID为空，返回空列表");
            return new ArrayList<>();
        }
        
        List<CarrierDriver> driverList = carrierDriverMapper.selectDriversByTaskDriverMatchId(taskDriverMatchId);
        log.info("查询到{}条承运司机记录", driverList != null ? driverList.size() : 0);
        return convertToRespVoList(driverList);
    }

    /**
     * 更新承运司机状态
     *
     * @param id 承运司机ID
     * @param carrierStatus 承运状态(0-待接单,1-待装车,2-运输中,3-已送达)
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCarrierStatus(Long id, String carrierStatus) {
        // 校验承运司机是否存在
        CarrierDriver existingDriver = carrierDriverMapper.selectCarrierDriverById(id);
        if (existingDriver == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "承运司机不存在");
        }
        
        // 校验状态是否有效
        if (!CarrierStatusEnum.isValid(carrierStatus)) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "无效的承运状态");
        }
        
        // 校验状态变更的合法性
        validateStatusChange(existingDriver.getCarrierStatus(), carrierStatus);
        
        // 更新状态
        CarrierDriver carrierDriver = new CarrierDriver();
        carrierDriver.setId(id);
        carrierDriver.setCarrierStatus(carrierStatus);
        
        // 如果是已送达状态，设置送达时间
        if ("3".equals(carrierStatus)) {
            carrierDriver.setUpdateTime(DateUtils.getNowDate());
        }
        
        // 设置更新人
        carrierDriver.setUpdateBy(SecurityUtils.getUsername());
        
        return carrierDriverMapper.updateCarrierDriver(carrierDriver);
    }

    /**
     * 校验承运状态变更的合法性
     *
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     */
    private void validateStatusChange(String currentStatus, String targetStatus) {
        // 状态流转规则：0(待接单) -> 1(待装车) -> 2(运输中) -> 3(已送达)
        if (StringUtils.equals(currentStatus, targetStatus)) {
            // 状态相同，无需变更
            return;
        }
        
        // 检查状态流转是否合法
        switch (currentStatus) {
            case "0": // 待接单
                if (!"1".equals(targetStatus)) {
                    throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "待接单状态只能变更为待装车状态");
                }
                break;
            case "1": // 待装车
                if (!"2".equals(targetStatus)) {
                    throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "待装车状态只能变更为运输中状态");
                }
                break;
            case "2": // 运输中
                if (!"3".equals(targetStatus)) {
                    throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "运输中状态只能变更为已送达状态");
                }
                break;
            case "3": // 已送达
                throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "已送达状态不能再变更");
            default:
                throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "无效的当前状态");
        }
    }
    
    /**
     * 将实体列表转换为响应VO列表
     *
     * @param driverList 承运司机实体列表
     * @return 承运司机响应VO列表
     */
    private List<CarrierDriverRespVo> convertToRespVoList(List<CarrierDriver> driverList) {
        List<CarrierDriverRespVo> respVoList = new ArrayList<>();
        if (driverList == null || driverList.isEmpty()) {
            log.info("承运司机列表为空，返回空集合");
            return respVoList;
        }
        
        for (CarrierDriver driver : driverList) {
            CarrierDriverRespVo respVo = new CarrierDriverRespVo();
            BeanUtils.copyProperties(driver, respVo);
            
            // 数据脱敏处理
            respVo.setIdCard(maskIdCard(driver.getIdCard()));
            respVo.setPhoneNumber(maskPhoneNumber(driver.getPhoneNumber()));
            
            // 设置状态名称
            String carrierStatus = driver.getCarrierStatus();
            String plateColor = driver.getPlateColor();
            
            if (StringUtils.isNotBlank(carrierStatus)) {
                respVo.setCarrierStatusName(CarrierStatusEnum.getDescByCode(carrierStatus));
            }
            
            if (StringUtils.isNotBlank(plateColor)) {
                respVo.setPlateColorName(PlateColorEnum.getDescByCode(plateColor));
            }
            
            respVoList.add(respVo);
        }
        
        log.info("成功转换{}条承运司机数据", respVoList.size());
        return respVoList;
    }
    
    /**
     * 身份证号脱敏
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    private String maskIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return "";
        }
        int length = idCard.length();
        if (length < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "********" + idCard.substring(length - 4);
    }
    
    /**
     * 手机号脱敏
     *
     * @param phoneNumber 手机号
     * @return 脱敏后的手机号
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return "";
        }
        int length = phoneNumber.length();
        if (length < 7) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(length - 4);
    }
} 