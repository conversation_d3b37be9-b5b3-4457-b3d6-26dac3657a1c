package com.zly.project.cargo.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运输路线响应VO
 *
 * <AUTHOR>
 */
@ApiModel("运输路线响应VO")
@Data
public class TransportRouteRespVo {

    @ApiModelProperty(value = "起始地点", example = "上海市浦东新区")
    private String startPoint;

    @ApiModelProperty(value = "目的地点", example = "北京市朝阳区")
    private String endPoint;
} 