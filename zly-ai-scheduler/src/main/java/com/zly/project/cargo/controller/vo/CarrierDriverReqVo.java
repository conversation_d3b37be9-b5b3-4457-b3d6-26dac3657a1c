package com.zly.project.cargo.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 承运司机请求VO
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class CarrierDriverReqVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 货源ID
     */
    @ApiModelProperty(value = "货源ID")
    @NotNull(message = "货源ID不能为空")
    private Long cargoId;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    @NotBlank(message = "司机姓名不能为空")
    private String driverName;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)", message = "身份证号格式不正确")
    private String idCard;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phoneNumber;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String plateNumber;

    /**
     * 车牌颜色(1-蓝色,2-黄色,3-黑色,4-白色,5-绿色,91-其他,92-农绿色,93-黄绿色,94-渐变绿)
     */
    @ApiModelProperty(value = "车牌颜色 车牌颜色(1-蓝色,2-黄色,3-黑色,4-白色,5-绿色,91-其他,92-农绿色,93-黄绿色,94-渐变绿)")
    @NotBlank(message = "车牌颜色不能为空")
    private String plateColor;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
} 