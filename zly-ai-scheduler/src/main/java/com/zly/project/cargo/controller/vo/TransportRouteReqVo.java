package com.zly.project.cargo.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运输路线请求VO
 *
 * <AUTHOR>
 */
@ApiModel("运输路线请求VO")
@Data
public class TransportRouteReqVo {

    @ApiModelProperty(value = "装货省名称", example = "上海市")
    private String loadingProvinceName;

    @ApiModelProperty(value = "装货市名称", example = "上海市")
    private String loadingCityName;

    @ApiModelProperty(value = "装货区名称", example = "浦东新区")
    private String loadingDistrictName;

    @ApiModelProperty(value = "卸货省名称", example = "北京市")
    private String unloadingProvinceName;

    @ApiModelProperty(value = "卸货市名称", example = "北京市")
    private String unloadingCityName;

    @ApiModelProperty(value = "卸货区名称", example = "朝阳区")
    private String unloadingDistrictName;
} 