package com.zly.project.cargo.controller.vo;

import com.zly.project.scheduler.controller.vo.CustomerInfoRes;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 常发货源请求VO对象
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@ApiModel("常发货源请求VO对象")
@Data
public class FrequentCargoReqVo {
    
    @NotNull(message = "货主ID不能为空")
    @ApiModelProperty(value = "货主ID")
    private Long customerId;

    @NotBlank(message = "货物名称不能为空")
    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @NotBlank(message = "货物类型不能为空")
    @ApiModelProperty(value = "货物类型")
    private String cargoType;

    @NotBlank(message = "包装方式不能为空")
    @ApiModelProperty(value = "包装方式")
    private String packagingMethod;

    // 装货地址
    @ApiModelProperty(value = "装货省编码")
    private String loadingProvinceCode;

    @ApiModelProperty(value = "装货省名称")
    private String loadingProvinceName;

    @ApiModelProperty(value = "装货市编码")
    private String loadingCityCode;

    @ApiModelProperty(value = "装货市名称")
    private String loadingCityName;

    @ApiModelProperty(value = "装货区编码")
    private String loadingDistrictCode;

    @ApiModelProperty(value = "装货区名称")
    private String loadingDistrictName;

    @NotBlank(message = "装货详细地址不能为空")
    @ApiModelProperty(value = "装货详细地址")
    private String loadingAddress;
    
    @ApiModelProperty(value = "装货地址经度")
    private BigDecimal loadingLongitude;
    
    @ApiModelProperty(value = "装货地址纬度")
    private BigDecimal loadingLatitude;

    // 卸货地址
    @ApiModelProperty(value = "卸货省编码")
    private String unloadingProvinceCode;

    @ApiModelProperty(value = "卸货省名称")
    private String unloadingProvinceName;

    @ApiModelProperty(value = "卸货市编码")
    private String unloadingCityCode;

    @ApiModelProperty(value = "卸货市名称")
    private String unloadingCityName;

    @ApiModelProperty(value = "卸货区编码")
    private String unloadingDistrictCode;

    @ApiModelProperty(value = "卸货区名称")
    private String unloadingDistrictName;

    @NotBlank(message = "卸货详细地址不能为空")
    @ApiModelProperty(value = "卸货详细地址")
    private String unloadingAddress;
    
    @ApiModelProperty(value = "卸货地址经度")
    private BigDecimal unloadingLongitude;
    
    @ApiModelProperty(value = "卸货地址纬度")
    private BigDecimal unloadingLatitude;

    @ApiModelProperty(value = "货主信息")
    private CustomerInfoRes customerInfoRespVo;
} 