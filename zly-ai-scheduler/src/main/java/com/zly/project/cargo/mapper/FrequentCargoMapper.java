package com.zly.project.cargo.mapper;

import com.zly.project.cargo.controller.vo.FrequentCargoQueryVo;
import com.zly.project.cargo.controller.vo.FrequentCargoRespVo;
import com.zly.project.cargo.domain.FrequentCargo;

import java.util.List;

/**
 * 常发货源Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
public interface FrequentCargoMapper {
    
    /**
     * 新增常发货源
     * 
     * @param frequentCargo 常发货源
     * @return 结果
     */
    int insertFrequentCargo(FrequentCargo frequentCargo);
    
    /**
     * 通过ID查询常发货源信息
     *
     * @param id 常发货源ID
     * @return 常发货源信息
     */
    FrequentCargo selectFrequentCargoById(Long id);
    
    /**
     * 修改常发货源
     *
     * @param frequentCargo 常发货源信息
     * @return 结果
     */
    int updateFrequentCargo(FrequentCargo frequentCargo);
    
    /**
     * 查询常发货源列表
     *
     * @param frequentCargo 常发货源信息
     * @return 常发货源集合
     */
    List<FrequentCargo> selectFrequentCargoList(FrequentCargo frequentCargo);
    
    /**
     * 关联查询常发货源列表（包含货主信息）
     *
     * @param queryVo 查询参数
     * @return 常发货源集合
     */
    List<FrequentCargoRespVo> selectFrequentCargoListWithCarrier(FrequentCargoQueryVo queryVo);
    
    /**
     * 检查是否存在相同的常发货源
     * 
     * @param frequentCargo 常发货源信息
     * @return 结果
     */
    FrequentCargo checkDuplicateFrequentCargo(FrequentCargo frequentCargo);
} 