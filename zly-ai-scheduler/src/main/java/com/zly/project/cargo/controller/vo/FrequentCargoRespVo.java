package com.zly.project.cargo.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 常发货源响应VO对象
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@ApiModel("常发货源响应VO对象")
@Data
public class FrequentCargoRespVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "货主关联ID")
    private Long customerId;

    @ApiModelProperty(value = "货主名称")
    private String customerName;

    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @ApiModelProperty(value = "货物类型")
    private String cargoType;

    @ApiModelProperty(value = "包装方式")
    private String packagingMethod;

    @ApiModelProperty(value = "装货完整地址")
    private String loadingFullAddress;

    @ApiModelProperty(value = "卸货完整地址")
    private String unloadingFullAddress;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系电话")
    private String carrierContact;

    @ApiModelProperty(value = "状态(0-正常,1-已删除)")
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 