package com.zly.project.cargo.controller.vo;

import com.zly.framework.web.page.PageDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 常发货源查询VO对象
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@ApiModel("常发货源查询VO对象")
@Data
public class FrequentCargoQueryVo extends PageDomain {
    
    @ApiModelProperty(value = "货主名称")
    private String customerName;
    
    @ApiModelProperty(value = "货物名称")
    private String cargoName;
    
    @ApiModelProperty(value = "装货地址")
    private String loadingAddress;
    
    @ApiModelProperty(value = "卸货地址")
    private String unloadingAddress;
    
    @ApiModelProperty(value = "状态(0-正常,1-已删除)")
    private String status;
    
    @ApiModelProperty(value = "创建开始日期")
    private String createTimeStart;
    
    @ApiModelProperty(value = "创建结束日期")
    private String createTimeEnd;
} 