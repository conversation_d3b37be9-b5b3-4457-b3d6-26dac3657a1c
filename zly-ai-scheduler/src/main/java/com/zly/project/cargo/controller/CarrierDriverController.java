package com.zly.project.cargo.controller;

import com.zly.enums.CarrierStatusEnum;
import com.zly.enums.PlateColorEnum;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.cargo.controller.vo.CarrierDriverRespVo;
import com.zly.project.cargo.domain.CarrierDriver;
import com.zly.project.cargo.service.ICarrierDriverService;
import com.zly.project.cargo.service.IDailyCargoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 承运司机Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Api(tags = "管理后台 - 承运司机管理")
@RestController
@RequestMapping("/cargo/carrier-driver")
@Slf4j
public class CarrierDriverController extends BaseController {

    @Resource
    private ICarrierDriverService carrierDriverService;

    @Resource
    private IDailyCargoService dailyCargoService;

    /**
     * 查询承运司机列表
     */
//    @ApiOperation("查询承运司机列表")
//    @PreAuthorize("@ss.hasPermi('scheduler:carrierDriver:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(CarrierDriver carrierDriver) {
//        log.info("查询承运司机列表，查询条件：{}", carrierDriver);
//        startPage();
//        List<CarrierDriverRespVo> list = carrierDriverService.selectCarrierDriverRespList(carrierDriver);
//        log.info("查询到{}条承运司机记录", list != null ? list.size() : 0);
//        return getDataTable(list);
//    }

    /**
     * 根据货源ID查询承运司机列表
     */
    @ApiOperation("根据货源ID查询承运司机列表")
    @PreAuthorize("@ss.hasPermi('scheduler:carrierDriver:list')")
    @GetMapping("/list/cargo/{cargoId}")
    public AjaxResult listByCargoId(@PathVariable("cargoId") Long cargoId) {
        log.info("根据货源ID查询承运司机列表，货源ID：{}", cargoId);
        List<CarrierDriverRespVo> list = carrierDriverService.selectDriverRespVosByCargoId(cargoId);
        log.info("查询到{}条承运司机记录", list != null ? list.size() : 0);
        return AjaxResult.success(list);
    }

    /**
     * 获取承运司机详情
     */
    @ApiOperation("获取承运司机详情")
    @PreAuthorize("@ss.hasPermi('scheduler:carrierDriver:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        log.info("获取承运司机详情，ID：{}", id);
        CarrierDriverRespVo carrierDriverRespVo = carrierDriverService.getCarrierDriverById(id);
        return AjaxResult.success(carrierDriverRespVo);
    }

    /**
     * 更新承运状态
     */
    @ApiOperation("更新承运状态")
    @PreAuthorize("@ss.hasPermi('scheduler:carrierDriver:edit')")
    @Log(title = "承运司机", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/status/{status}")
    public AjaxResult updateStatus(@PathVariable("id") Long id, @PathVariable("status") String status) {
        log.info("更新承运状态，ID：{}，状态：{}", id, status);
        return toAjax(carrierDriverService.updateCarrierStatus(id, status));
    }
} 