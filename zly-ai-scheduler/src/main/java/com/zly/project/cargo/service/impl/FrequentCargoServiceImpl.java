package com.zly.project.cargo.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.enums.ErrorCodeConstants;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.cargo.controller.vo.DailyCargoReqVo;
import com.zly.project.cargo.controller.vo.FrequentCargoQueryVo;
import com.zly.project.cargo.controller.vo.FrequentCargoReqVo;
import com.zly.project.cargo.controller.vo.FrequentCargoRespVo;
import com.zly.project.cargo.domain.DailyCargo;
import com.zly.project.cargo.domain.FrequentCargo;
import com.zly.project.cargo.mapper.FrequentCargoMapper;
import com.zly.project.cargo.service.IDailyCargoService;
import com.zly.project.cargo.service.IFrequentCargoService;
import com.zly.project.common.vo.AddressInfo;
import com.zly.project.scheduler.controller.vo.CustomerInfoRes;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import com.zly.project.scheduler.service.ApiCustomerInfoService;
import com.zly.project.scheduler.service.ICustomerInfoService;
import com.zly.project.system.service.impl.SysClientLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 常发货源Service实现
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Slf4j
@Service
public class FrequentCargoServiceImpl implements IFrequentCargoService {

    @Resource
    private FrequentCargoMapper frequentCargoMapper;

    @Resource
    private ICustomerInfoService carrierInfoService;

    @Resource
    private ApiCustomerInfoService apiCustomerInfoService;

    @Resource
    private IDailyCargoService dailyCargoService;

    @Resource
    private SysClientLogService sysClientLogService;

    /**
     * 新增常发货源
     *
     * @param reqVo 常发货源请求信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addFrequentCargo(FrequentCargoReqVo reqVo) {
        Long customerId = reqVo.getCustomerId();
        CustomerInfoRes respVoInfoById = apiCustomerInfoService.getCustomerRespVoInfoById(customerId);
        if (respVoInfoById == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货主信息不存在[常发货源]");
        }
        Integer state = respVoInfoById.getState();
        if (state == null || state!=0){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "合作状态不正确，请查看");
        }

        // 创建常发货源对象
        FrequentCargo frequentCargo = new FrequentCargo();
        BeanUtils.copyProperties(reqVo, frequentCargo);
        // 设置状态为正常
        frequentCargo.setStatus("0");
        // 校验是否存在重复的常发货源
        FrequentCargo existingCargo = frequentCargoMapper.checkDuplicateFrequentCargo(frequentCargo);
        if (existingCargo != null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "存在相同的货源，请勿重复保存");
        }
        frequentCargo.setId(TextUtil.generateId());
        // 设置创建人和创建时间
        frequentCargo.setCreateBy(SecurityUtils.getUsername());
        frequentCargo.setCreateTime(DateUtils.getNowDate());
        processAddressCoordinates(frequentCargo);
        // 插入数据库
        int i = frequentCargoMapper.insertFrequentCargo(frequentCargo);
        Long frequentCargoId = frequentCargo.getId();
        String actionName = "新增常发货源(货住ID：" + frequentCargo.getCustomerId() + "，货物：" + frequentCargo.getCargoName() + ")";
        actionName += "，请求参数：" + JSONUtil.toJsonStr(frequentCargo);
        sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_FREQUENT_CARGO, BusinessConstants.ACTION_TYPE_ADD, StringUtils.substring(actionName, 0, 5000),String.valueOf(frequentCargoId));
        return i;
    }

    /**
     * 编辑常发货源
     * 
     * @param reqVo 常发货源请求信息
     * @param id 常发货源ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateFrequentCargo(FrequentCargoReqVo reqVo, Long id) {
        // 校验货源是否存在
        FrequentCargo frequentCargo = frequentCargoMapper.selectFrequentCargoById(id);
        if (frequentCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "常发货源不存在");
        }

        Long customerId = reqVo.getCustomerId();
        CustomerInfoRes respVoInfoById = apiCustomerInfoService.getCustomerRespVoInfoById(customerId);
        if (respVoInfoById == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货主信息不存在[常发货源]");
        }
        Integer state = respVoInfoById.getState();
        if (state == null || state!=0){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "合作状态不正确，请查看");
        }

        // 更新货源信息
        BeanUtils.copyProperties(reqVo, frequentCargo);
        frequentCargo.setStatus("0");
        // 保持原有的ID和状态
        frequentCargo.setId(null);
        // 校验是否存在重复的常发货源
        FrequentCargo existingCargo = frequentCargoMapper.checkDuplicateFrequentCargo(frequentCargo);
        if (existingCargo != null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "存在相同的货源，请勿重复保存");
        }
        frequentCargo.setId(id);
        // 设置更新人和更新时间
        frequentCargo.setUpdateBy(SecurityUtils.getUsername());
        frequentCargo.setUpdateTime(DateUtils.getNowDate());
        processAddressCoordinates(frequentCargo);
        int i = frequentCargoMapper.updateFrequentCargo(frequentCargo);
        log.info("updateFrequentCargo,i->{}",i);
        //记录日志
        String action = "编辑常发货源,参数:" + JSONUtil.toJsonStr(frequentCargo);
        sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_FREQUENT_CARGO, BusinessConstants.ACTION_TYPE_UPDATE, action,String.valueOf(frequentCargo.getId()), Collections.singletonList(frequentCargo.getId()));
        return i;
    }



    /**
     * 处理地址经纬度信息
     *
     * @param frequentCargo 每日货源对象
     */
    private void processAddressCoordinates(FrequentCargo frequentCargo) {
        try {
            // 处理装货地址经纬度
            String loadingFullAddress = dailyCargoService.assembleFullAddress(frequentCargo.getLoadingProvinceName(), frequentCargo.getLoadingCityName(), frequentCargo.getLoadingDistrictName(),
                    frequentCargo.getLoadingAddress());
            if (StringUtils.isNotBlank(loadingFullAddress)) {
                AddressInfo loadingAddressInfo = new AddressInfo();
                loadingAddressInfo.setAddressDetail(loadingFullAddress);
                AjaxResult loadingCargoResult = dailyCargoService.getAddressInfo(loadingAddressInfo);

                if (loadingCargoResult.isSuccess()) {
                    AddressInfo processedLoadingAddress = (AddressInfo) loadingCargoResult.get("data");
                    if (processedLoadingAddress != null) {
                        // 设置装货地址经纬度
                        if (processedLoadingAddress.getLongitude() != null) {
                            frequentCargo.setLoadingLongitude(new java.math.BigDecimal(processedLoadingAddress.getLongitude()));
                        }
                        if (processedLoadingAddress.getLatitude() != null) {
                            frequentCargo.setLoadingLatitude(new java.math.BigDecimal(processedLoadingAddress.getLatitude()));
                        }
                        log.info("成功获取常发货源装货地址经纬度 - 地址: {}, 经度: {}, 纬度: {}", loadingFullAddress, processedLoadingAddress.getLongitude(), processedLoadingAddress.getLatitude());
                    }
                } else {
                    log.warn("获取常发货源装货地址经纬度失败 - 地址: {}, 错误: {}", loadingFullAddress, loadingCargoResult.get("msg"));
                }
            }
            // 处理卸货地址经纬度
            String unloadingFullAddress = dailyCargoService.assembleFullAddress(frequentCargo.getUnloadingProvinceName(), frequentCargo.getUnloadingCityName(), frequentCargo.getUnloadingDistrictName(),
                    frequentCargo.getUnloadingAddress());
            if (StringUtils.isNotBlank(unloadingFullAddress)) {
                AddressInfo unloadingAddressInfo = new AddressInfo();
                unloadingAddressInfo.setAddressDetail(unloadingFullAddress);
                AjaxResult unloadingResult = dailyCargoService.getAddressInfo(unloadingAddressInfo);
                if (unloadingResult.isSuccess()) {
                    AddressInfo processedUnloadingAddress = (AddressInfo) unloadingResult.get("data");
                    if (processedUnloadingAddress != null) {
                        // 设置卸货地址经纬度
                        if (processedUnloadingAddress.getLongitude() != null) {
                            frequentCargo.setUnloadingLongitude(new BigDecimal(processedUnloadingAddress.getLongitude()));
                        }
                        if (processedUnloadingAddress.getLatitude() != null) {
                            frequentCargo.setUnloadingLatitude(new BigDecimal(processedUnloadingAddress.getLatitude()));
                        }
                        log.info("成功获取常发货源卸货地址经纬度 - 地址: {}, 经度: {}, 纬度: {}", unloadingFullAddress, processedUnloadingAddress.getLongitude(), processedUnloadingAddress.getLatitude());
                    }
                } else {
                    log.warn("获取常发货源卸货地址经纬度失败 - 地址: {}, 错误: {}", unloadingFullAddress, unloadingResult.get("msg"));
                }
            }
        } catch (Exception e) {
            log.error("处理地址经纬度信息异常", e);
        }
    }

    /**
     * 根据ID查询常发货源详情
     *
     * @param id 常发货源ID
     * @return 常发货源信息
     */
    @Override
    public FrequentCargoReqVo getFrequentCargoById(Long id) {
        // 查询货源信息
        FrequentCargo frequentCargo = frequentCargoMapper.selectFrequentCargoById(id);
        if (frequentCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "常发货源不存在");
        }
        Long customerId = frequentCargo.getCustomerId();
        CustomerInfoRes respVoInfoById = apiCustomerInfoService.getCustomerRespVoInfoById(customerId);

        // 转换为请求VO对象
        FrequentCargoReqVo reqVo = new FrequentCargoReqVo();
        BeanUtils.copyProperties(frequentCargo, reqVo);
        reqVo.setCustomerInfoRespVo(respVoInfoById);
        return reqVo;
    }
    
    /**
     * 更新常发货源状态
     *
     * @param id 常发货源ID
     * @param status 状态(0-正常,1-已删除)
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCargoStatus(Long id, String status) {
        // 校验货源是否存在
        FrequentCargo frequentCargo = frequentCargoMapper.selectFrequentCargoById(id);
        if (frequentCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "常发货源不存在");
        }
        // 如果当前状态已经是目标状态，则直接返回成功
        if (status.equals(frequentCargo.getStatus())) {
            return 1;
        }
        // 更新状态
        frequentCargo.setStatus(status);
        frequentCargo.setUpdateBy(SecurityUtils.getUsername());
        frequentCargo.setUpdateTime(DateUtils.getNowDate());
        return frequentCargoMapper.updateFrequentCargo(frequentCargo);
    }
    
    /**
     * 分页查询常发货源列表
     *
     * @param queryVo 查询参数
     * @return 常发货源列表
     */
    @Override
    public List<FrequentCargoRespVo> selectFrequentCargoPageList(FrequentCargoQueryVo queryVo) {
        return frequentCargoMapper.selectFrequentCargoListWithCarrier(queryVo);
    }



    @Override
    public DailyCargoReqVo copyFrequentCargo(Long frequentId) {
        log.info("copyFrequentCargo,frequentId:{}",frequentId);
        // 获取常发货源信息
        FrequentCargoReqVo frequentCargoReqVo = getSelf().getFrequentCargoById(frequentId);
        if (frequentCargoReqVo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "常发货源不存在");
        }
        DailyCargoReqVo reqVo = new DailyCargoReqVo();
        BeanUtils.copyProperties(frequentCargoReqVo,reqVo);
        // 设置为常发货源
        reqVo.setIsFrequentSource("1");
		return reqVo;
	}

    /**
     * 删除常发货源
     *
     * @param id 常发货源ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFrequentCargo(Long id) {
        // 校验货源是否存在
        FrequentCargo frequentCargo = frequentCargoMapper.selectFrequentCargoById(id);
        if (frequentCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "常发货源不存在");
        }
        // 设置deleted字段为1表示已删除
        frequentCargo.setDeleted(true);
        frequentCargo.setUpdateBy(SecurityUtils.getUsername());
        frequentCargo.setUpdateTime(DateUtils.getNowDate());
        log.info("删除常发货源，ID：{}，操作人：{}", id, SecurityUtils.getUsername());
        int i = frequentCargoMapper.updateFrequentCargo(frequentCargo);
        Long frequentCargoId = frequentCargo.getId();
        String actionName = "删除常发货源(货源ID：" + frequentCargo.getId() +")";
        actionName += "，请求参数：" + JSONUtil.toJsonStr(frequentCargo);
        sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_FREQUENT_CARGO, BusinessConstants.ACTION_TYPE_DELETE, StringUtils.substring(actionName, 0, 5000),String.valueOf(frequentCargoId));
        return i;
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private FrequentCargoServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}