package com.zly.project.cargo.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.web.page.PageDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 每日货源查询参数VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("每日货源查询参数VO对象")
@Data
public class DailyCargoQueryVo extends PageDomain {
    
    @ApiModelProperty(value = "货主名称")
    private String customerName;

    @ApiModelProperty(value = "货主Id集合")
    List<String> customerIdList;
    
    @ApiModelProperty(value = "货物名称")
    private String cargoName;
    
    @ApiModelProperty(value = "装货地址")
    private String loadingAddress;
    
    @ApiModelProperty(value = "卸货地址")
    private String unloadingAddress;
    
    @ApiModelProperty(value = "状态(0-正常上架,1-已下架)")
    private String status;
    
    @ApiModelProperty(value = "AI调度状态(0-未开始,1-调度中,2-调度结束,3-匹配完成)")
    private String aiScheduleStatus;
    
    @ApiModelProperty(value = "运力配置(0-无,1-部分匹配,2-匹配完成)")
    private String transportCapacity;
    
    @ApiModelProperty(value = "上架开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date onlineTimeStart;
    
    @ApiModelProperty(value = "上架结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date onlineTimeEnd;
    
    @ApiModelProperty(value = "下架开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offlineTimeStart;
    
    @ApiModelProperty(value = "下架结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offlineTimeEnd;
} 