package com.zly.project.cargo.controller;

import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.cargo.controller.vo.CarrierDriverReqVo;
import com.zly.project.cargo.controller.vo.DailyCargoQueryVo;
import com.zly.project.cargo.controller.vo.DailyCargoReqVo;
import com.zly.project.cargo.controller.vo.DailyCargoRespVo;
import com.zly.project.cargo.controller.vo.TransportRouteReqVo;
import com.zly.project.cargo.domain.CarrierDriver;
import com.zly.project.cargo.service.IDailyCargoService;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 每日货源Controller
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@Api(tags = "管理后台 - 每日货源管理")
@RestController
@RequestMapping("/cargo/daily")
@Slf4j
public class DailyCargoController extends BaseController {

    @Resource
    private IDailyCargoService dailyCargoService;

    /**
     * 新增每日货源
     */
    @ApiOperation("新增每日货源")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:add')")
    @Log(title = "每日货源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody DailyCargoReqVo reqVo) {
        // 添加每日货源
        return AjaxResult.success(dailyCargoService.addDailyCargo(reqVo));
    }

    /**
     * 编辑每日货源
     */
    @ApiOperation("编辑每日货源")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:edit')")
    @Log(title = "每日货源", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    public AjaxResult edit(@PathVariable("id") Long id, @Valid @RequestBody DailyCargoReqVo reqVo) {
        return toAjax(dailyCargoService.updateDailyCargo(reqVo, id));
    }

    /**
     * 获取每日货源详情
     */
    @ApiOperation("获取每日货源详情")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        log.info("获取每日货源详情，ID：{}", id);
        DailyCargoReqVo dailyCargoReqVo = dailyCargoService.getDailyCargoById(id);
        log.info("获取到的每日货源详情：{}", dailyCargoReqVo);
        // 检查承运司机信息是否已加载
        if (dailyCargoReqVo.getCarrierDrivers() != null) {
            log.info("承运司机信息数量：{}", dailyCargoReqVo.getCarrierDrivers().size());
        }
        return AjaxResult.success(dailyCargoReqVo);
    }

    /**
     * 分页查询每日货源列表
     */
    @ApiOperation("分页查询每日货源列表")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:list')")
    @PostMapping("/list")
    public TableDataInfo pageList(@RequestBody DailyCargoQueryVo queryVo) {
        startPageByPageDomain(queryVo);
        List<DailyCargoRespVo> list = dailyCargoService.selectDailyCargoPageList(queryVo);
        return getDataTable(list);
    }

    /**
     * 上架/下架每日货源
     */
    @ApiOperation("上架/下架每日货源")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:edit')")
    @Log(title = "每日货源", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}/{status}")
    public AjaxResult changeStatus(@PathVariable("id") Long id, @PathVariable("status") String status) {
        return toAjax(dailyCargoService.updateCargoStatus(id, status));
    }
    
    /**
     * 组装调度任务参数
     */
    @ApiOperation("组装调度任务参数")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:schedule')")
    @GetMapping("/{id}/schedule-task")
    public AjaxResult assembleScheduleTask(@PathVariable("id") Long id) {
        return AjaxResult.success(dailyCargoService.assembleScheduleTask(id));
    }
    
    /**
     * 更新AI调度状态
     */
    @ApiOperation("更新AI调度状态")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:edit')")
    @Log(title = "每日货源", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/ai-status/{status}")
    public AjaxResult updateAiStatus(
            @PathVariable("id") Long id,
            @PathVariable("status") String status,
            @RequestParam(required = false) Long taskId) {
        return toAjax(dailyCargoService.updateAiScheduleStatus(id, status, taskId));
    }
    
    /**
     * 上传承运司机信息
     */
    @ApiOperation("上传承运司机信息")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:carrier:upload')")
    @Log(title = "承运司机", businessType = BusinessType.INSERT)
    @PostMapping("/carrier-driver")
    public AjaxResult uploadCarrierDriver(@Valid @RequestBody CarrierDriverReqVo reqVo) {
        // 将请求VO转换为实体对象
        CarrierDriver carrierDriver = new CarrierDriver();
        BeanUtils.copyProperties(reqVo, carrierDriver);

        return toAjax(dailyCargoService.uploadCarrierDriver(carrierDriver));
    }
    
    /**
     * 取消承运司机合作
     */
    @ApiOperation("取消承运司机合作")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:carrier:cancel')")
    @Log(title = "承运司机", businessType = BusinessType.UPDATE)
    @DeleteMapping("/carrier-driver/{id}")
    public AjaxResult cancelCarrierDriver(@PathVariable("id") Long id) {
        return toAjax(dailyCargoService.cancelCarrierDriver(id, 1));
    }
    
    /**
     * 获取运输路线简称
     */
    @ApiOperation("获取运输路线简称")
    @PreAuthorize("@ss.hasPermi('scheduler:dailyCargo:carrier:route')")
    @PostMapping("/transport-route")
    public AjaxResult getTransportRoute(@RequestBody TransportRouteReqVo reqVo) {
        return AjaxResult.success(dailyCargoService.getTransportRoute(reqVo));
    }

    /**
     * 测试自动下架处理
     */
    @Anonymous
    @ApiOperation("测试自动下架处理")
    @PostMapping("/auto-offline-test")
    public AjaxResult testAutoOfflineProcess() {
        dailyCargoService.autoOfflineProcess();
        return AjaxResult.success("自动下架处理执行成功");
    }
}