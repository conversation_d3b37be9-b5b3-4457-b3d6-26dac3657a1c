package com.zly.project.cargo.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 中国省市区实体类
 */
@Data
public class ChinaProvincesCitiesAreas {
    /** 主键ID */
    @ApiModelProperty("主键ID")
    private Long id;
    
    /** 区域编码 */
    @ApiModelProperty("区域编码")
    private String areaCode;
    
    /** 区域名称 */
    @ApiModelProperty("区域名称")
    private String areaName;
    
    /** 区域简称 */
    @ApiModelProperty("区域简称")
    private String shortName;
    
    /** 父级编码 */
    @ApiModelProperty("父级编码")
    private String pCode;
    
    /** 区域类型 */
    @ApiModelProperty("区域类型")
    private Integer areaType;
    
    /** 状态 */
    @ApiModelProperty("状态")
    private Integer state;

    @ApiModelProperty("创建人")
    private Long creator;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("修改时间")
    private Date modifyTime;


}