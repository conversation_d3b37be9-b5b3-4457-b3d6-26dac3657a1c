package com.zly.project.cargo.mapper;

import com.zly.project.cargo.domain.CarrierDriver;
import org.apache.ibatis.annotations.MapKey;

import java.util.List;
import java.util.Map;

/**
 * 承运司机Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface CarrierDriverMapper {
    
    /**
     * 新增承运司机
     * 
     * @param carrierDriver 承运司机
     * @return 结果
     */
    public int insertCarrierDriver(CarrierDriver carrierDriver);
    
    /**
     * 通过ID查询承运司机信息
     *
     * @param id 承运司机ID
     * @return 承运司机信息
     */
    public CarrierDriver selectCarrierDriverById(Long id);
    
    /**
     * 修改承运司机
     *
     * @param carrierDriver 承运司机信息
     * @return 结果
     */
    public int updateCarrierDriver(CarrierDriver carrierDriver);
    
    /**
     * 查询承运司机列表
     *
     * @param carrierDriver 承运司机信息
     * @return 承运司机集合
     */
    public List<CarrierDriver> selectCarrierDriverList(CarrierDriver carrierDriver);
    
    /**
     * 查询货源的承运司机数量
     *
     * @param cargoId 货源ID
     * @return 承运司机数量
     */
    public int countDriversByCargoId(Long cargoId);
    
    /**
     * 批量查询货源的承运司机数量
     *
     * @param cargoIds 货源ID列表
     * @return 货源ID与承运司机数量的映射
     */
    @MapKey("cargoId")
    public List<Map<String, Object>> countDriversByCargoIds(List<Long> cargoIds);
    
    /**
     * 根据货源ID查询承运司机列表
     *
     * @param cargoId 货源ID
     * @return 承运司机集合
     */
    public List<CarrierDriver> selectDriversByCustomerId(Long cargoId);
    
    /**
     * 根据调度匹配司机ID查询承运司机列表
     *
     * @param taskDriverMatchId 调度匹配司机ID
     * @return 承运司机集合
     */
    public List<CarrierDriver> selectDriversByTaskDriverMatchId(Long taskDriverMatchId);
} 