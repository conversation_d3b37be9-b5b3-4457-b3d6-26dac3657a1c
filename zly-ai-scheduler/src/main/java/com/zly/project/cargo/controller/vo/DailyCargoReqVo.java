package com.zly.project.cargo.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.project.scheduler.controller.vo.CustomerInfoRes;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 每日货源请求VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@ApiModel("每日货源请求VO对象")
@Data
public class DailyCargoReqVo {
    
    @NotNull(message = "货主ID不能为空")
    @ApiModelProperty(value = "货主ID")
    private Long customerId;

    @NotBlank(message = "货物名称不能为空")
    @ApiModelProperty(value = "货物名称")
    private String cargoName;

    @NotBlank(message = "货物类型不能为空")
    @ApiModelProperty(value = "货物类型")
    private String cargoType;

    @NotBlank(message = "包装方式不能为空")
    @ApiModelProperty(value = "包装方式")
    private String packagingMethod;

    @ApiModelProperty(value = "货物数量(吨)")
    private BigDecimal cargoWeight;

    @ApiModelProperty(value = "货物数量(方)")
    private BigDecimal cargoVolume;

    // 装货地址
    @ApiModelProperty(value = "装货省编码")
    private String loadingProvinceCode;

    @ApiModelProperty(value = "装货省名称")
    private String loadingProvinceName;

    @ApiModelProperty(value = "装货市编码")
    private String loadingCityCode;

    @ApiModelProperty(value = "装货市名称")
    private String loadingCityName;

    @ApiModelProperty(value = "装货区编码")
    private String loadingDistrictCode;

    @ApiModelProperty(value = "装货区名称")
    private String loadingDistrictName;

    @NotBlank(message = "装货详细地址不能为空")
    @ApiModelProperty(value = "装货详细地址")
    private String loadingAddress;

    // 卸货地址
    @ApiModelProperty(value = "卸货省编码")
    private String unloadingProvinceCode;

    @ApiModelProperty(value = "卸货省名称")
    private String unloadingProvinceName;

    @ApiModelProperty(value = "卸货市编码")
    private String unloadingCityCode;

    @ApiModelProperty(value = "卸货市名称")
    private String unloadingCityName;

    @ApiModelProperty(value = "卸货区编码")
    private String unloadingDistrictCode;

    @ApiModelProperty(value = "卸货区名称")
    private String unloadingDistrictName;

    @NotBlank(message = "卸货详细地址不能为空")
    @ApiModelProperty(value = "卸货详细地址")
    private String unloadingAddress;

    // 装货要求
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "装货日期")
    private Date loadingDate;
    
    @ApiModelProperty(value = "装货日期类型(0-今天,1-明天,2-后天,3-其他日期)")
    private String loadingDateType;
    
    @ApiModelProperty(value = "自定义装货日期(当loadingDateType=3时使用)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date customLoadingDate;

    @ApiModelProperty(value = "最早装货时间")
    private String loadingTimeStart;

    @ApiModelProperty(value = "最晚装货时间")
    private String loadingTimeEnd;

    @ApiModelProperty(value = "车辆类型")
    @NotNull(message = "车辆类型不能为空")
    private String vehicleType;
    
    @ApiModelProperty(value = "车辆长度，单位米")
    @NotNull(message = "车辆长度不能为空")
    private Double vehicleLength;

    @ApiModelProperty(value = "车辆数量")
    @NotNull(message = "车辆数量不能为空")
    private Integer vehicleCount;

    @ApiModelProperty(value = "特殊要求")
    private String specialRequirements;

    @ApiModelProperty(value = "是否常发货源(0-否,1-是)")
    private String isFrequentSource;

    @ApiModelProperty(value = "货主信息")
    private CustomerInfoRes customerInfoRespVo;
    
    @ApiModelProperty(value = "承运司机信息集合")
    private List<CarrierDriverRespVo> carrierDrivers;
}