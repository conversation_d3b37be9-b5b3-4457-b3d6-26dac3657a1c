package com.zly.project.cargo.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 每日货源对象 daily_cargo
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DailyCargo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "货源ID")
    private Long id;

    /** 货主关联ID */
    @ApiModelProperty(value = "货主关联ID")
    @Excel(name = "货主关联ID")
    private Long customerId;

    @ApiModelProperty(value = "调度任务关联ID")
    @Excel(name = "调度任务关联ID")
    private Long taskId;

    @ApiModelProperty(value = "常发货源ID")
    @Excel(name = "常发货源ID")
    private Long  frequentCargoId;

    /** 货物名称 */
    @ApiModelProperty(value = "货物名称")
    @Excel(name = "货物名称")
    private String cargoName;

    /** 货物类型 */
    @ApiModelProperty(value = "货物类型")
    @Excel(name = "货物类型")
    private String cargoType;

    /** 包装方式 */
    @ApiModelProperty(value = "包装方式")
    @Excel(name = "包装方式")
    private String packagingMethod;

    /** 货物数量 */
    @ApiModelProperty(value = "货物数量(吨)")
    @Excel(name = "货物数量(吨)")
    private BigDecimal cargoWeight;

    /** 数量单位(吨/方) */
    @ApiModelProperty(value = "货物数量(方)")
    @Excel(name = "货物数量(方)")
    private BigDecimal cargoVolume;

    /** 装货省编码 */
    @ApiModelProperty(value = "装货省编码")
    private String loadingProvinceCode;

    /** 装货省名称 */
    @ApiModelProperty(value = "装货省名称")
    @Excel(name = "装货省名称")
    private String loadingProvinceName;

    /** 装货市编码 */
    @ApiModelProperty(value = "装货市编码")
    private String loadingCityCode;

    /** 装货市名称 */
    @ApiModelProperty(value = "装货市名称")
    @Excel(name = "装货市名称")
    private String loadingCityName;

    /** 装货区编码 */
    @ApiModelProperty(value = "装货区编码")
    private String loadingDistrictCode;

    /** 装货区名称 */
    @ApiModelProperty(value = "装货区名称")
    @Excel(name = "装货区名称")
    private String loadingDistrictName;

    /** 装货详细地址 */
    @ApiModelProperty(value = "装货详细地址")
    @Excel(name = "装货详细地址")
    private String loadingAddress;

    @ApiModelProperty(value = "装货地址经度")
    @Excel(name = "装货地址经度")
    private BigDecimal loadingLongitude;

    @ApiModelProperty(value = "装货地址纬度")
    @Excel(name = "装货地址纬度")
    private BigDecimal loadingLatitude;

    /** 卸货省编码 */
    @ApiModelProperty(value = "卸货省编码")
    private String unloadingProvinceCode;

    /** 卸货省名称 */
    @ApiModelProperty(value = "卸货省名称")
    @Excel(name = "卸货省名称")
    private String unloadingProvinceName;

    /** 卸货市编码 */
    @ApiModelProperty(value = "卸货市编码")
    private String unloadingCityCode;

    /** 卸货市名称 */
    @ApiModelProperty(value = "卸货市名称")
    @Excel(name = "卸货市名称")
    private String unloadingCityName;

    /** 卸货区编码 */
    @ApiModelProperty(value = "卸货区编码")
    private String unloadingDistrictCode;

    /** 卸货区名称 */
    @ApiModelProperty(value = "卸货区名称")
    @Excel(name = "卸货区名称")
    private String unloadingDistrictName;

    /** 卸货详细地址 */
    @ApiModelProperty(value = "卸货详细地址")
    @Excel(name = "卸货详细地址")
    private String unloadingAddress;

    @ApiModelProperty(value = "卸货地址经度")
    @Excel(name = "卸货地址经度")
    private BigDecimal unloadingLongitude;

    @ApiModelProperty(value = "卸货地址纬度")
    @Excel(name = "卸货地址纬度")
    private BigDecimal unloadingLatitude;

    /** 装货日期 */
    @ApiModelProperty(value = "装货日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "装货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loadingDate;

    /** 最早装货时间 */
    @ApiModelProperty(value = "最早装货时间")
    @Excel(name = "最早装货时间")
    private String loadingTimeStart;

    /** 最晚装货时间 */
    @ApiModelProperty(value = "最晚装货时间")
    @Excel(name = "最晚装货时间")
    private String loadingTimeEnd;

    /** 车辆类型 */
    @ApiModelProperty(value = "车辆类型")
    @Excel(name = "车辆类型")
    private String vehicleType;
    
    /** 车辆长度 */
    @ApiModelProperty(value = "车辆长度")
    @Excel(name = "车辆长度")
    private Double vehicleLength;

    /** 车辆数量 */
    @ApiModelProperty(value = "车辆数量")
    @Excel(name = "车辆数量")
    private Integer vehicleCount;

    /** 特殊要求 */
    @ApiModelProperty(value = "特殊要求")
    @Excel(name = "特殊要求")
    private String specialRequirements;

    /** 状态(0-正常,1-已删除) */
    @ApiModelProperty(value = "状态(0-正常,1-已删除)")
    @Excel(name = "状态", readConverterExp = "0=正常,1=已删除")
    private String status;

    /** 是否常发货源(0-否,1-是) */
    @ApiModelProperty(value = "是否常发货源(0-否,1-是)")
    @Excel(name = "是否常发货源", readConverterExp = "0=否,1=是")
    private String isFrequentSource;
    
    /** AI调度状态(未开始、调度中、调度结束、匹配完成) */
    @ApiModelProperty(value = "AI调度状态(0=未开始,1=调度中,2=调度结束)")
    @Excel(name = "AI调度状态", readConverterExp = "0=未开始,1=调度中,2=调度结束")
    private String aiScheduleStatus;
    
    /** 运力配置 */
    @ApiModelProperty(value = "运力配置(0=无,1=部分匹配,2=匹配完成)")
    @Excel(name = "运力配置 0=无，1=部分匹配，2=匹配完成")
    private String transportCapacity;

    /** 上架时间 */
    @ApiModelProperty(value = "上架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上架时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date onlineTime;

    /** 下架时间 */
    @ApiModelProperty(value = "下架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下架时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date offlineTime;

}