package com.zly.project.cargo.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 每日货源列表返回VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
@ApiModel("每日货源列表返回VO对象")
@Data
public class DailyCargoRespVo {
    
    @ApiModelProperty(value = "货源ID")
    private Long id;
    
    @ApiModelProperty(value = "货主ID")
    private Long customerId;
    
    @ApiModelProperty(value = "调度任务关联ID")
    private Long taskId;
    
    @ApiModelProperty(value = "常发货源ID")
    private Long frequentCargoId;
    
    @ApiModelProperty(value = "货主名称")
    private String customerName;
    
    @ApiModelProperty(value = "货物名称")
    private String cargoName;
    
    @ApiModelProperty(value = "货物类型")
    private String cargoType;
    
    @ApiModelProperty(value = "包装方式")
    private String packagingMethod;

    @ApiModelProperty(value = "货物数量(吨)")
    private BigDecimal cargoWeight;

    @ApiModelProperty(value = "货物数量(方)")
    private BigDecimal cargoVolume;

    @ApiModelProperty(value = "装货地址")
    private String loadingAddress;

    @ApiModelProperty(value = "装货地址经度")
    private BigDecimal loadingLongitude;

    @ApiModelProperty(value = "装货地址纬度")
    private BigDecimal loadingLatitude;
    
    @ApiModelProperty(value = "卸货地址")
    private String unloadingAddress;

    @ApiModelProperty(value = "卸货地址经度")
    private BigDecimal unloadingLongitude;

    @ApiModelProperty(value = "卸货地址纬度")
    private BigDecimal unloadingLatitude;
    
    @ApiModelProperty(value = "装货日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loadingDate;
    
    @ApiModelProperty(value = "装货时间")
    private String loadingTime;
    
    @ApiModelProperty(value = "格式化后的装货时间(如: 6.4 9:00-17:00)")
    private String formattedLoadingTime;
    
    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;
    
    @ApiModelProperty(value = "车辆长度，单位米")
    private Double vehicleLength;
    
    @ApiModelProperty(value = "车辆数量")
    private Integer vehicleCount;
    
    @ApiModelProperty(value = "特殊要求")
    private String specialRequirements;
    
    @ApiModelProperty(value = "货主联系人")
    private String contactPerson;
    
    @ApiModelProperty(value = "货主联系方式")
    private String carrierContact;
    
    @ApiModelProperty(value = "AI调度状态(0-未开始,1-调度中,2-调度结束,3-匹配完成)")
    private String aiScheduleStatus;
    
    @ApiModelProperty(value = "运力配置(0-无,1-部分匹配,2-匹配完成)")
    private String transportCapacity;
    
    @ApiModelProperty(value = "是否常发货源(0-否,1-是)")
    private String isFrequentSource;
    
    @ApiModelProperty(value = "状态(0-正常上架,1-已下架)")
    private String status;
    
    @ApiModelProperty(value = "上架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date onlineTime;
    
    @ApiModelProperty(value = "下架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date offlineTime;
    
    @ApiModelProperty(value = "意向司机数量")
    private Integer interestedDriverCount;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 