package com.zly.project.cargo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.enums.ErrorCodeConstants;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.cargo.controller.vo.*;
import com.zly.project.cargo.domain.CarrierDriver;
import com.zly.project.cargo.domain.DailyCargo;
import com.zly.project.cargo.domain.FrequentCargo;
import com.zly.project.cargo.mapper.DailyCargoMapper;
import com.zly.project.cargo.mapper.FrequentCargoMapper;
import com.zly.project.cargo.mapper.CarrierDriverMapper;
import com.zly.project.cargo.service.ICarrierDriverService;
import com.zly.project.cargo.service.IDailyCargoService;
import com.zly.project.common.vo.AddressInfo;
import com.zly.project.driver.domain.SchedulerDriverProfile;
import com.zly.project.driver.domain.req.SchedulerDriverProfileReq;
import com.zly.project.driver.service.ISchedulerDriverProfileService;
import com.zly.project.match.service.TaskDriverMatchService;
import com.zly.project.scheduler.controller.vo.*;
import com.zly.project.scheduler.service.ApiCustomerInfoService;
import com.zly.project.scheduler.service.ICustomerInfoService;
import com.zly.project.system.service.impl.SysClientLogService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.common.utils.AddressUtils.concatPcdTwo;
import static com.zly.common.utils.DateUtils.getDate;
import static com.zly.framework.web.domain.AjaxResult.CODE_TAG;
import static com.zly.project.scheduler.service.impl.CustomerInfoServiceImpl.getEndCalendar;

/**
 * 每日货源Service实现
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@Slf4j
@Service
public class DailyCargoServiceImpl implements IDailyCargoService {

    @Resource
    private DailyCargoMapper dailyCargoMapper;

    @Resource
    private ICustomerInfoService carrierInfoService;

    @Resource
    private ApiCustomerInfoService apiCustomerInfoService;

    @Resource
    private ICarrierDriverService carrierDriverService;

    @Resource
    private FrequentCargoMapper frequentCargoMapper;

    @Resource
    private CarrierDriverMapper carrierDriverMapper;

    @Resource
    @Lazy
    private TaskDriverMatchService taskDriverMatchService;

    @Resource
    private ISchedulerDriverProfileService schedulerDriverProfileService;

    @Resource
    private SysClientLogService sysClientLogService;

    @Resource
    private RestTemplate restTemplate;

    @Value("${custom.areas.list.apiUrl}")
    private String areasListApiUrl;

    @Value("${custom.areas.list.resolveAddressIpLimit}")
    private String resolveAddressIpLimitUrl;

    /**
     * 新增每日货源
     *
     * @param reqVo 每日货源请求信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addDailyCargo(DailyCargoReqVo reqVo) {
        Long customerId = reqVo.getCustomerId();
        CustomerInfoRes respVoInfoById = apiCustomerInfoService.getCustomerRespVoInfoById(customerId);
        if (respVoInfoById == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货主信息不存在[每日货源]");
        }
        Integer state = respVoInfoById.getState();
        if (state == null || state!=0){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "合作状态不正确，请查看");
        }
        // 处理装货时间字段
        if (StringUtils.isBlank(reqVo.getLoadingTimeStart())) {
            reqVo.setLoadingTimeStart("00:00");
        }
        if (StringUtils.isBlank(reqVo.getLoadingTimeEnd())) {
            reqVo.setLoadingTimeEnd("23:59");
        }
        // 处理装货日期类型
        Date loadingDate = calculateLoadingDateByType(reqVo);
        // 校验装货日期与当前时间
        if (loadingDate.before(DateUtils.getNowDate())) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "最晚装货时间不能早于当前时间");
        }
        // 创建每日货源对象
        DailyCargo dailyCargo = new DailyCargo();
        BeanUtils.copyProperties(reqVo, dailyCargo);
        // 设置处理后的装货日期（只保留年月日）
        dailyCargo.setLoadingDate(truncateTimeFromDate(loadingDate));
        // 设置AI调度状态为未开始
        dailyCargo.setAiScheduleStatus("0");
        // 设置状态为正常
        dailyCargo.setStatus("0");
        // 如果未设置是否常发货源，默认为否
        if (dailyCargo.getIsFrequentSource() == null) {
            dailyCargo.setIsFrequentSource("0");
        }
        // 设置创建人和创建时间
        dailyCargo.setCreateBy(SecurityUtils.getUsername());
        dailyCargo.setCreateTime(DateUtils.getNowDate());
        // 设置上架时间
        dailyCargo.setOnlineTime(DateUtils.getNowDate());
        handleFrequentCargoCheck(reqVo, dailyCargo);
        dailyCargo.setId(TextUtil.generateId());
        int i = dailyCargoMapper.insertDailyCargo(dailyCargo);
        log.info("addDailyCargo,i->{}",i);
        // 插入数据库
        // 处理经度维度数据
        processAddressCoordinates(dailyCargo);
        Long dailyCargoId = dailyCargo.getId();
        String actionName = "新增每日货源(货住ID：" + dailyCargo.getCustomerId() + "，货物：" + dailyCargo.getCargoName() + ")";
        actionName += "，请求参数：" + JSONUtil.toJsonStr(dailyCargo);
        sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_DAILY_CARGO, BusinessConstants.ACTION_TYPE_ADD, StringUtils.substring(actionName, 0, 5000),String.valueOf(dailyCargoId));
        return dailyCargoId;
    }

    /**
     * 处理常发货源的校验
     * @param reqVo  reqVo
     * @param dailyCargo dailyCargo
     */
    private void handleFrequentCargoCheck(DailyCargoReqVo reqVo, DailyCargo dailyCargo) {
        String setFrequentSource = reqVo.getIsFrequentSource();
        if (StringUtils.isNotBlank(setFrequentSource) && StringUtils.equals(setFrequentSource,"1")){
            FrequentCargo frequentCargo = getFrequentCargo(dailyCargo);
            // 校验是否存在重复的常发货源
            frequentCargo.setId(null);
            FrequentCargo existingCargo = frequentCargoMapper.checkDuplicateFrequentCargo(frequentCargo);
            if (existingCargo == null) {
                frequentCargo.setId(TextUtil.generateId());
                frequentCargoMapper.insertFrequentCargo(frequentCargo);
                Long frequentCargoId = frequentCargo.getId();
                String actionName = "设置为常发货源(货住ID：" + frequentCargo.getCustomerId() + "，货物：" + frequentCargo.getCargoName() + ")";
                actionName += "，请求参数：" + JSONUtil.toJsonStr(frequentCargo);
                sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_FREQUENT_CARGO, BusinessConstants.ACTION_TYPE_ADD, StringUtils.substring(actionName, 0, 5000),String.valueOf(frequentCargoId));
                dailyCargo.setFrequentCargoId(frequentCargo.getId());
                dailyCargo.setIsFrequentSource("1");
                log.info("已将每日货源[{}]设置为常发货源，常发货源ID：{}", dailyCargo.getId(), frequentCargo.getId());
            } else {
                log.info("已存在相同的常发货源，不再重复创建，常发货源ID：{}", existingCargo.getId());
                throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "存在相同的货源，请勿重复保存");
            }
        }
    }

    /**
     * 数据转换操作
     * @param dailyCargo dailyCargo
     * @return FrequentCargo
     */
    @NotNull
    private static FrequentCargo getFrequentCargo(DailyCargo dailyCargo) {
        FrequentCargo frequentCargo = new FrequentCargo();
        BeanUtils.copyProperties(dailyCargo,frequentCargo);
        // 设置状态为正常
        frequentCargo.setStatus("0");
        // 设置创建人和创建时间
        frequentCargo.setCreateBy(SecurityUtils.getUsername());
        frequentCargo.setCreateTime(DateUtils.getNowDate());
        return frequentCargo;
    }

    /**
     * 将日期的时间部分截断，只保留年月日
     *
     * @param date 原始日期
     * @return 只包含年月日的日期
     */
    @NotNull
    private Date truncateTimeFromDate(Date date) {
        return getDate(date);
    }


    /**
     * 根据装货日期类型计算实际装货日期
     *
     * @param reqVo 请求参数
     * @return 装货日期
     */
    private Date calculateLoadingDateByType(DailyCargoReqVo reqVo) {
        // 获取装货日期类型
        String loadingDateType = reqVo.getLoadingDateType();
        if (loadingDateType == null || loadingDateType.isEmpty()) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "装货日期类型不能为空");
        }
        
        // 使用当前日期作为基准
        Date today = DateUtils.getNowDate();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);
        
        // 根据类型计算日期
        Date loadingDate;
        switch (loadingDateType) {
            case "0": // 今天
                loadingDate = today;
                break;
            case "1": // 明天
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                loadingDate = calendar.getTime();
                break;
            case "2": // 后天
                calendar.add(Calendar.DAY_OF_MONTH, 2);
                loadingDate = calendar.getTime();
                break;
            case "3": // 其他日期（用户自定义）
                if (reqVo.getCustomLoadingDate() == null) {
                    throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "选择其他日期时，必须指定具体日期");
                }
                loadingDate = reqVo.getCustomLoadingDate();
                // 确保自定义日期只包含年月日
                loadingDate = truncateTimeFromDate(loadingDate);
                break;
            default:
                throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "无效的装货日期类型");
        }
        
        // 处理装货时间
        String loadingTimeEnd = reqVo.getLoadingTimeEnd();
        if (StringUtils.isBlank(loadingTimeEnd)) {
            loadingTimeEnd = "23:59";
        }
        
        // 将日期与最晚装货时间结合
        try {
            Calendar loadingCalendar = getLoadingCalendar(loadingDate, loadingTimeEnd);
            return loadingCalendar.getTime();
        } catch (Exception e) {
            log.error("解析装货时间出错", e);
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "装货时间格式不正确");
        }
    }

    @NotNull
    private static Calendar getLoadingCalendar(Date loadingDate, String loadingTimeEnd) {
        Calendar loadingCalendar = Calendar.getInstance();
        loadingCalendar.setTime(loadingDate);

        // 解析最晚装货时间
        String[] timeParts = loadingTimeEnd.split(":");
        int hour = Integer.parseInt(timeParts[0]);
        int minute = Integer.parseInt(timeParts[1]);

        // 设置时间部分
        loadingCalendar.set(Calendar.HOUR_OF_DAY, hour);
        loadingCalendar.set(Calendar.MINUTE, minute);
        loadingCalendar.set(Calendar.SECOND, 0);
        return loadingCalendar;
    }

    /**
     * 获取合作中的货主列表，用于添加每日货源时选择
     *
     * @return 合作中的货主列表
     */
    @Override
    public List<CustomerInfoRespVo> getActiveCustomerList(Integer type) {
        return carrierInfoService.selectActiveCustomerList(type);
    }

    /**
     * 编辑每日货源
     * 
     * @param reqVo 每日货源请求信息
     * @param id 每日货源ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDailyCargo(DailyCargoReqVo reqVo, Long id) {
        // 校验货源是否存在
        DailyCargo dailyCargo = dailyCargoMapper.selectDailyCargoById(id);
        if (dailyCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "每日货源不存在");
        }

        Long customerId = reqVo.getCustomerId();
        CustomerInfoRes respVoInfoById = apiCustomerInfoService.getCustomerRespVoInfoById(customerId);
        if (respVoInfoById == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货主信息不存在[每日货源]");
        }
        Integer state = respVoInfoById.getState();
        if (state == null || state!=0){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "合作状态不正确，请查看");
        }
        
        // 检查AI调度状态，如果已经开始调度则不允许修改
        if (!"0".equals(dailyCargo.getAiScheduleStatus())) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "该货源已开始AI调度，不能修改");
        }

        // 处理装货时间字段
        if (StringUtils.isBlank(reqVo.getLoadingTimeStart())) {
            reqVo.setLoadingTimeStart("00:00");
        }
        if (StringUtils.isBlank(reqVo.getLoadingTimeEnd())) {
            reqVo.setLoadingTimeEnd("23:59");
        }
        
        // 处理装货日期类型
        Date loadingDate;
        if (reqVo.getLoadingDateType() != null && !reqVo.getLoadingDateType().isEmpty()) {

            loadingDate = calculateLoadingDateByType(reqVo);
            // 校验装货日期与当前时间
            if (loadingDate.before(DateUtils.getNowDate())) {
                throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "最晚装货时间不能早于当前时间");
            }
            // 只保留年月日
            loadingDate = truncateTimeFromDate(loadingDate);
            reqVo.setLoadingDate(loadingDate);
        }
        
        // 更新货源信息
        BeanUtils.copyProperties(reqVo, dailyCargo);
        // 保持原有的AI调度状态和删除状态不变
        dailyCargo.setId(id);
        // 设置更新人和更新时间
        dailyCargo.setUpdateBy(SecurityUtils.getUsername());
        dailyCargo.setUpdateTime(DateUtils.getNowDate());
        handleFrequentCargoCheck(reqVo, dailyCargo);
        
        // 处理地址经纬度信息
        processAddressCoordinates(dailyCargo);

        int i = dailyCargoMapper.updateDailyCargo(dailyCargo);
        log.info("updateDailyCargo,i->{}",i);
        //记录日志
        String action = "编辑每日货源,参数:" + JSONUtil.toJsonStr(dailyCargo);
        sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_DAILY_CARGO, BusinessConstants.ACTION_TYPE_UPDATE, action,String.valueOf(dailyCargo.getId()), Collections.singletonList(dailyCargo.getId()));
        return i;
    }
    
    /**
     * 根据ID查询每日货源详情
     *
     * @param id 每日货源ID
     * @return 每日货源信息
     */
    @Override
    public DailyCargoReqVo getDailyCargoById(Long id) {
        // 查询货源信息
        DailyCargo dailyCargo = dailyCargoMapper.selectDailyCargoById(id);
        if (dailyCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "每日货源不存在");
        }
        Long customerId = dailyCargo.getCustomerId();

        CustomerInfoRes respVoInfoById = apiCustomerInfoService.getCustomerRespVoInfoById(customerId);
        // 转换为请求VO对象
        DailyCargoReqVo reqVo = new DailyCargoReqVo();
        BeanUtils.copyProperties(dailyCargo, reqVo);
        Date loadingDate = reqVo.getLoadingDate();
        
        // 根据装货日期时间，来推断出loadingDateType 装货日期类型，是今天明天后天还是其他日期，并返回
        String loadingDateType = inferLoadingDateType(loadingDate);
        reqVo.setLoadingDateType(loadingDateType);
        
        // 如果是其他日期类型，设置自定义装货日期
        if ("3".equals(loadingDateType)) {
            reqVo.setCustomLoadingDate(truncateTimeFromDate(loadingDate));
        }
        reqVo.setCustomerInfoRespVo(respVoInfoById);
        
        // 在此方法中添加 司机的承运信息集合，根据每日货源ID查询到所有的承运司机集合信息
        List<CarrierDriverRespVo> carrierDrivers = carrierDriverService.selectDriverRespVosByCargoId(id);
        reqVo.setCarrierDrivers(carrierDrivers);
        
        return reqVo;
    }
    
    /**
     * 根据装货日期推断出装货日期类型
     *
     * @param loadingDate 装货日期
     * @return 装货日期类型(0-今天, 1-明天, 2-后天, 3-其他日期)
     */
    private String inferLoadingDateType(Date loadingDate) {
        if (loadingDate == null) {
            return "0"; // 默认为今天
        }
        
        // 获取当前日期（只保留年月日）
        Calendar today = Calendar.getInstance();
        today.setTime(DateUtils.getNowDate());
        resetTimeToMidnight(today);
        
        // 获取装货日期（只保留年月日）
        Calendar loadingCal = Calendar.getInstance();
        loadingCal.setTime(loadingDate);
        resetTimeToMidnight(loadingCal);
        
        // 计算日期差
        long diffInMillis = loadingCal.getTimeInMillis() - today.getTimeInMillis();
        long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);
        
        // 根据日期差判断类型
        if (diffInDays == 0) {
            return "0"; // 今天
        } else if (diffInDays == 1) {
            return "1"; // 明天
        } else if (diffInDays == 2) {
            return "2"; // 后天
        } else {
            return "3"; // 其他日期
        }
    }
    
    /**
     * 重置日历时间为午夜（00:00:00）
     *
     * @param calendar 日历对象
     */
    private void resetTimeToMidnight(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }
    
    /**
     * 更新每日货源状态
     *
     * @param id 每日货源ID
     * @param status 状态(0-正常上架,1-已下架)
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCargoStatus(Long id, String status) {
        // 校验货源是否存在
        DailyCargo dailyCargo = dailyCargoMapper.selectDailyCargoById(id);
        if (dailyCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "每日货源不存在");
        }
        // 如果当前状态已经是目标状态，则直接返回成功
        if (status.equals(dailyCargo.getStatus())) {
            return 1;
        }
        // 检查AI调度状态，如果已经开始调度则不允许下架 暂时不考虑ai调度
//        if ("1".equals(status) && !"0".equals(dailyCargo.getAiScheduleStatus())) {
//            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "该货源已开始AI调度，不能下架");
//        }
        // 更新状态
        dailyCargo.setStatus(status);
        try {
            String username = SecurityUtils.getUsername();
            dailyCargo.setUpdateBy(username);
        }catch (Exception e){
            log.info("updateCargoStatus,获取更新人失败,e->{}",e.getMessage());
            dailyCargo.setUpdateBy(String.valueOf(0));
        }
        dailyCargo.setUpdateTime(DateUtils.getNowDate());
        
        // 如果是下架操作，设置下架时间
        if ("1".equals(status)) {
            dailyCargo.setOfflineTime(DateUtils.getNowDate());
        }
        
        return dailyCargoMapper.updateDailyCargo(dailyCargo);
    }
    
    /**
     * 分页查询每日货源列表
     *
     * @param queryVo 查询参数
     * @return 每日货源列表
     */
    @Override
    public List<DailyCargoRespVo> selectDailyCargoPageList(DailyCargoQueryVo queryVo) {
        String customerName = queryVo.getCustomerName();
        List<String> customerIdLists = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(customerName)){
            //根据货主筛选数据
            CustomerInfoPageReqVo pageReqVo = new CustomerInfoPageReqVo();
            pageReqVo.setCustomerName(customerName);
            TableDataInfo<RemoteCustomerInfoVo> customerInfoList = apiCustomerInfoService.getCustomerInfoList(pageReqVo);
            List<RemoteCustomerInfoVo> rows = customerInfoList.getRows();
            if (CollectionUtil.isNotEmpty(rows)){
                customerIdLists = rows.stream().map(RemoteCustomerInfoVo::getId).collect(Collectors.toList());
            }else {
                return new ArrayList<>(2);
            }
        }
        queryVo.setCustomerIdList(customerIdLists);
        List<DailyCargoRespVo> dailyCargoRespVos = dailyCargoMapper.selectDailyCargoListWithCarrier(queryVo);
        for (DailyCargoRespVo dailyCargoRespVo : dailyCargoRespVos) {
            Date loadingDate = dailyCargoRespVo.getLoadingDate();
            String loadingTime = dailyCargoRespVo.getLoadingTime();
            handleLoadingDate(dailyCargoRespVo, loadingDate, loadingTime);
        }
        if (!dailyCargoRespVos.isEmpty()) {
            // 提取所有货源ID
            List<Long> customerIds = dailyCargoRespVos.stream()
                    .map(DailyCargoRespVo::getId)
                    .collect(java.util.stream.Collectors.toList());

            setInterestedDriverCount(customerIds, dailyCargoRespVos);
            setTransportCapacity(customerIds, dailyCargoRespVos);
        }
        
        return dailyCargoRespVos;
    }

    /**
     * 设置意向司机数量
     * @param cargoIds cargoIds
     * @param dailyCargoRespVos dailyCargoRespVos
     */
    private void setInterestedDriverCount(List<Long> cargoIds, List<DailyCargoRespVo> dailyCargoRespVos) {
        // 查询意向司机数量
        List<Map<String, Object>> driverCountMap = dailyCargoMapper.selectInterestedDriverCountByCargos(cargoIds);

        // 将查询结果转换为Map，方便后续使用
        Map<Long, Integer> cargoDriverCountMap = new java.util.HashMap<>();
        for (Map<String, Object> map : driverCountMap) {
            Long customerId = ((Number) map.get("customerId")).longValue();
            Integer count = ((Number) map.get("interestedDriverCount")).intValue();
            cargoDriverCountMap.put(customerId, count);
        }

        // 设置意向司机数量
        for (DailyCargoRespVo dailyCargoRespVo : dailyCargoRespVos) {
            Integer count = cargoDriverCountMap.get(dailyCargoRespVo.getId());
            dailyCargoRespVo.setInterestedDriverCount(count != null ? count : 0);
        }
    }

    /**
     * 设置运力匹配状态
     * @param cargoIds cargoIds
     * @param dailyCargoRespVos dailyCargoRespVos
     */
    private void setTransportCapacity(List<Long> cargoIds, List<DailyCargoRespVo> dailyCargoRespVos) {
        // 查询承运司机数量
        List<Map<String, Object>> carrierDriverCountMap = carrierDriverMapper.countDriversByCargoIds(cargoIds);

        // 将查询结果转换为Map，方便后续使用
        Map<Long, Integer> cargoCarrierDriverCountMap = new java.util.HashMap<>();
        for (Map<String, Object> map : carrierDriverCountMap) {
            Long customerId = ((Number) map.get("cargoId")).longValue();
            Integer count = ((Number) map.get("driverCount")).intValue();
            cargoCarrierDriverCountMap.put(customerId, count);
        }
        // 设置运力配置状态
        for (DailyCargoRespVo dailyCargoRespVo : dailyCargoRespVos) {
            Integer driverCount = cargoCarrierDriverCountMap.get(dailyCargoRespVo.getId());
            Integer vehicleCount = dailyCargoRespVo.getVehicleCount();
            // 根据承运司机数量与车辆数量的比较确定运力配置状态
            if (driverCount == null || driverCount == 0) {
                // 无承运司机
                dailyCargoRespVo.setTransportCapacity("0");
            } else if (driverCount < vehicleCount) {
                // 部分匹配
                dailyCargoRespVo.setTransportCapacity("1");
            } else {
                // 匹配完成
                dailyCargoRespVo.setTransportCapacity("2");
            }
        }
    }

    /**
     * 处理装货日期的展示
     * @param dailyCargoRespVo dailyCargoRespVo
     * @param loadingDate loadingDate
     * @param loadingTime loadingTime
     */
    private static void handleLoadingDate(DailyCargoRespVo dailyCargoRespVo, Date loadingDate, String loadingTime) {
        if (loadingDate != null) {
            // 格式化日期部分为 "M.d" 格式，如 "6.4"
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(loadingDate);
            int month = calendar.get(Calendar.MONTH) + 1; // 月份从0开始，需要+1
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            String dateStr = month + "." + day;
            // 处理时间部分
            String timeStr = loadingTime;
            if (StringUtils.isBlank(timeStr)) {
                timeStr = "0:00-23:59"; // 默认时间范围
            } else {
                // 检查时间格式是否正确
                String[] timeParts = timeStr.split("-");
                String startTime = timeParts.length > 0 ? timeParts[0].trim() : "";
                String endTime = timeParts.length > 1 ? timeParts[1].trim() : "";
                // 如果开始时间为空，设为默认值
                if (StringUtils.isBlank(startTime)) {
                    startTime = "0:00";
                }
                // 如果结束时间为空，设为默认值
                if (StringUtils.isBlank(endTime)) {
                    endTime = "23:59";
                }
                timeStr = startTime + "-" + endTime;
            }
            // 组合日期和时间
            String formattedLoadingTime = dateStr + " " + timeStr;
            dailyCargoRespVo.setFormattedLoadingTime(formattedLoadingTime);
        }
    }

    /**
     * 组装调度任务参数
     *
     * @param cargoId 货源ID
     * @return 调度任务VO对象
     */
    @Override
    public ScheduleTaskVo assembleScheduleTask(Long cargoId) {
        DailyCargo dailyCargo = dailyCargoMapper.selectDailyCargoById(cargoId);
        if (dailyCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "每日货源不存在");
        }
        String status = dailyCargo.getStatus();
        if (StringUtils.equals(status,"1")){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货源已下架无法执行调度计划");
        }
        String aiScheduleStatus = dailyCargo.getAiScheduleStatus();
        if (!StringUtils.equals(aiScheduleStatus,"0")){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "AI调度状态错误");
        }
        // 查询货主信息
        Long customerId = dailyCargo.getCustomerId();
        CustomerInfoRes respVoInfoById = apiCustomerInfoService.getCustomerRespVoInfoById(customerId);
        if (respVoInfoById == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货主信息不存在");
        }
        Integer state = respVoInfoById.getState();
        if (state == null || state!=0){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "合作状态不正确，请查看");
        }
        // 创建调度任务VO对象
        ScheduleTaskVo scheduleTaskVo = new ScheduleTaskVo();
        BeanUtils.copyProperties(dailyCargo,scheduleTaskVo);
        // 设置基本信息
        scheduleTaskVo.setCargoId(cargoId);
        scheduleTaskVo.setCustomerId(customerId);
        scheduleTaskVo.setCustomerName(respVoInfoById.getCustomerName());
        scheduleTaskVo.setContactPerson(respVoInfoById.getContact());
        scheduleTaskVo.setContactPhone(respVoInfoById.getContactPhone());
        
        // 设置货物信息
        scheduleTaskVo.setCargoName(dailyCargo.getCargoName());
        scheduleTaskVo.setCargoType(dailyCargo.getCargoType());
        scheduleTaskVo.setPackagingMethod(dailyCargo.getPackagingMethod());
        scheduleTaskVo.setCargoWeight(dailyCargo.getCargoWeight());
        scheduleTaskVo.setCargoVolume(dailyCargo.getCargoVolume());

        // 设置运输路线信息
        // 组装完整的装货地址
        String loadingFullAddress = getSelf().assembleFullAddress(
                dailyCargo.getLoadingProvinceName(),
                dailyCargo.getLoadingCityName(),
                dailyCargo.getLoadingDistrictName(),
                dailyCargo.getLoadingAddress()
        );
        scheduleTaskVo.setLoadingFullAddress(loadingFullAddress);

        String startPoint = concatPcdTwo(dailyCargo.getLoadingProvinceName(), dailyCargo.getLoadingCityName(), dailyCargo.getLoadingDistrictName());
        scheduleTaskVo.setStartPoint(startPoint);
        // 组装完整的卸货地址
        String unloadingFullAddress = getSelf().assembleFullAddress(
                dailyCargo.getUnloadingProvinceName(),
                dailyCargo.getUnloadingCityName(),
                dailyCargo.getUnloadingDistrictName(),
                dailyCargo.getUnloadingAddress()
        );
        scheduleTaskVo.setUnloadingFullAddress(unloadingFullAddress);

        String endPoint = concatPcdTwo(dailyCargo.getUnloadingProvinceName(), dailyCargo.getUnloadingCityName(), dailyCargo.getUnloadingDistrictName());
        scheduleTaskVo.setEndPoint(endPoint);

        // 设置时间信息
        scheduleTaskVo.setLoadingDate(dailyCargo.getLoadingDate());
        String loadingTimeStart = dailyCargo.getLoadingTimeStart();
        String loadingTimeEnd = dailyCargo.getLoadingTimeEnd();
        scheduleTaskVo.setLoadingTimeStart(loadingTimeStart);
        scheduleTaskVo.setLoadingTimeEnd(loadingTimeEnd);
        
        // 判断时间格式类型
        String timeFormatType = determineTimeFormatType(loadingTimeStart, loadingTimeEnd);
        scheduleTaskVo.setTimeFormatType(timeFormatType);
        
        // 格式化装货时间
        String formattedLoadingTime = formatLoadingTime(dailyCargo.getLoadingDate(), loadingTimeStart, loadingTimeEnd);
        scheduleTaskVo.setFormattedLoadingTime(formattedLoadingTime);
        
        // 设置车辆要求
        scheduleTaskVo.setVehicleType(dailyCargo.getVehicleType());
        scheduleTaskVo.setVehicleCount(dailyCargo.getVehicleCount());
        scheduleTaskVo.setVehicleLength(dailyCargo.getVehicleLength());
        scheduleTaskVo.setSpecialRequirements(dailyCargo.getSpecialRequirements());
        
        return scheduleTaskVo;
    }
    
    /**
     * 组装完整地址
     *
     * @param province 省名称
     * @param city 市名称
     * @param district 区名称
     * @param address 详细地址
     * @return 完整地址
     */
    @Override
    public String assembleFullAddress(String province, String city, String district, String address) {
        StringBuilder fullAddress = new StringBuilder();
        if (StringUtils.isNotBlank(province)) {
            fullAddress.append(province);
        }
        if (StringUtils.isNotBlank(city)) {
            fullAddress.append(city);
        }
        if (StringUtils.isNotBlank(district)) {
            fullAddress.append(district);
        }
        if (StringUtils.isNotBlank(address)) {
            fullAddress.append(address);
        }
        return fullAddress.toString();
    }
    
    /**
     * 判断时间格式类型
     * 
     * @param loadingTimeStart 最早装货时间
     * @param loadingTimeEnd 最晚装货时间
     * @return 时间格式类型(1-按天计算,2-按小时计算)
     */
    private String determineTimeFormatType(String loadingTimeStart, String loadingTimeEnd) {
        // 如果开始时间为空或为"00:00"，且结束时间为空或为"23:59"，则按天计算
        if ((StringUtils.isBlank(loadingTimeStart) || "00:00".equals(loadingTimeStart)) 
                && (StringUtils.isBlank(loadingTimeEnd) || "23:59".equals(loadingTimeEnd))) {
            return "1"; // 按天计算
        } else {
            return "2"; // 按小时计算
        }
    }
    
    /**
     * 格式化装货时间
     *
     * @param loadingDate 装货日期
     * @param loadingTimeStart 最早装货时间
     * @param loadingTimeEnd 最晚装货时间
     * @return 格式化后的装货时间
     */
    private String formatLoadingTime(Date loadingDate, String loadingTimeStart, String loadingTimeEnd) {
        if (loadingDate == null) {
            return "";
        }
        
        // 格式化日期部分为 "M.d" 格式，如 "6.4"
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(loadingDate);
        int month = calendar.get(Calendar.MONTH) + 1; // 月份从0开始，需要+1
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String dateStr = month + "." + day;
        
        // 处理时间部分
        String startTime = StringUtils.isBlank(loadingTimeStart) ? "0:00" : loadingTimeStart;
        String endTime = StringUtils.isBlank(loadingTimeEnd) ? "23:59" : loadingTimeEnd;
        String timeStr = startTime + "-" + endTime;
        
        // 组合日期和时间
        return dateStr + " " + timeStr;
    }
    
    /**
     * 更新AI调度状态
     *
     * @param customerId 货源ID
     * @param aiScheduleStatus AI调度状态(0-未开始,1-调度中,2-调度结束)
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAiScheduleStatus(Long customerId, String aiScheduleStatus,Long taskId) {
        log.info("updateAiScheduleStatus,cargoId:{},aiScheduleStatus:{},taskId:{}",customerId,aiScheduleStatus,taskId);
        // 查询货源信息
        DailyCargo dailyCargo = dailyCargoMapper.selectDailyCargoById(customerId);
        if (dailyCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "每日货源不存在");
        }
        if (StringUtils.isBlank(aiScheduleStatus)){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "调度状态不能为空");
        }
        if ("1".equals(aiScheduleStatus) && taskId == null){
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "任务ID不能为空");
        }
        // 校验状态变更的合法性
        validateStatusChange(dailyCargo.getAiScheduleStatus(), aiScheduleStatus);
        
        // 更新AI调度状态
        dailyCargo.setAiScheduleStatus(aiScheduleStatus);

        dailyCargo.setUpdateBy(SecurityUtils.getUsername());
        dailyCargo.setUpdateTime(DateUtils.getNowDate());

        if ("1".equals(aiScheduleStatus)){
            dailyCargo.setTaskId(taskId);
        } else if ("2".equals(aiScheduleStatus)) {
            // 调度结束时，更新运力配置状态
            log.info("updateAiScheduleStatus:调度任务已经完成");
        }
        return dailyCargoMapper.updateDailyCargo(dailyCargo);
    }
    
    /**
     * 校验AI调度状态变更的合法性
     *
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     */
    private void validateStatusChange(String currentStatus, String targetStatus) {
        // 状态流转规则：0(未开始) -> 1(调度中) -> 2(调度结束)
        // 不允许跳过状态或回退状态
        
        if (StringUtils.equals(currentStatus, targetStatus)) {
            // 状态相同，无需变更
            return;
        }
        
        // 检查状态流转是否合法
        switch (currentStatus) {
            case "0": // 未开始
                if (!"1".equals(targetStatus)) {
                    throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "未开始状态只能变更为调度中状态");
                }
                break;
            case "1": // 调度中
                if (!"2".equals(targetStatus)) {
                    throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "调度中状态只能变更为调度结束状态");
                }
                break;
            case "2": // 调度完成
                throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "调度完成完成状态不能再变更");
            default:
                throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "无效的当前状态");
        }
    }
    
    /**
     * 自动下架处理
     * 1. 过了计划装车最晚时间的24小时，自动下架
     * 2. 需求车辆数量满足后，当天23:59自动下架
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoOfflineProcess() {
        log.info("开始执行自动下架处理...");
        // 获取当前时间
        Date now = DateUtils.getNowDate();
        // 1. 处理过了计划装车最晚时间24小时的货源
        processExpiredCargos(now);
        // 2. 处理需求车辆数量已满足的货源
        processFulfilledCargos(now);
        log.info("自动下架处理执行完成");
    }
    
    /**
     * 处理过了计划装车最晚时间24小时的货源
     *
     * @param now 当前时间
     */
    private void processExpiredCargos(Date now) {
        // 查询所有状态为正常且未下架的货源
        List<DailyCargo> cargos = dailyCargoMapper.selectNormalCargos();
        if (cargos == null || cargos.isEmpty()) {
            log.info("没有需要处理的正常货源");
            return;
        }
        for (DailyCargo cargo : cargos) {
            try {
                // 获取装货日期
                Date loadingDate = cargo.getLoadingDate();
                if (loadingDate == null) {
                    continue;
                }
                // 获取最晚装货时间
                String loadingTimeEnd = cargo.getLoadingTimeEnd();
                if (StringUtils.isBlank(loadingTimeEnd)) {
                    loadingTimeEnd = "23:59";
                }
                // 计算装货截止时间
                Calendar calendar = getLoadingCalendar(loadingDate, loadingTimeEnd);
                // 加上24小时
                calendar.add(Calendar.HOUR_OF_DAY, 24);
                Date expiryTime = calendar.getTime();
                // 如果当前时间已经超过了装货截止时间+24小时，则自动下架
                if (now.after(expiryTime)) {
                    log.info("货源ID:{}已过装货截止时间24小时，自动下架", cargo.getId());
                    getSelf().updateCargoStatus(cargo.getId(), "1"); // 1-已下架
                }
            } catch (Exception e) {
                log.error("处理货源ID:{}自动下架异常", cargo.getId(), e);
            }
        }
    }

    /**
     * 处理需求车辆数量已满足的货源
     *
     * @param now 当前时间
     */
    private void processFulfilledCargos(Date now) {
        // 查询所有状态为正常且未下架的货源
        List<DailyCargo> cargos = dailyCargoMapper.selectNormalCargos();
        if (cargos == null || cargos.isEmpty()) {
            return;
        }
        for (DailyCargo cargo : cargos) {
            try {
                Date loadingDate = cargo.getLoadingDate();
                // 获取装货日期当天23:59的时间
                Calendar endCalendar = getEndCalendar(loadingDate);
                // 如果当前时间已经是23:59，则处理需求车辆数量已满足的货源
                if (now.getTime() >= endCalendar.getTimeInMillis() - 60000) { // 考虑1分钟的误差
                    // 获取需求车辆数量
                    Integer requiredVehicles = cargo.getVehicleCount();
                    // 查询该货源的承运司机数量
                    int driverCount = carrierDriverService.countDriversByCargoId(cargo.getId());
                    // 如果承运司机数量满足或超过需求车辆数量，则自动下架
                    if (driverCount >= requiredVehicles) {
                        log.info("货源ID:{}需求车辆数量已满足，自动下架", cargo.getId());
                        getSelf().updateCargoStatus(cargo.getId(), "1"); // 1-已下架
                    }
                }
            } catch (Exception e) {
                log.error("处理货源ID:{}自动下架异常", cargo.getId(), e);
            }
        }
    }

    /**
     * 更新货源的运力配置状态
     *
     * @param customerId 货源ID
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTransportCapacityStatus(Long customerId) {
        // 校验货源是否存在
        DailyCargo dailyCargo = dailyCargoMapper.selectDailyCargoById(customerId);
        if (dailyCargo == null) {
            log.error("更新运力配置状态失败，货源不存在，customerId: {}", customerId);
            return 0;
        }
        
        // 查询该货源的承运司机数量
        int driverCount = carrierDriverService.countDriversByCargoId(customerId);
        // 获取需要的车辆数量
        Integer vehicleCount = dailyCargo.getVehicleCount();
        
        // 根据承运司机数量与车辆数量的比较确定运力配置状态
        String transportCapacity;
        if (driverCount == 0) {
            // 无承运司机
            transportCapacity = "0";
        } else if (driverCount < vehicleCount) {
            // 部分匹配
            transportCapacity = "1";
        } else {
            // 匹配完成
            transportCapacity = "2";
        }
        
        // 如果状态没有变化，不需要更新
        if (transportCapacity.equals(dailyCargo.getTransportCapacity())) {
            log.info("货源ID:{}的运力配置状态未变化，保持为:{}", customerId, transportCapacity);
            return 1;
        }
        
        // 更新货源的运力配置状态
        dailyCargo.setUpdateBy(SecurityUtils.getUsername());
        dailyCargo.setUpdateTime(DateUtils.getNowDate());
        dailyCargo.setTransportCapacity(transportCapacity);
        int result = dailyCargoMapper.updateDailyCargo(dailyCargo);
        
        log.info("货源ID:{}的运力配置状态已更新为:{}", customerId, transportCapacity);
        return result;
    }
    
    /**
     * 手动上传承运信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int uploadCarrierDriver(CarrierDriver carrierDriver) {
        // 校验货源是否存在
        Long customerId = carrierDriver.getCargoId();
        if (customerId == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货源ID不能为空");
        }
        DailyCargo dailyCargo = dailyCargoMapper.selectDailyCargoById(customerId);
        if (dailyCargo == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "货源不存在");
        }
        
        // 检查货源状态是否为上架中
        if (!"0".equals(dailyCargo.getStatus())) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "只有上架中的货源才能上传承运信息");
        }
        
        // 设置调度任务ID
        Long taskId = dailyCargo.getTaskId();
        if (taskId != null) {
            carrierDriver.setTaskId(taskId);
        } else {
            // 如果任务ID为空，设置为0，表示手动添加的承运信息
            carrierDriver.setTaskId(0L);
            log.info("货源{}没有关联的调度任务，设置任务ID为0", customerId);
        }
        
        // 设置默认承运状态为待接单
        if (StringUtils.isBlank(carrierDriver.getCarrierStatus())) {
            carrierDriver.setCarrierStatus("0");
        }
        
        // 设置接单时间
        if (carrierDriver.getAcceptTime() == null) {
            carrierDriver.setAcceptTime(DateUtils.getNowDate());
        }
        
        log.info("开始添加承运司机信息：{}", carrierDriver);
        // 调用承运司机服务添加承运信息
        int result = carrierDriverService.addCarrierDriver(carrierDriver);

        // 更新运力配置状态
        if (result > 0) {
            // 调用更新运力配置状态方法
            int i = updateTransportCapacityStatus(customerId);
            log.info("uploadCarrierDriver,updateTransportCapacityStatus,i->{}",i);
        }
        Long taskId1 = carrierDriver.getTaskId();
        if (taskId1 == 0){
            //手动上传需要同步结果到司机数据中
            try {
                // 获取司机基本信息
                String driverName = carrierDriver.getDriverName();
                String idCard = carrierDriver.getIdCard();
                String phoneNumber = carrierDriver.getPhoneNumber();

                // 获取货源信息，用于提取货物类型和运输路线
                String cargoName = dailyCargo.getCargoName();
                String vehicleType = dailyCargo.getVehicleType();

                // 构建运输路线字符串
                String routeName = "";
                if (dailyCargo.getLoadingProvinceName() != null && dailyCargo.getUnloadingProvinceName() != null) {
                    routeName = dailyCargo.getLoadingProvinceName() + dailyCargo.getLoadingCityName() + " - " +
                               dailyCargo.getUnloadingProvinceName() + dailyCargo.getUnloadingCityName();
                }
                // 查询司机画像服务
                if (schedulerDriverProfileService != null) {
                    // 创建查询条件
                    SchedulerDriverProfileReq query = new SchedulerDriverProfileReq();
                    query.setIdentityCard(idCard);
                    List<SchedulerDriverProfile> existingProfiles = schedulerDriverProfileService.selectSchedulerDriverProfileList(query);

                    SchedulerDriverProfile driverProfile;
                    boolean isNewProfile = false;

                    if (existingProfiles == null || existingProfiles.isEmpty()) {
                        // 如果不存在，创建新的司机画像
                        driverProfile = new SchedulerDriverProfile();
                        driverProfile.setDriverId(String.valueOf(TextUtil.getTimeSequenceID(5))); // 生成唯一ID
                        driverProfile.setDriverName(driverName);
                        driverProfile.setIdentityCard(idCard);
                        driverProfile.setSchedulerTelephone(phoneNumber);
                        driverProfile.setOperation(20); // 9表示手动上传
                        driverProfile.setOperationDesc("手动上传承运司机");
                        driverProfile.setServiceCount(1L); // 首次服务
                        driverProfile.setState(0); // 正常状态
                        driverProfile.setCooperativeStatus(1); // 合作状态
                        driverProfile.setBlacklistStatus(0); // 非黑名单
                        driverProfile.setCreateBy(SecurityUtils.getUsername());
                        driverProfile.setCreateTime(DateUtils.getNowDate());
                        isNewProfile = true;
                    } else {
                        // 已存在，更新现有司机画像
                        driverProfile = existingProfiles.get(0);
                        // 更新服务次数
                        if (driverProfile.getServiceCount() == null) {
                            driverProfile.setServiceCount(1L);
                        } else {
                            driverProfile.setServiceCount(driverProfile.getServiceCount() + 1);
                        }
                        driverProfile.setUpdateBy(SecurityUtils.getUsername());
                        driverProfile.setUpdateTime(DateUtils.getNowDate());
                        // 如果司机处于黑名单状态，则不更新
                        if (driverProfile.getBlacklistStatus() != null && driverProfile.getBlacklistStatus() == 1) {
                            log.warn("司机{}在黑名单中，跳过更新司机画像", driverName);
                            return result;
                        }
                    }
                    // 更新常运货物信息 (schedulerGoods字段)
                    if (StringUtils.isNotBlank(cargoName)) {
                        try {
                            updateJsonArrayField(driverProfile, cargoName, "schedulerGoods");
                        } catch (Exception e) {
                            log.error("更新常运货物信息失败", e);
                        }
                    }

                    // 更新车辆结构信息 (schedulerVehicleStructure字段)
                    if (StringUtils.isNotBlank(vehicleType)) {
                        try {
                            updateJsonArrayField(driverProfile, vehicleType, "schedulerVehicleStructure");
                        } catch (Exception e) {
                            log.error("更新车辆结构信息失败", e);
                        }
                    }

                    // 更新车辆长度信息 (schedulerVehicleLength字段)
                    if (dailyCargo.getVehicleLength() != null) {
                        try {
                            updateJsonArrayField(driverProfile, dailyCargo.getVehicleLength().toString(), "schedulerVehicleLength");
                        } catch (Exception e) {
                            log.error("更新车辆长度信息失败", e);
                        }
                    }

                    // 更新常跑线路信息
                    if (StringUtils.isNotBlank(routeName)) {
                        try {
                            updateJsonArrayField(driverProfile, routeName, "schedulerHaulway");
                        } catch (Exception e) {
                            log.error("更新常跑线路信息失败", e);
                        }
                    }
                    // 更新最近服务时间
                    driverProfile.setLastServiceTime(DateUtils.getNowDate());
                    // 保存司机画像
                    try {
                        if (isNewProfile) {
                            schedulerDriverProfileService.insertSchedulerDriverProfile(driverProfile);
                            log.info("成功创建司机画像，司机：{}", driverName);
                        } else {
                            schedulerDriverProfileService.updateSchedulerDriverProfile(driverProfile);
                            log.info("成功更新司机画像，司机：{}", driverName);
                        }
                    } catch (Exception e) {
                        log.error("保存司机画像失败", e);
                    }
                } else {
                    log.warn("无法获取司机画像服务，跳过同步司机数据");
                }
            } catch (Exception e) {
                log.error("同步司机数据到司机画像表失败", e);
            }
        }
        return result;
    }

    /**
     * 取消承运司机合作
     *
     * @param id 承运司机ID
     * @param handleType 解决循环调用
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelCarrierDriver(Long id,Integer handleType) {
        // 校验承运司机是否存在
        CarrierDriver carrierDriver = carrierDriverMapper.selectCarrierDriverById(id);
        if (carrierDriver == null) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "承运司机不存在");
        }

        // 检查承运状态，只有待接单(0)和待装车(1)状态才能取消合作
        String carrierStatus = carrierDriver.getCarrierStatus();
        if (!"0".equals(carrierStatus) && !"1".equals(carrierStatus)) {
            throw exception(ErrorCodeConstants.BASE_SERVER_ERROR, "只有待接单和待装车状态下才能取消合作");
        }

        // 更新状态为已删除
        carrierDriver.setStatus("1"); // 1-已删除
        carrierDriver.setUpdateBy(SecurityUtils.getUsername());
        carrierDriver.setUpdateTime(DateUtils.getNowDate());
        carrierDriver.setRemark(StringUtils.isBlank(carrierDriver.getRemark()) ?
                "已取消合作" : carrierDriver.getRemark() + "；已取消合作");

        // 更新承运司机信息
        int result = carrierDriverMapper.updateCarrierDriver(carrierDriver);

        // 如果更新成功，同时更新货源的运力配置状态
        if (result > 0) {
            Long customerId = carrierDriver.getCargoId();
            if (customerId != null) {
                int i = updateTransportCapacityStatus(customerId);
                log.info("cancelCarrierDriver,updateTransportCapacityStatus,i->{}", i);
            }
        }
        Long taskDriverMatchId = carrierDriver.getTaskDriverMatchId();
        if (taskDriverMatchId != null && handleType == 1){
            Boolean isSuccess = taskDriverMatchService.confirmOrCancelDeal(taskDriverMatchId, 2, 2);
            log.info("cancelCarrierDriver,confirmOrCancelDeal,isSuccess:{}",isSuccess);
        }
        return result;
    }
    
    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private DailyCargoServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    /**
     * 更新司机画像中的JSON数组字段
     *
     * @param driverProfile 司机画像对象
     * @param value 要添加的值
     * @param fieldName 字段名称
     */
    private void updateJsonArrayField(SchedulerDriverProfile driverProfile, String value, String fieldName) {
        try {
            // 使用反射获取字段值
            String getterMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            String setterMethodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            
            Method getterMethod = driverProfile.getClass().getMethod(getterMethodName);
            Method setterMethod = driverProfile.getClass().getMethod(setterMethodName, String.class);
            
            String jsonField = (String) getterMethod.invoke(driverProfile);
            
            // 处理JSON数组
            JSONArray jsonArray;
            if (StringUtils.isBlank(jsonField)) {
                jsonArray = new JSONArray();
                jsonArray.add(value);
            } else {
                jsonArray = JSONArray.parseArray(jsonField);
                // 使用Set进行去重
                java.util.Set<String> valueSet = new java.util.HashSet<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    valueSet.add(jsonArray.getString(i));
                }
                if (!valueSet.contains(value)) {
                    jsonArray.add(value);
                }
            }
            
            // 设置更新后的值
            setterMethod.invoke(driverProfile, jsonArray.toJSONString());
        } catch (Exception e) {
            log.error("更新" + fieldName + "失败", e);
        }
    }

    @Override
    public List<ChinaProvincesCitiesAreas> listChinaProvincesCitiesAreas(ChinaProvincesCitiesAreas chinaProvincesCitiesAreas) {
        try {
            // 构建请求URL
            String url = areasListApiUrl;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<MultiValueMap<String, String>> requestEntity = getMultiValueMapHttpEntity(chinaProvincesCitiesAreas, headers);
            ResponseEntity<AjaxResult> responseEntity = restTemplate.postForEntity(url, requestEntity, AjaxResult.class);
            AjaxResult ajaxResult = responseEntity.getBody();
            if (ajaxResult != null && ajaxResult.isSuccess()) {
                // 将返回的数据转换为List<ChinaProvincesCitiesAreas>
                Object data = ajaxResult.get("data");
                if (data != null) {
                    String jsonString = JSON.toJSONString(data);
                    return JSON.parseArray(jsonString, ChinaProvincesCitiesAreas.class);
                }
            }
            log.error("查询平台行政区划列表失败，返回结果：{}", ajaxResult);
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("查询平台行政区划列表异常", e);
            return new ArrayList<>();
        }
    }


    private static HttpEntity<MultiValueMap<String, String>> getMultiValueMapHttpEntity(ChinaProvincesCitiesAreas chinaProvincesCitiesAreas, HttpHeaders headers) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        // 将对象属性转换为表单参数
        if (chinaProvincesCitiesAreas.getId() != null) {
            map.add("id", chinaProvincesCitiesAreas.getId().toString());
        }
        if (chinaProvincesCitiesAreas.getAreaCode() != null) {
            map.add("areaCode", chinaProvincesCitiesAreas.getAreaCode());
        }
        if (chinaProvincesCitiesAreas.getAreaName() != null) {
            map.add("areaName", chinaProvincesCitiesAreas.getAreaName());
        }
        if (chinaProvincesCitiesAreas.getShortName() != null) {
            map.add("shortName", chinaProvincesCitiesAreas.getShortName());
        }
        if (chinaProvincesCitiesAreas.getPCode() != null) {
            map.add("pCode", chinaProvincesCitiesAreas.getPCode());
        }
        if (chinaProvincesCitiesAreas.getAreaType() != null) {
            map.add("areaType", chinaProvincesCitiesAreas.getAreaType().toString());
        }
        if (chinaProvincesCitiesAreas.getState() != null) {
            map.add("state", chinaProvincesCitiesAreas.getState().toString());
        }
        return new HttpEntity<>(map, headers);
    }

    @Override
    public AjaxResult getAddressInfo(AddressInfo addressInfo) {
        // 构建请求URL - 使用实际的远程服务地址
        String url = resolveAddressIpLimitUrl;
        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 构建请求体
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("addressDetail", addressInfo.getAddressDetail());
        // 创建请求实体
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestMap, headers);
        // 发送请求并获取响应
        ResponseEntity<AjaxResult> responseEntity = restTemplate.postForEntity(url, requestEntity, AjaxResult.class);
        AjaxResult result = responseEntity.getBody();
        // 处理响应
        if (result != null && StringUtils.equals(String.valueOf(result.get(CODE_TAG)),"200")) {
            // 获取返回的数据
            Object data = result.get("data");
            if (data != null) {
                // 将返回的数据转换为AddressInfo对象
                String jsonString = JSON.toJSONString(data);
                AddressInfo processedAddress = JSON.parseObject(jsonString, AddressInfo.class);
                return AjaxResult.success(processedAddress);
            }
        }
        return AjaxResult.error("地址解析失败");
    }

    /**
     * 处理地址经纬度信息
     * 
     * @param dailyCargo 每日货源对象
     */
    private void processAddressCoordinates(DailyCargo dailyCargo) {
        try {
            // 处理装货地址经纬度
            String loadingFullAddress = getSelf().assembleFullAddress(
                    dailyCargo.getLoadingProvinceName(),
                    dailyCargo.getLoadingCityName(),
                    dailyCargo.getLoadingDistrictName(),
                    dailyCargo.getLoadingAddress()
            );
            if (StringUtils.isNotBlank(loadingFullAddress)) {
                AddressInfo loadingAddressInfo = new AddressInfo();
                loadingAddressInfo.setAddressDetail(loadingFullAddress);
                AjaxResult loadingResult = getAddressInfo(loadingAddressInfo);

                if (loadingResult.isSuccess()) {
                    AddressInfo processedLoadingAddress = (AddressInfo) loadingResult.get("data");
                    if (processedLoadingAddress != null) {
                        // 设置装货地址经纬度
                        if (processedLoadingAddress.getLongitude() != null) {
                            dailyCargo.setLoadingLongitude(new java.math.BigDecimal(processedLoadingAddress.getLongitude()));
                        }
                        if (processedLoadingAddress.getLatitude() != null) {
                            dailyCargo.setLoadingLatitude(new java.math.BigDecimal(processedLoadingAddress.getLatitude()));
                        }
                        log.info("成功获取装货地址经纬度 - 地址: {}, 经度: {}, 纬度: {}", 
                                loadingFullAddress, 
                                processedLoadingAddress.getLongitude(), 
                                processedLoadingAddress.getLatitude());
                    }
                } else {
                    log.warn("获取装货地址经纬度失败 - 地址: {}, 错误: {}", loadingFullAddress, loadingResult.get("msg"));
                }
            }
            
            // 处理卸货地址经纬度
            String unloadingFullAddress = getSelf().assembleFullAddress(
                    dailyCargo.getUnloadingProvinceName(),
                    dailyCargo.getUnloadingCityName(),
                    dailyCargo.getUnloadingDistrictName(),
                    dailyCargo.getUnloadingAddress()
            );

            if (StringUtils.isNotBlank(unloadingFullAddress)) {
                AddressInfo unloadingAddressInfo = new AddressInfo();
                unloadingAddressInfo.setAddressDetail(unloadingFullAddress);
                AjaxResult unloadingResult = getAddressInfo(unloadingAddressInfo);

                if (unloadingResult.isSuccess()) {
                    AddressInfo processedUnloadingAddress = (AddressInfo) unloadingResult.get("data");
                    if (processedUnloadingAddress != null) {
                        // 设置卸货地址经纬度
                        if (processedUnloadingAddress.getLongitude() != null) {
                            dailyCargo.setUnloadingLongitude(new java.math.BigDecimal(processedUnloadingAddress.getLongitude()));
                        }
                        if (processedUnloadingAddress.getLatitude() != null) {
                            dailyCargo.setUnloadingLatitude(new java.math.BigDecimal(processedUnloadingAddress.getLatitude()));
                        }
                        log.info("成功获取卸货地址经纬度 - 地址: {}, 经度: {}, 纬度: {}",
                                unloadingFullAddress,
                                processedUnloadingAddress.getLongitude(),
                                processedUnloadingAddress.getLatitude());
                    }
                } else {
                    log.warn("获取卸货地址经纬度失败 - 地址: {}, 错误: {}", unloadingFullAddress, unloadingResult.get("msg"));
                }
            }
        } catch (Exception e) {
            // 捕获异常但不影响主流程
            log.error("处理地址经纬度信息异常", e);
        }
    }

    @Override
    public TransportRouteRespVo getTransportRoute(TransportRouteReqVo reqVo) {
        TransportRouteRespVo respVo = new TransportRouteRespVo();
        
        // 组装起始地点
        String startPoint = concatPcdTwo(
                reqVo.getLoadingProvinceName(),
                reqVo.getLoadingCityName(),
                reqVo.getLoadingDistrictName()
        );
        respVo.setStartPoint(startPoint);
        
        // 组装目的地点
        String endPoint = concatPcdTwo(
                reqVo.getUnloadingProvinceName(),
                reqVo.getUnloadingCityName(),
                reqVo.getUnloadingDistrictName()
        );
        respVo.setEndPoint(endPoint);
        
        return respVo;
    }
}