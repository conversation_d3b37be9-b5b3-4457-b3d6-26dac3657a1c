package com.zly.project.cargo.service;

import com.zly.project.cargo.controller.vo.DailyCargoReqVo;
import com.zly.project.cargo.controller.vo.FrequentCargoQueryVo;
import com.zly.project.cargo.controller.vo.FrequentCargoReqVo;
import com.zly.project.cargo.controller.vo.FrequentCargoRespVo;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;

import java.util.List;

/**
 * 常发货源Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
public interface IFrequentCargoService {
    
    /**
     * 新增常发货源
     * 
     * @param reqVo 常发货源请求信息
     * @return 结果
     */
    int addFrequentCargo(FrequentCargoReqVo reqVo);
    
    /**
     * 编辑常发货源
     *
     * @param reqVo 常发货源请求信息
     * @param id 常发货源ID
     * @return 结果
     */
    int updateFrequentCargo(FrequentCargoReqVo reqVo, Long id);
    
    /**
     * 根据ID查询常发货源详情
     *
     * @param id 常发货源ID
     * @return 常发货源信息
     */
    FrequentCargoReqVo getFrequentCargoById(Long id);
    
    /**
     * 更新常发货源状态
     *
     * @param id 常发货源ID
     * @param status 状态(0-正常,1-已删除)
     * @return 结果
     */
    int updateCargoStatus(Long id, String status);
    
    /**
     * 分页查询常发货源列表
     *
     * @param queryVo 查询参数
     * @return 常发货源列表
     */
    List<FrequentCargoRespVo> selectFrequentCargoPageList(FrequentCargoQueryVo queryVo);

    /**
     * 拷贝常发货源信息
     * @param frequentId 常发货源ID
     * @return DailyCargoReqVo
     */
    DailyCargoReqVo copyFrequentCargo(Long frequentId);
    
    /**
     * 删除常发货源
     *
     * @param id 常发货源ID
     * @return 结果
     */
    int deleteFrequentCargo(Long id);
}