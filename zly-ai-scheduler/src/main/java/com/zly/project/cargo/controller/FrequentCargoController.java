package com.zly.project.cargo.controller;

import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.cargo.controller.vo.DailyCargoReqVo;
import com.zly.project.cargo.controller.vo.FrequentCargoQueryVo;
import com.zly.project.cargo.controller.vo.FrequentCargoReqVo;
import com.zly.project.cargo.controller.vo.FrequentCargoRespVo;
import com.zly.project.cargo.service.IFrequentCargoService;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 常发货源Controller
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Api(tags = "管理后台 - 常发货源管理")
@RestController
@RequestMapping("/cargo/frequent")
public class FrequentCargoController extends BaseController {
    
    @Resource
    private IFrequentCargoService frequentCargoService;
    
    /**
     * 新增常发货源
     */
    @ApiOperation("新增常发货源")
    @PreAuthorize("@ss.hasPermi('cargo:frequent:add')")
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody FrequentCargoReqVo reqVo) {
        return toAjax(frequentCargoService.addFrequentCargo(reqVo));
    }
    
    /**
     * 修改常发货源
     */
    @ApiOperation("修改常发货源")
    @PreAuthorize("@ss.hasPermi('cargo:frequent:edit')")
    @PutMapping("/edit/{id}")
    public AjaxResult edit(@PathVariable("id") Long id, @Validated @RequestBody FrequentCargoReqVo reqVo) {
        return toAjax(frequentCargoService.updateFrequentCargo(reqVo, id));
    }
    
    /**
     * 获取常发货源详情
     */
    @ApiOperation("获取常发货源详情")
    @PreAuthorize("@ss.hasPermi('cargo:frequent:query')")
    @GetMapping("/detail/{id}")
    public AjaxResult getDetail(@PathVariable("id") Long id) {
        return AjaxResult.success(frequentCargoService.getFrequentCargoById(id));
    }
    
    /**
     * 更新常发货源状态
     */
    @ApiOperation("更新常发货源状态")
    @PreAuthorize("@ss.hasPermi('cargo:frequent:edit')")
    @PutMapping("/status/{id}")
    public AjaxResult updateStatus(@PathVariable("id") Long id, @ApiParam("状态(0-正常,1-已删除)") @RequestParam String status) {
        return toAjax(frequentCargoService.updateCargoStatus(id, status));
    }
    
    /**
     * 查询常发货源列表
     */
    @ApiOperation("查询常发货源列表")
    @PreAuthorize("@ss.hasPermi('cargo:frequent:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody FrequentCargoQueryVo queryVo) {
        startPageByPageDomain(queryVo);
        List<FrequentCargoRespVo> list = frequentCargoService.selectFrequentCargoPageList(queryVo);
        return getDataTable(list);
    }

    /**
     * 从常发货源创建每日货源
     */
    @ApiOperation("从常发货源创建每日货源")
    @PreAuthorize("@ss.hasPermi('cargo:daily:add')")
    @PostMapping("/createFromFrequent/{frequentId}")
    public AjaxResult createFromFrequent(@PathVariable("frequentId") Long frequentId) {
        DailyCargoReqVo reqVo = frequentCargoService.copyFrequentCargo(frequentId);
        // 创建每日货源
        return AjaxResult.success(reqVo);
    }
    
    /**
     * 删除常发货源
     */
    @ApiOperation("删除常发货源")
    @PreAuthorize("@ss.hasPermi('cargo:frequent:delete')")
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {
        return toAjax(frequentCargoService.deleteFrequentCargo(id));
    }
} 