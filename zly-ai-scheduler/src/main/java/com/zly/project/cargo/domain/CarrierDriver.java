package com.zly.project.cargo.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 承运司机对象 carrier_driver
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CarrierDriver extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 货源ID */
    @Excel(name = "货源ID")
    private Long cargoId;

    /** 调度任务ID */
    @Excel(name = "调度任务ID")
    private Long taskId;

    @Excel(name = "调度匹配司机ID")
    private Long taskDriverMatchId;

    /** 司机姓名 */
    @Excel(name = "司机姓名")
    private String driverName;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phoneNumber;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNumber;

    /** 车牌颜色(1-蓝色,2-黄色,3-黑色,4-白色,5-绿色,91-其他,92-农绿色,93-黄绿色,94-渐变绿) */
    @Excel(name = "车牌颜色", readConverterExp = "1=蓝色,2=黄色,3=黑色,4=白色,5=绿色,91=其他,92=农绿色,93=黄绿色,94=渐变绿")
    private String plateColor;

    /** 接单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTime;

    /** 承运状态(0-待接单,1-待装车,2-运输中,3-已送达) */
    @Excel(name = "承运状态", readConverterExp = "0=待接单,1=待装车,2=运输中,3=已送达")
    private String carrierStatus;

    /** 状态(0-正常,1-已删除) */
    @Excel(name = "状态", readConverterExp = "0=正常,1=已删除")
    private String status;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;
} 