package com.zly.project.cargo.controller;

import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.cargo.controller.vo.ChinaProvincesCitiesAreas;
import com.zly.project.cargo.service.IDailyCargoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 平台行政区划Controller
 */
@Api(tags = "平台行政区划")
@RestController
@RequestMapping("/common/areas")
public class ChinaProvincesCitiesAreasController extends BaseController {

	@Resource
	private IDailyCargoService dailyCargoService;


	@ApiOperation("查询平台行政区划列表调4.0")
	@PostMapping("/list")
	public AjaxResult list(ChinaProvincesCitiesAreas chinaProvincesCitiesAreas) {
		List<ChinaProvincesCitiesAreas> list = dailyCargoService.listChinaProvincesCitiesAreas(chinaProvincesCitiesAreas);
		return AjaxResult.success(list);
	}

}