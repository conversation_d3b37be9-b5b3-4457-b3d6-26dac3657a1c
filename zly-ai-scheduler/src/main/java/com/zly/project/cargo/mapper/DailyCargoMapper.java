package com.zly.project.cargo.mapper;

import com.zly.project.cargo.controller.vo.DailyCargoQueryVo;
import com.zly.project.cargo.controller.vo.DailyCargoRespVo;
import com.zly.project.cargo.domain.DailyCargo;
import org.apache.ibatis.annotations.MapKey;

import java.util.List;
import java.util.Map;

/**
 * 每日货源Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public interface DailyCargoMapper {
    
    /**
     * 新增每日货源
     * 
     * @param dailyCargo 每日货源
     * @return 结果
     */
    public int insertDailyCargo(DailyCargo dailyCargo);
    
    /**
     * 通过ID查询每日货源信息
     *
     * @param id 每日货源ID
     * @return 每日货源信息
     */
    public DailyCargo selectDailyCargoById(Long id);
    
    /**
     * 修改每日货源
     *
     * @param dailyCargo 每日货源信息
     * @return 结果
     */
    public int updateDailyCargo(DailyCargo dailyCargo);
    
    /**
     * 查询每日货源列表
     *
     * @param dailyCargo 每日货源信息
     * @return 每日货源集合
     */
    public List<DailyCargo> selectDailyCargoList(DailyCargo dailyCargo);
    
    /**
     * 关联查询每日货源列表（包含货主信息）
     *
     * @param queryVo 查询参数
     * @return 每日货源集合
     */
    public List<DailyCargoRespVo> selectDailyCargoListWithCarrier(DailyCargoQueryVo queryVo);
    
    /**
     * 查询所有状态为正常且未下架的货源
     *
     * @return 正常货源集合
     */
    public List<DailyCargo> selectNormalCargos();
    
    /**
     * 查询每日货源的意向司机数量
     * 
     * @param customerIds 货源ID列表
     * @return 货源ID与意向司机数量的映射
     */
    @MapKey("customerId")
    List<Map<String, Object>> selectInterestedDriverCountByCargos(List<Long> customerIds);
}