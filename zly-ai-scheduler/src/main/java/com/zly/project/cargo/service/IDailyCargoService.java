package com.zly.project.cargo.service;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.cargo.controller.vo.DailyCargoQueryVo;
import com.zly.project.cargo.controller.vo.DailyCargoReqVo;
import com.zly.project.cargo.controller.vo.DailyCargoRespVo;
import com.zly.project.cargo.domain.CarrierDriver;
import com.zly.project.common.vo.AddressInfo;
import com.zly.project.scheduler.controller.vo.CustomerInfoRespVo;
import com.zly.project.scheduler.controller.vo.ScheduleTaskVo;
import com.zly.project.cargo.controller.vo.ChinaProvincesCitiesAreas;
import com.zly.project.cargo.controller.vo.TransportRouteReqVo;
import com.zly.project.cargo.controller.vo.TransportRouteRespVo;

import java.util.List;

/**
 * 每日货源Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
public interface IDailyCargoService {
    
    /**
     * 新增每日货源
     * 
     * @param reqVo 每日货源请求信息
     * @return 结果
     */
    Long addDailyCargo(DailyCargoReqVo reqVo);

    /**
     * 查询货主是合作状态的数据集合
     * @return List<CarrierInfoRespVo>
     */
    List<CustomerInfoRespVo> getActiveCustomerList(Integer type);
    
    /**
     * 编辑每日货源
     *
     * @param reqVo 每日货源请求信息
     * @param id 每日货源ID
     * @return 结果
     */
    int updateDailyCargo(DailyCargoReqVo reqVo, Long id);
    
    /**
     * 根据ID查询每日货源详情
     *
     * @param id 每日货源ID
     * @return 每日货源信息
     */
    DailyCargoReqVo getDailyCargoById(Long id);
    
    /**
     * 更新每日货源状态
     *
     * @param id 每日货源ID
     * @param status 状态(0-正常上架,1-已下架)
     * @return 结果
     */
    int updateCargoStatus(Long id, String status);
    
    /**
     * 分页查询每日货源列表
     *
     * @param queryVo 查询参数
     * @return 每日货源列表
     */
    List<DailyCargoRespVo> selectDailyCargoPageList(DailyCargoQueryVo queryVo);
    
    /**
     * 组装调度任务参数
     *
     * @param customerId 货源ID
     * @return 调度任务VO对象
     */
    ScheduleTaskVo assembleScheduleTask(Long customerId);
    
    /**
     * 更新AI调度状态
     *
     * @param customerId 货源ID
     * @param aiScheduleStatus AI调度状态(0-未开始,1-调度中,2-调度结束)
     * @param taskId 任务ID
     * @return 结果
     */
    int updateAiScheduleStatus(Long customerId, String aiScheduleStatus, Long taskId);
    
    /**
     * 自动下架处理
     * 1. 过了计划装车最晚时间的24小时，自动下架
     * 2. 需求车辆数量满足后，当天23:59自动下架
     */
    void autoOfflineProcess();
    
    /**
     * 手动上传承运信息
     * 
     * @param carrierDriver 承运司机信息
     * @return 结果
     */
    int uploadCarrierDriver(CarrierDriver carrierDriver);

    /**
     * 更新货源的运力配置状态
     *
     * @param customerId 货源ID
     * @return 更新结果
     */
    int updateTransportCapacityStatus(Long customerId);

    /**
     * 取消承运司机合作
     * 
     * @param id 承运司机ID
     * @param handleType 处理类型(1:同步取消调度匹配司机，0:不同步)
     * @return 结果
     */
    int cancelCarrierDriver(Long id, Integer handleType);

    /**
     * 查询平台行政区划列表
     *
     * @param chinaProvincesCitiesAreas 行政区划查询条件
     * @return 行政区划列表
     */
    List<ChinaProvincesCitiesAreas> listChinaProvincesCitiesAreas(ChinaProvincesCitiesAreas chinaProvincesCitiesAreas);

    /**
     * 查询地址信息
     * @param addressInfo 地址信息
     * @return AjaxResult
     */
    AjaxResult getAddressInfo(AddressInfo addressInfo);

    /**
     * 组装完整地址
     *
     * @param province 省名称
     * @param city 市名称
     * @param district 区名称
     * @param address 详细地址
     * @return 完整地址
     */
    String assembleFullAddress(String province, String city, String district, String address);
    
    /**
     * 获取运输路线简称
     *
     * @param reqVo 运输路线请求VO
     * @return 运输路线响应VO
     */
    TransportRouteRespVo getTransportRoute(TransportRouteReqVo reqVo);
}