package com.zly.project.cargo.service;

import com.zly.project.cargo.controller.vo.CarrierDriverRespVo;
import com.zly.project.cargo.domain.CarrierDriver;

import java.util.List;

/**
 * 承运司机Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface ICarrierDriverService {
    
    /**
     * 新增承运司机
     * 
     * @param carrierDriver 承运司机信息
     * @return 结果
     */
    int addCarrierDriver(CarrierDriver carrierDriver);
    
    /**
     * 根据ID查询承运司机详情
     *
     * @param id 承运司机ID
     * @return 承运司机信息
     */
    CarrierDriverRespVo getCarrierDriverById(Long id);
    
    /**
     * 编辑承运司机
     *
     * @param carrierDriver 承运司机信息
     * @return 结果
     */
    int updateCarrierDriver(CarrierDriver carrierDriver);
    
    /**
     * 查询承运司机列表
     *
     * @param carrierDriver 查询条件
     * @return 承运司机列表
     */
    List<CarrierDriver> selectCarrierDriverList(CarrierDriver carrierDriver);
    
    /**
     * 查询承运司机列表并转换为响应VO
     *
     * @param carrierDriver 查询条件
     * @return 承运司机响应VO列表
     */
    List<CarrierDriverRespVo> selectCarrierDriverRespList(CarrierDriver carrierDriver);
    
    /**
     * 查询货源的承运司机数量
     *
     * @param cargoId 货源ID
     * @return 承运司机数量
     */
    int countDriversByCargoId(Long cargoId);
    
    /**
     * 根据货源ID查询承运司机列表
     *
     * @param customerId 货源ID
     * @return 承运司机列表
     */
    List<CarrierDriver> selectDriversByCargoId(Long customerId);
    
    /**
     * 根据货源ID查询承运司机列表并转换为响应VO
     *
     * @param cargoId 货源ID
     * @return 承运司机响应VO列表
     */
    List<CarrierDriverRespVo> selectDriverRespVosByCargoId(Long cargoId);
    
    /**
     * 根据调度匹配司机ID查询承运司机列表
     *
     * @param taskDriverMatchId 调度匹配司机ID
     * @return 承运司机列表
     */
    List<CarrierDriver> selectDriversByTaskDriverMatchId(Long taskDriverMatchId);
    
    /**
     * 根据调度匹配司机ID查询承运司机列表并转换为响应VO
     *
     * @param taskDriverMatchId 调度匹配司机ID
     * @return 承运司机响应VO列表
     */
    List<CarrierDriverRespVo> selectDriverRespVosByTaskDriverMatchId(Long taskDriverMatchId);
    
    /**
     * 更新承运司机状态
     *
     * @param id 承运司机ID
     * @param carrierStatus 承运状态(0-待接单,1-待装车,2-运输中,3-已送达)
     * @return 结果
     */
    int updateCarrierStatus(Long id, String carrierStatus);
} 