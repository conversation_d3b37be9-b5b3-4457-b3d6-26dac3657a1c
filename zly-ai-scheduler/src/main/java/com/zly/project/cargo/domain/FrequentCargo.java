package com.zly.project.cargo.domain;

import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 常发货源对象 frequent_cargo
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FrequentCargo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "货源ID")
    private Long id;

    /** 货主关联ID */
    @ApiModelProperty(value = "货主关联ID")
    @Excel(name = "货主关联ID")
    private Long customerId;

    /** 货物名称 */
    @ApiModelProperty(value = "货物名称")
    @Excel(name = "货物名称")
    private String cargoName;

    /** 货物类型 */
    @ApiModelProperty(value = "货物类型")
    @Excel(name = "货物类型")
    private String cargoType;

    /** 包装方式 */
    @ApiModelProperty(value = "包装方式")
    @Excel(name = "包装方式")
    private String packagingMethod;

    /** 装货省编码 */
    @ApiModelProperty(value = "装货省编码")
    private String loadingProvinceCode;

    /** 装货省名称 */
    @ApiModelProperty(value = "装货省名称")
    @Excel(name = "装货省名称")
    private String loadingProvinceName;

    /** 装货市编码 */
    @ApiModelProperty(value = "装货市编码")
    private String loadingCityCode;

    /** 装货市名称 */
    @ApiModelProperty(value = "装货市名称")
    @Excel(name = "装货市名称")
    private String loadingCityName;

    /** 装货区编码 */
    @ApiModelProperty(value = "装货区编码")
    private String loadingDistrictCode;

    /** 装货区名称 */
    @ApiModelProperty(value = "装货区名称")
    @Excel(name = "装货区名称")
    private String loadingDistrictName;

    /** 装货详细地址 */
    @ApiModelProperty(value = "装货详细地址")
    @Excel(name = "装货详细地址")
    private String loadingAddress;
    
    /** 装货地址经度 */
    @ApiModelProperty(value = "装货地址经度")
    private BigDecimal loadingLongitude;
    
    /** 装货地址纬度 */
    @ApiModelProperty(value = "装货地址纬度")
    private BigDecimal loadingLatitude;

    /** 卸货省编码 */
    @ApiModelProperty(value = "卸货省编码")
    private String unloadingProvinceCode;

    /** 卸货省名称 */
    @ApiModelProperty(value = "卸货省名称")
    @Excel(name = "卸货省名称")
    private String unloadingProvinceName;

    /** 卸货市编码 */
    @ApiModelProperty(value = "卸货市编码")
    private String unloadingCityCode;

    /** 卸货市名称 */
    @ApiModelProperty(value = "卸货市名称")
    @Excel(name = "卸货市名称")
    private String unloadingCityName;

    /** 卸货区编码 */
    @ApiModelProperty(value = "卸货区编码")
    private String unloadingDistrictCode;

    /** 卸货区名称 */
    @ApiModelProperty(value = "卸货区名称")
    @Excel(name = "卸货区名称")
    private String unloadingDistrictName;

    /** 卸货详细地址 */
    @ApiModelProperty(value = "卸货详细地址")
    @Excel(name = "卸货详细地址")
    private String unloadingAddress;
    
    /** 卸货地址经度 */
    @ApiModelProperty(value = "卸货地址经度")
    private BigDecimal unloadingLongitude;
    
    /** 卸货地址纬度 */
    @ApiModelProperty(value = "卸货地址纬度")
    private BigDecimal unloadingLatitude;

    /** 状态(0-正常,1-已删除) */
    @ApiModelProperty(value = "状态(0-正常,1-已删除)")
    @Excel(name = "状态", readConverterExp = "0=正常,1=已删除")
    private String status;
} 