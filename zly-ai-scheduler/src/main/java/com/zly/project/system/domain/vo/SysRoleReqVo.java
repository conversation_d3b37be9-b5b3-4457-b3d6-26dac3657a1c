package com.zly.project.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 角色请求实体
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("角色请求实体")
public class SysRoleReqVo
{
    /** 角色ID */
    @ApiModelProperty("角色ID")
    private Long roleId;

    /** 角色名称 */
    @ApiModelProperty(value = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    /** 角色权限 */
    @ApiModelProperty("角色权限字符串,新增系统自动创建")
    private String roleKey;

    /** 角色排序 */
    @ApiModelProperty(value = "显示顺序", required = true)
    @NotNull(message = "显示顺序不能为空")
    private Integer roleSort;

    /** 角色状态（0正常 1停用） */
    @ApiModelProperty(value = "角色状态", required = true)
    @NotBlank(message = "角色状态不能为空")
    private String status;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 权限集合 */
    @ApiModelProperty("权限集合")
    private String permissionsJson;


}
