package com.zly.project.system.service.impl;

import com.zly.common.utils.CommonUtil;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.common.utils.reflect.ReflectUtils;
import com.zly.project.cargo.controller.vo.DailyCargoRespVo;
import com.zly.project.cargo.domain.DailyCargo;
import com.zly.project.cargo.domain.FrequentCargo;
import com.zly.project.cargo.mapper.DailyCargoMapper;
import com.zly.project.cargo.mapper.FrequentCargoMapper;
import com.zly.project.driver.domain.SchedulerDriverEvaluateTag;
import com.zly.project.driver.domain.SchedulerDriverProfile;
import com.zly.project.driver.mapper.SchedulerDriverEvaluateTagMapper;
import com.zly.project.driver.mapper.SchedulerDriverProfileMapper;
import com.zly.project.system.domain.ModifyDataset;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: SysClientLogTransferService
 * @Description: 系统日志中转处理类
 * @Author: CYS
 * @Date: 2024/12/19 13:31
 **/
@Slf4j
@Service
public class SysClientLogTransferService {

    @Resource
    private PlatformTransactionManager transactionManager;
    
    @Resource
    private DailyCargoMapper dailyCargoMapper;
    
    @Resource
    private FrequentCargoMapper frequentCargoMapper;
    
    @Resource
    private SchedulerDriverProfileMapper schedulerDriverProfileMapper;
    
    @Resource
    private SchedulerDriverEvaluateTagMapper schedulerDriverEvaluateTagMapper;

    public ModifyDataset<T> dailyCargoLogTransferProcessing(Long id) {
        if (CommonUtil.isEmptyOrZero(id)) {
            return new ModifyDataset<T>();
        }

        //新老数据集 比对数据使用
        List<ModifyDataset<?>> list = new ArrayList<>();
        //获取调用方法事务 更新后数据获取
        DailyCargo afterDailyCargo = dailyCargoMapper.selectDailyCargoById(id);
        //未查询到数据直接返回
        if (null == afterDailyCargo) {
            return new ModifyDataset<T>();
        }
        DailyCargoRespVo afterDailyCargoRespVo = new DailyCargoRespVo();
        BeanUtils.copyProperties(afterDailyCargo,afterDailyCargoRespVo);
        // 开启新事务 更新前数据获取
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW));
        try {
            DailyCargo beforeDailyCargo = dailyCargoMapper.selectDailyCargoById(id);
            DailyCargoRespVo beforeDailyCargoRespVo = new DailyCargoRespVo();
            BeanUtils.copyProperties(beforeDailyCargo,beforeDailyCargoRespVo);
            //组装数据进行反射处理
            ModifyDataset<DailyCargoRespVo> dailyCargoModifyDataset = new ModifyDataset<>(beforeDailyCargoRespVo, afterDailyCargoRespVo);
            list.add(dailyCargoModifyDataset);

        } catch (Exception e) {
            return new ModifyDataset<T>();
        } finally {
            transactionManager.commit(status);
        }
        return this.reflectionProcessing(list);
    }

    public ModifyDataset<T> frequentCargoLogTransferProcessing(Long id) {
        if (CommonUtil.isEmptyOrZero(id)) {
            return new ModifyDataset<T>();
        }
        //新老数据集 比对数据使用
        List<ModifyDataset<?>> list = new ArrayList<>();
        //获取调用方法事务 更新后数据获取
        FrequentCargo afterFrequentCargo = frequentCargoMapper.selectFrequentCargoById(id);
        //未查询到数据直接返回
        if (null == afterFrequentCargo) {
            return new ModifyDataset<T>();
        }
        // 开启新事务 更新前数据获取
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW));
        try {
            FrequentCargo beforeFrequentCargo = frequentCargoMapper.selectFrequentCargoById(id);
            //组装数据进行反射处理
            ModifyDataset<FrequentCargo> frequentCargoModifyDataset = new ModifyDataset<>(beforeFrequentCargo, afterFrequentCargo);
            list.add(frequentCargoModifyDataset);

        } catch (Exception e) {
            log.error("frequentCargoLogTransferProcessing,e->{}",e.getMessage());
            return new ModifyDataset<T>();
        } finally {
            transactionManager.commit(status);
        }
        return this.reflectionProcessing(list);
    }


    public ModifyDataset<T> driverProfileTransferProcessing(Long id) {
        if (CommonUtil.isEmptyOrZero(id)) {
            return new ModifyDataset<T>();
        }
        //新老数据集 比对数据使用
        List<ModifyDataset<?>> list = new ArrayList<>();
        //获取调用方法事务 更新后数据获取
        SchedulerDriverProfile afterDriverProfile = schedulerDriverProfileMapper.selectSchedulerDriverProfileById(id);
        //未查询到数据直接返回
        if (null == afterDriverProfile) {
            return new ModifyDataset<T>();
        }
        // 开启新事务 更新前数据获取
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW));
        try {
            SchedulerDriverProfile beforeDriverProfile = schedulerDriverProfileMapper.selectSchedulerDriverProfileById(id);
            //组装数据进行反射处理
            ModifyDataset<SchedulerDriverProfile> driverProfileModifyDataset = new ModifyDataset<>(beforeDriverProfile, afterDriverProfile);
            list.add(driverProfileModifyDataset);

        } catch (Exception e) {
            log.error("driverProfileTransferProcessing,e->{}",e.getMessage());
            return new ModifyDataset<T>();
        } finally {
            transactionManager.commit(status);
        }
        return this.reflectionProcessing(list);
    }

    public ModifyDataset<T> driverProfileTagTransferProcessing(Long driverProfileId) {
        if (CommonUtil.isEmptyOrZero(driverProfileId)) {
            return new ModifyDataset<T>();
        }
        //新老数据集 比对数据使用
        List<ModifyDataset<?>> list = new ArrayList<>();
        
        //构造查询条件，获取司机标记信息（type=2表示标记）
        SchedulerDriverEvaluateTag queryParam = new SchedulerDriverEvaluateTag();
        queryParam.setDriverProfileId(driverProfileId);
        queryParam.setType(2); // 类型为标记
        //获取调用方法事务 更新后数据获取
        List<SchedulerDriverEvaluateTag> afterTags = schedulerDriverEvaluateTagMapper.selectSchedulerDriverEvaluateTagList(queryParam);
        
        //未查询到数据直接返回
        if (null == afterTags || afterTags.isEmpty()) {
            return new ModifyDataset<T>();
        }
        // 开启新事务 更新前数据获取
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_REQUIRES_NEW));
        try {
            List<SchedulerDriverEvaluateTag> beforeTags = schedulerDriverEvaluateTagMapper.selectSchedulerDriverEvaluateTagList(queryParam);
            //组装数据进行反射处理
            ModifyDataset<List<SchedulerDriverEvaluateTag>> tagModifyDataset = new ModifyDataset<>(beforeTags, afterTags);
            list.add(tagModifyDataset);

        } catch (Exception e) {
            log.error("driverProfileTagTransferProcessing,e->{}",e.getMessage());
            return new ModifyDataset<T>();
        } finally {
            transactionManager.commit(status);
        }
        return this.reflectionProcessing(list);
    }

    /**
     * 反射处理数据
     * @param modifyDatasets
     * @return
     */
    private ModifyDataset<T> reflectionProcessing(List<ModifyDataset<?>> modifyDatasets) {
        //比对结字符串
        List<String> beforeStr = new ArrayList<>();
        List<String> afterStr = new ArrayList<>();

        modifyDatasets.forEach(dataset -> {
            if (dataset.getAfterActionData() instanceof List ||dataset.getBeforeActionData() instanceof List ) {
                //如果是附件集合 判断该集合中是否有数据修改
                if (!ReflectUtils.dataCompare((List) dataset.getAfterActionData(), (List) dataset.getBeforeActionData())) {
                    ReflectUtils.compareAndReplaceOldDataList((List) dataset.getAfterActionData(), (List) dataset.getBeforeActionData());
                    beforeStr.add(ReflectUtils.convertClassDataToChineseList((List) dataset.getBeforeActionData()));
                    afterStr.add(ReflectUtils.convertClassDataToChineseList((List) dataset.getAfterActionData()));
                }
            } else {
                ReflectUtils.compareAndReplaceOldData(dataset.getAfterActionData(), dataset.getBeforeActionData());
                beforeStr.addAll(ReflectUtils.convertClassDataToChinese(dataset.getBeforeActionData()));
                afterStr.addAll(ReflectUtils.convertClassDataToChinese(dataset.getAfterActionData()));
            }
        });

        //组装数据
        ModifyDataset<T> modifyDataset = new ModifyDataset<T>();
        modifyDataset.setBeforeStr(StringUtils.join(beforeStr.stream().filter(f->!StringUtils.isEmpty(f)).distinct().collect(Collectors.toList()), "，"));
        modifyDataset.setAfterStr(StringUtils.join(afterStr.stream().filter(f->!StringUtils.isEmpty(f)).distinct().collect(Collectors.toList()),"，"));
        return modifyDataset;
    }
}
