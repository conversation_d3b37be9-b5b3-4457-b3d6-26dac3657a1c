package com.zly.project.system.controller;

import com.zly.common.enums.ClientType;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.PageUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.system.domain.SysClientLog;
import com.zly.project.system.domain.SysClientLogRes;
import com.zly.project.system.service.impl.SysClientLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日志Controller
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Api(tags = "日志管理")
@RestController
@RequestMapping("/sys/log")
public class SysClientLogController extends BaseController {
	@Autowired
	private SysClientLogService sysClientLogService;

	/**
	 * 日志列表
	 *
	 * @param bean
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('sys:client:log:list')")
	@RequestMapping(value = "/list", method = RequestMethod.POST)
	@ApiOperation(value = "日志列表", notes = "日志列表")
	@ResponseBody
	public TableDataInfo lists(@RequestBody SysClientLog bean) {

		try {
			Map<String, Object> map = new HashMap<String, Object>();
			if (StringUtils.isNotBlank(bean.getBeginTime())) {
				map.put("beginTime", bean.getBeginTime() + " 00:00:00");
			}
			if (StringUtils.isNotBlank(bean.getEndTime())) {
				map.put("endTime", bean.getEndTime() + " 23:59:59");
			}
			if (null != bean.getActionType() && 0 < bean.getActionType()) {
				map.put("actionType", bean.getActionType());
			}
			if (null != bean.getActionScene() && 0 < bean.getActionScene()) {
				map.put("actionScene", bean.getActionScene());
			}
			if (StringUtils.isNotBlank(bean.getActionName())) {
				map.put("actionName", bean.getActionName().trim());
			}
			if (StringUtils.isNotBlank(bean.getUserName())) {
				map.put("userName", bean.getUserName().trim());
			}
			if (bean.getPlatform() != null) {
				map.put("platform", bean.getPlatform());
			}

			// 需要查询的表名列表
			List<String> tableNames = new ArrayList<>();

			Date dateStart = DateUtils.strToDate(bean.getBeginTime(), DateUtils.YYMMDD);
			Date dateEnd = DateUtils.strToDate(bean.getEndTime(), DateUtils.YYMMDD);
			if (null == dateStart && null == dateEnd) {
				// 起始时间、结束时间都不存在，则查询当月数据
				String tableName = "sys_client_log_" + DateUtils.getCurrentMonthString();
				tableNames.add(tableName);
			} else { // 起始时间或结束时间存在

				// 指定了结束时间，则必须指定开始时间
				if (null == dateStart) {
					return getDataTable(new ArrayList<>());
				}

				// 如果只指定了开始时间，则结束时间为开始时间的那个月
				if (null == dateEnd) {
					dateEnd = DateUtils.getTheLastMomentOfMonth(dateStart);
				}

				List<String> yearMonthList = new ArrayList<>();
				int monthCount = DateUtils.getMonthCountCovered(dateStart, dateEnd);
				if (0 == monthCount) {
					yearMonthList.add(DateUtils.dateToStr(dateStart, DateUtils.YM));
				} else {
					for (int i = 0; i < monthCount; i++) {
						Calendar calendar = Calendar.getInstance();
						calendar.setTime(dateStart);
						calendar.add(Calendar.MONTH, i);
						yearMonthList.add(DateUtils.dateToStr(calendar.getTime(), DateUtils.YM));
					}
				}

				// 查询当前数据库
				String database = sysClientLogService.selectDatabase();
				for (String yearMonth : yearMonthList) {
					String tableName = "sys_client_log_" + yearMonth;
					// 如果表存在，则加入列表
					boolean exist = sysClientLogService.tableExist(database, tableName);
					if (exist) {
						tableNames.add(tableName);
					}
				}
			}
			if (tableNames.isEmpty()) {
				return getDataTable(new ArrayList<>());
			}
			map.put("tableNames", tableNames);

			PageUtils.startPage();
			List<SysClientLogRes> list = sysClientLogService.selectByParams(map);
			for (SysClientLogRes sysClientLog : list) {
				sysClientLog.setUserName(sysClientLog.getUserName().split("_")[0]);
				String actionNameNew = replacePhoneNumber(sysClientLog.getActionName());
				sysClientLog.setActionName(actionNameNew);

				//处理日志对应
				SysClientLogRes res = new SysClientLogRes();
				BeanUtils.copyProperties(sysClientLog, res);
				String[] beforeArrays = res.getBeforeActionData().split("，");
				String[] afterArrays = res.getAfterActionData().split("，");
				List<Map<String,String>> mapList = new ArrayList<>();

				for (int i = 0; i < afterArrays.length; i++) {
					String[] afterSplit = afterArrays[i].split("：");
					if (afterSplit.length == 0 || StringUtils.isEmpty(afterSplit[0])) {
						continue;
					}
					Map<String, String> logMap = new HashMap<>();
					logMap.put("filed", afterSplit[0]);
					logMap.put("afterData", (afterSplit.length > 1 ? afterSplit[1] : " "));
					if (beforeArrays.length > 0) {
						for (int j = 0; j < beforeArrays.length; j++) {
							String[] beforeSplit = beforeArrays[j].split("：");
							if (afterSplit[0].equals(beforeSplit[0])) {
								logMap.put("beforeData", (beforeSplit.length > 1 ? beforeSplit[1] : " "));
								break;
							} else {
								logMap.put("beforeData", " ");
							}
						}
					} else {
						logMap.put("beforeData", " ");
					}

					mapList.add(logMap);
				}
				sysClientLog.setLogValueList(mapList);
			}
			return getDataTable(list);
		} catch (Exception e) {
			logger.error("获取客户端日志列表失败:{}",e);
			return getDataTable(new ArrayList<>());
		}
	}

	private static String replacePhoneNumber(String input) {
		// 定义需要替换的格式
		String regex = "\\d+_\\d+";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(input);

		// 替换匹配的内容
		StringBuffer stringBuffer = new StringBuffer();
		while (matcher.find()) {
			String replacement = matcher.group().split("_")[0];
			matcher.appendReplacement(stringBuffer, replacement);
		}
		matcher.appendTail(stringBuffer);

		return stringBuffer.toString();
	}

}
