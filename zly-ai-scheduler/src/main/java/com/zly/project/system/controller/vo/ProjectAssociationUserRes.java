package com.zly.project.system.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * @ClassName: RoleAssociationProject
 * @Description: 角色关联项目
 * @Author: CYS
 * @Date: 2023/7/18 9:29
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ProjectAssociationUserRes {
	@ApiModelProperty("项目Id")
	private Long projectId;
	@ApiModelProperty("项目名称")
	private String projectName;
}
