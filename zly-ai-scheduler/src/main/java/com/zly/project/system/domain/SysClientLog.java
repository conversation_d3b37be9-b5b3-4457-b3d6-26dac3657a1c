package com.zly.project.system.domain;

import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 日志对象 sys_client_log
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
public class SysClientLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    private String nickName;

    /** 操作 */
    @ApiModelProperty("操作")
    @Excel(name = "操作")
    private String actionName;

    /** 操作场景 */
    @ApiModelProperty("操作场景(1：登录系统， 2：每日货源，3：常发货源，4：司机画像，5：司机画像标记)")
    private Integer actionScene;

    /** 操作类型 */
    @Excel(name = "操作类型")
    @ApiModelProperty("操作类型(1:查询 2:新增 3:删除 4:更新 5:其他操作)")
    private Integer actionType;

    /** 平台(1:PC 2:微信) */
    @Excel(name = "平台(0:AI调度系统)")
    @ApiModelProperty("平台(0:AI调度系统)")
    private Integer platform;

    /** ip */
    @ApiModelProperty("ip")
    private String ip;

    /** 商户id */
    @Excel(name = "商户id")
    private Long customerId;

    /** 表名 */
    private String tableName;

    /** 开始时间 */
    private String beginTime;

    /** 结束时间 */
    private String endTime;

    /** 操作人 */
    @ApiModelProperty("操作人")
    private String userName;

    @ApiModelProperty("变更前数据")
    private String beforeActionData;
    @ApiModelProperty("变更后数据")
    private String afterActionData;
    @ApiModelProperty("关键字")
    private String keyWord;
}
