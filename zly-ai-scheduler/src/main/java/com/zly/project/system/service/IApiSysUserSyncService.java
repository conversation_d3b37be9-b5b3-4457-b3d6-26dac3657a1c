package com.zly.project.system.service;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.system.controller.vo.SysUserRes;
import com.zly.project.system.domain.SysUser;

import java.util.List;

/**
 * 用户同步服务接口
 * 
 * <AUTHOR>
 */
public interface IApiSysUserSyncService
{

    /**
     * 远程同步善道数据
     * @return
     */
    CommonResult<List<SysUser>> getShandaoUsersFromRemote();

    /**
     * 同步善道用户信息到本地数据库
     * 
     * @param remoteUsers 远程获取的用户列表
     * @return 同步结果
     */
    AjaxResult syncShandaoUsers(List<SysUser> remoteUsers);

    /**
     * 同步单个用户信息
     * 
     * @param remoteUser 远程用户信息
     * @return 同步结果
     */
    AjaxResult syncSingleUser(SysUser remoteUser);

    /**
     * 检查用户是否存在（根据用户名）
     * 
     * @param userName 用户名
     * @return 用户信息，不存在返回null
     */
    SysUser checkUserExists(String userName);

    /**
     * 为新用户分配默认角色（普通角色）
     * 
     * @param remoteUser 用户ID
     * @return 分配结果
     */
    boolean assignDefaultRole(SysUser remoteUser);

    /**
     * 获取普通角色ID
     *
     * @return 普通角色ID
     */
    Long getCommonRoleId();

    /**
     * 根据用户编号获取详细信息
     *
     * @param userId 用户ID
     * @return 用户详细信息
     */
    CommonResult<SysUserRes> getUserInfo(Long userId);
}
