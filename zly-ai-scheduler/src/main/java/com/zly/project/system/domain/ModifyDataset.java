package com.zly.project.system.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @ClassName: ModifyDatasetReq
 * @Description: 修改数据集
 * @Author: CYS
 * @Date: 2024/12/17 15:31
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ModifyDataset<T> {

    @ApiModelProperty("修改前数据")
    private T beforeActionData;
    @ApiModelProperty("修改后数据")
    private T afterActionData;

    @ApiModelProperty("修改前字符串")
    private String beforeStr;

    @ApiModelProperty("修改后字符串")
    private String afterStr;

    public ModifyDataset(T beforeActionData, T afterActionData) {
        this.beforeActionData = beforeActionData;
        this.afterActionData = afterActionData;
    }


}
