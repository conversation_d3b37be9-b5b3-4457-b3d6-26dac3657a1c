package com.zly.project.system.controller.vo;

import com.zly.project.system.domain.SysPost;
import com.zly.project.system.domain.SysRole;
import com.zly.project.system.domain.SysUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SysUserRes {

	@ApiModelProperty(value = "用户信息")
	SysUser sysUser;

	@ApiModelProperty(value = "当前用户选择的部门id")
	List<Long> postIds;
	@ApiModelProperty(value = "当前网货平台所有的部门信息")
	List<SysPost> posts;

	@ApiModelProperty(value = "当前用户选择的角色id")
	List<Long> roleIds;
	@ApiModelProperty(value = "当前网货平台所有的角色信息")
	List<SysRole> roles;

	@ApiModelProperty(value = "当前操作（新增、编辑、查询）的用户所选择的项目信息")
	List<ProjectAssociationUserRes> selectedContract;

	@ApiModelProperty(value = "当前登录的用户所有的项目信息")
	List<ProjectAssociationUserRes> isSelfContractIds;

}
