package com.zly.project.system.controller;

import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.service.IApiSysUserSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户同步管理
 * 
 * <AUTHOR>
 */
@Api(value = "用户同步管理", tags = "用户同步管理")
@RestController
@RequestMapping("/api/user")
public class ApiSysUserController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(ApiSysUserController.class);

    @Autowired
    private IApiSysUserSyncService userSyncService;

    /**
     * 同步善道用户信息
     */
    @Anonymous
    @ApiOperation(value = "同步善道用户信息", notes = "从远程接口获取善道用户信息并同步到本地数据库")
    @PostMapping("/syncShandaoUsers")
    public AjaxResult syncShandaoUsers()
    {
        try
        {
            logger.info("开始同步善道用户信息");
            // 1. 调用远程接口获取善道用户信息
            CommonResult<List<SysUser>> remoteResult = userSyncService.getShandaoUsersFromRemote();
            if (!remoteResult.isSuccess())
            {
                String errorMsg = remoteResult.getMsg();
                logger.error("获取善道用户信息失败: {}", errorMsg);
                return AjaxResult.error("获取善道用户信息失败: " + errorMsg);
            }
            List<SysUser> remoteUsers = remoteResult.getData();
            if (remoteUsers == null || remoteUsers.isEmpty())
            {
                logger.warn("远程接口返回的用户数据为空");
                return AjaxResult.success("远程接口返回的用户数据为空，无需同步");
            }
            logger.info("获取到 {} 个善道用户信息，开始同步", remoteUsers.size());
            // 2. 同步用户信息到本地数据库
            AjaxResult syncResult = userSyncService.syncShandaoUsers(remoteUsers);
            logger.info("善道用户信息同步完成: {}", syncResult.get("msg"));
            return syncResult;
        }
        catch (Exception e)
        {
            logger.error("同步善道用户信息异常", e);
            return AjaxResult.error("同步善道用户信息异常: " + e.getMessage());
        }
    }

    /**
     * 手动触发单个用户同步（用于测试）
     */
    @Anonymous
    @ApiOperation(value = "手动同步单个用户", notes = "手动同步单个用户信息到本地数据库")
    @PostMapping("/syncSingleUser")
    public AjaxResult syncSingleUser(SysUser user)
    {
        try
        {
            logger.info("开始手动同步用户: {}", user.getUserName());
            
            AjaxResult result = userSyncService.syncSingleUser(user);
            
            logger.info("用户同步完成: {}", result.get("msg"));
            return result;
        }
        catch (Exception e)
        {
            logger.error("手动同步用户异常", e);
            return AjaxResult.error("手动同步用户异常: " + e.getMessage());
        }
    }

    /**
     * 获取普通角色ID（用于测试）
     */
    @Anonymous
    @ApiOperation(value = "获取普通角色ID", notes = "获取系统中普通角色的ID")
    @PostMapping("/getCommonRoleId")
    public AjaxResult getCommonRoleId()
    {
        try
        {
            Long roleId = userSyncService.getCommonRoleId();
            return AjaxResult.success("普通角色ID", roleId);
        }
        catch (Exception e)
        {
            logger.error("获取普通角色ID异常", e);
            return AjaxResult.error("获取普通角色ID异常: " + e.getMessage());
        }
    }
}
