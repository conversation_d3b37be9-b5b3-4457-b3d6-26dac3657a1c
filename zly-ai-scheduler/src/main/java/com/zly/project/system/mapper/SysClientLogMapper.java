package com.zly.project.system.mapper;

import com.zly.project.system.domain.SysClientLog;
import com.zly.project.system.domain.SysClientLogRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 日志Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
public interface SysClientLogMapper
{

	String selectDatabase();

	int tableCount(@Param("database") String database, @Param("tableName") String tableName);

	int createTable(@Param("tableName") String tableName);

	List<SysClientLogRes> selectByParams(Map<String, Object> map);

	int insertSelective(SysClientLog sysClientLog);

}
