package com.zly.project.system.service.impl;

import com.zly.common.constant.BusinessConstants;
import com.zly.common.enums.ClientType;
import com.zly.common.utils.*;
import com.zly.common.utils.ip.IpUtils;
import com.zly.framework.security.LoginUser;
import com.zly.framework.security.service.TokenService;
import com.zly.project.system.domain.ModifyDataset;
import com.zly.project.system.domain.SysClientLog;
import com.zly.project.system.domain.SysClientLogRes;
import com.zly.project.system.mapper.SysClientLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Slf4j
@Service
public class SysClientLogService {

	@Resource
	private SysClientLogMapper sysClientLogMapper;
	@Resource
	private TokenService tokenService;
	@Resource
	private SysClientLogTransferService sysClientLogTransferService;

	/**
	 * 插入操作日志
	 *
	 * @param actionScene
	 *            操作场景
	 * @param actionType
	 *            操作类型(1:操作 2:查询 3:插入 4:更新 5:删除 6:登录 7:退出)
	 * @param actionName
	 *            操作名称
	 */
	public void insertLog(int actionScene, int actionType, String actionName,String keyWorld) {
		String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
		ClientType clientType = ClientType.____;
		LoginUser loginUser = null;
		try {
			loginUser = SecurityUtils.getLoginUser();
		} catch (Exception e) {
			log.info("insertLog,e:{}",e);
		}
		this.insertLog(actionScene, actionType, actionName, ip, loginUser, clientType, keyWorld);
	}

	/**
	 * 插入操作日志
	 *
	 * @param actionScene
	 *            操作场景
	 * @param actionType
	 *            操作类型(1:操作 2:查询 3:插入 4:更新 5:删除 6:登录 7:退出)
	 * @param actionName
	 *            操作名称
	 */
	public void insertLog(int actionScene, int actionType, String actionName,String keyWorld,List<Long> ids) {
		try {
			if (CommonUtil.isNullOrEmpty(ids)) {
				ids = Collections.singletonList(0L);
			}
			String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
			ClientType clientType = ClientType.____;
			LoginUser loginUser = null;
			try {
				loginUser = SecurityUtils.getLoginUser();
			} catch (Exception e) {

			}
			ModifyDataset<T> tModifyDataset = new ModifyDataset<>();

			switch (actionScene) {
				case 2://每日货源
					tModifyDataset = sysClientLogTransferService.dailyCargoLogTransferProcessing(ids.get(0));
					break;
				case 3://常发货源
					tModifyDataset = sysClientLogTransferService.frequentCargoLogTransferProcessing(ids.get(0));
					break;
				case 4://运力信息
					tModifyDataset = sysClientLogTransferService.driverProfileTransferProcessing(ids.get(0));
					break;
				case 5://司机画像标记
					tModifyDataset = sysClientLogTransferService.driverProfileTagTransferProcessing(ids.get(0));
					break;
			}
			this.insertLog(actionScene, actionType, actionName, ip, loginUser, clientType, keyWorld, tModifyDataset);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 写入日志表
	 *
	 * @param actionScene
	 * @param actionType
	 * @param actionName
	 * @param ip
	 * @param loginUser
	 * @param clientType
	 */
	@Async
	public void insertLog(int actionScene, int actionType, String actionName, String ip, LoginUser loginUser, ClientType clientType,String keyWorld,ModifyDataset<T> tModifyDataset) {
        try {
            String tableName = this.createTable();

			Long userId = loginUser == null ? 0L : loginUser.getUserId();
            String nickname = SecurityUtils.getNickname();
            String userName = SecurityUtils.getUsername();
			Long customerId = 0L;

            SysClientLog logInsert = new SysClientLog();
            logInsert.setTableName(tableName);
            logInsert.setId(TextUtil.generateId());
            logInsert.setUserId(userId);
            logInsert.setActionScene(actionScene);
            logInsert.setActionType(actionType);
            logInsert.setActionName(StringUtils.substring(userName + "(" + nickname + ")" + actionName.replace(",", " <br> "), 0, 5000));
            logInsert.setUserName(userName);
            logInsert.setNickName(nickname);
            logInsert.setIp(StringUtils.substring(ip, 0, 150));
            logInsert.setCustomerId(customerId);
            logInsert.setPlatform(clientType.val());
            logInsert.setCreateTime(new Date());
            logInsert.setKeyWord(StringUtils.substring(keyWorld, 0, 100));
            logInsert.setBeforeActionData(StringUtils.substring(tModifyDataset.getBeforeStr(), 0, 5000));
            logInsert.setAfterActionData(StringUtils.substring(tModifyDataset.getAfterStr(), 0, 5000));
            this.insertSelective(logInsert);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	/**
	 * 写入日志表
	 *
	 * @param actionScene
	 * @param actionType
	 * @param actionName
	 * @param ip
	 * @param loginUser
	 * @param clientType
	 */
	@Async
	public void insertLog(int actionScene, int actionType, String actionName, String ip, LoginUser loginUser, ClientType clientType,String keyWorld) {
        try {
            String tableName = this.createTable();
			Long userId = loginUser == null ? 0L : loginUser.getUserId();
            String nickname = SecurityUtils.getNickname();
            String userName = SecurityUtils.getUsername();
			Long customerId = 0L;

            SysClientLog logInsert = new SysClientLog();
            logInsert.setTableName(tableName);
            logInsert.setId(TextUtil.generateId());
            logInsert.setUserId(userId);
            logInsert.setActionScene(actionScene);
            logInsert.setActionType(actionType);
            logInsert.setActionName(StringUtils.substring(userName + "(" + nickname + ")" + actionName.replace(",", " <br> "), 0, 5000));
            logInsert.setUserName(userName);
            logInsert.setNickName(nickname);
            logInsert.setIp(StringUtils.substring(ip, 0, 150));
            logInsert.setCustomerId(customerId);
            logInsert.setPlatform(clientType.val());
            logInsert.setKeyWord(StringUtils.substring(keyWorld, 0, 100));
            logInsert.setCreateTime(new Date());
            this.insertSelective(logInsert);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	/**
	 * 写入日志表
	 *
	 * @param actionName
	 */
	@Async
	public void upFileInsertLog(int actionScene, int actionType, String actionName, Long customerId,String driverName,String keyWorld) {
        try {
            String tableName = this.createTable();
            SysClientLog logInsert = new SysClientLog();
            logInsert.setTableName(tableName);
            logInsert.setId(TextUtil.generateId());
            logInsert.setUserId(0L);
            logInsert.setActionScene(actionScene);
            logInsert.setActionType(actionType);
            logInsert.setActionName(StringUtils.substring(actionName, 0, 5000).replace(",", " <br> "));
            logInsert.setUserName(driverName);
            logInsert.setNickName(driverName);
            logInsert.setIp("127.0.0.1");
            logInsert.setCustomerId(customerId);
            logInsert.setPlatform(3);
            logInsert.setKeyWord(StringUtils.substring(keyWorld, 0, 100));
            logInsert.setCreateTime(new Date());
            this.insertSelective(logInsert);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	/**
	 * 写入日志表
	 *
	 * @param actionName
	 */
	@Async
	public void transferInsertLog(String actionName,String keyWorld) {
        try {
            String tableName = this.createTable();
            SysClientLog logInsert = new SysClientLog();
            logInsert.setTableName(tableName);
            logInsert.setId(TextUtil.generateId());
            logInsert.setUserId(0L);
            logInsert.setActionScene(20);
            logInsert.setActionType(5);
            logInsert.setActionName(StringUtils.substring(actionName, 0, 5000).replace(",", " <br> "));
            logInsert.setUserName("数据迁移");
            logInsert.setNickName("数据迁移");
            logInsert.setIp("127.0.0.1");
            logInsert.setCustomerId(10086L);
            logInsert.setPlatform(1);
            logInsert.setCreateTime(new Date());
            logInsert.setKeyWord(StringUtils.substring(keyWorld, 0, 100));
            this.insertSelective(logInsert);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	/**
	 * 定时任务日志
	 *
	 * @param actionScene
	 * @param actionType
	 * @param actionName
	 */
	@Async
	public void insertLogTask(int actionScene, int actionType, String actionName,String keyWorld) {
        try {
            String tableName = this.createTable();

            SysClientLog logInsert = new SysClientLog();
            logInsert.setTableName(tableName);
            logInsert.setId(TextUtil.generateId());
            logInsert.setUserId(0L);
            logInsert.setActionScene(actionScene);
            logInsert.setActionType(actionType);
            logInsert.setActionName(StringUtils.substring(("(定时任务)" + actionName), 0, 5000).replace(",", " <br> "));
            logInsert.setUserName("定时任务");
            logInsert.setNickName("定时任务");
            logInsert.setIp(IpUtils.LOOPBACK_IP);
            logInsert.setCustomerId(0L);
            logInsert.setPlatform(0);
            logInsert.setKeyWord(StringUtils.substring(keyWorld, 0, 100));
            logInsert.setCreateTime(new Date());
            this.insertSelective(logInsert);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

	/**
	 * 创建当月日志表
	 *
	 * @return
	 */
	@NotNull
	private String createTable() {
		String tableName = "sys_client_log_" + DateUtils.getCurrentMonthString();

		// 获取当前连接的数据库名
		String database = this.selectDatabase();
		// 判断是否已经有本月日志表，如果没有则新建该表
		boolean exist = this.tableExist(database, tableName);
		if (!exist) {
			this.createTable(tableName);
		}
		return tableName;
	}

	/**
	 * 指定数据库中是否存在指定表
	 *
	 * @param database
	 *            数据库名
	 * @param tableName
	 *            指定表名
	 * @return
	 */
	public boolean tableExist(String database, String tableName) {
		int cnt = this.sysClientLogMapper.tableCount(database, tableName);
		return cnt > 0;
	}

	/**
	 * 在当前数据库中创建新表
	 *
	 * @param tableName
	 *            新表表名
	 */
	public void createTable(String tableName) {
		this.sysClientLogMapper.createTable(tableName);
	}

	/**
	 * 查询当前数据库名称
	 *
	 * @return
	 */
	public String selectDatabase() {
		return sysClientLogMapper.selectDatabase();
	}

	public int insertSelective(SysClientLog bean) {
		int cnt = 0;
        try {
            cnt = this.sysClientLogMapper.insertSelective(bean);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cnt;
	}

	public List<SysClientLogRes> selectByParams(Map<String, Object> map) {
		return this.sysClientLogMapper.selectByParams(map);
	}

//	public void insertLog(int actionScene, int actionType, String actionName, String keyWorld, Long id) {
//		insertLog(actionScene, actionType, actionName, keyWorld, Collections.singletonList(id));
//	}
}
