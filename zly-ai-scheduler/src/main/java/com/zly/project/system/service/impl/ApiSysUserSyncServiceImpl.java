package com.zly.project.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zly.common.constant.ApiUrlConstants;
import com.zly.common.utils.RemoteApiUtil;
import com.zly.common.utils.StringUtils;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.system.controller.vo.SysUserRes;
import com.zly.project.system.domain.SysRole;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.domain.SysUserRole;
import com.zly.project.system.mapper.SysUserMapper;
import com.zly.project.system.mapper.SysUserRoleMapper;
import com.zly.project.system.service.IApiSysUserSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.json.JSONObject;

import static com.zly.common.constant.ApiUrlConstants.SHANDAO_USERS;

/**
 * 用户同步服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class ApiSysUserSyncServiceImpl implements IApiSysUserSyncService
{
    private static final Logger logger = LoggerFactory.getLogger(ApiSysUserSyncServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Resource
    private ObjectMapper objectMapper;

    /** 普通角色ID */
    private static final Long COMMON_ROLE_ID = 2L;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RemoteApiUtil remoteApiUtil;

    /**
     * 从远程接口获取善道用户信息
     *
     * @return 远程用户信息
     */
    public CommonResult<List<SysUser>> getShandaoUsersFromRemote()
    {
        try
        {
            logger.info("调用远程接口获取善道用户信息: {}", SHANDAO_USERS);
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String token = remoteApiUtil.getAccessToken();
            if (StringUtils.isNotEmpty(token)) {
                headers.add("Authorization", "Bearer " + token);
            }
            // 创建请求实体
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);
            // 发送请求并获取响应
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    ApiUrlConstants.getBaseUrl() + SHANDAO_USERS,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            String body = responseEntity.getBody();
            CommonResult<List<SysUser>> result = objectMapper.readValue(body, new TypeReference<CommonResult<List<SysUser>>>() {});
            if (result != null && result.isSuccess())
            {
                logger.info("远程接口调用成功，返回数据: {}", result);
                return result;
            }
            else
            {
                String errorMsg = result != null ? result.getMsg() : "远程接口返回异常";
                logger.error("远程接口调用失败: {}", errorMsg);
                return CommonResult.error(errorMsg);
            }
        }
        catch (Exception e)
        {
            logger.error("调用远程接口异常", e);
            return CommonResult.error("调用远程接口异常: " + e.getMessage());
        }
    }


    /**
     * 同步善道用户信息到本地数据库
     * 
     * @param remoteUsers 远程获取的用户列表
     * @return 同步结果
     */
    @Override
    @Transactional
    public AjaxResult syncShandaoUsers(List<SysUser> remoteUsers)
    {
        if (remoteUsers == null || remoteUsers.isEmpty())
        {
            return AjaxResult.error("远程用户数据为空");
        }

        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (SysUser remoteUser : remoteUsers)
        {
            try
            {
                AjaxResult result = syncSingleUser(remoteUser);
                if (result.get("code").equals(200))
                {
                    successCount++;
                }
                else
                {
                    failCount++;
                    errorMessages.add("用户 " + remoteUser.getUserName() + ": " + result.get("msg"));
                }
            }
            catch (Exception e)
            {
                failCount++;
                errorMessages.add("用户 " + remoteUser.getUserName() + ": " + e.getMessage());
                logger.error("同步用户失败: " + remoteUser.getUserName(), e);
            }
        }

        String message = String.format("同步完成，成功: %d, 失败: %d", successCount, failCount);
        if (!errorMessages.isEmpty())
        {
            message += "，错误详情: " + String.join("; ", errorMessages);
        }

        return successCount > 0 ? AjaxResult.success(message) : AjaxResult.error(message);
    }

    /**
     * 同步单个用户信息
     * 
     * @param remoteUser 远程用户信息
     * @return 同步结果
     */
    @Override
    @Transactional
    public AjaxResult syncSingleUser(SysUser remoteUser)
    {
        if (remoteUser == null || StringUtils.isEmpty(remoteUser.getUserName()))
        {
            return AjaxResult.error("用户信息不完整");
        }

        try
        {
            // 检查用户是否存在
            SysUser existingUser = checkUserExists(remoteUser.getUserName());
            
            if (existingUser != null)
            {
                // 用户存在，执行更新操作
                return updateExistingUser(existingUser, remoteUser);
            }
            else
            {
                // 用户不存在，执行新增操作
                return insertNewUser(remoteUser);
            }
        }
        catch (Exception e)
        {
            logger.error("同步用户失败: " + remoteUser.getUserName(), e);
            return AjaxResult.error("同步用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新现有用户
     */
    private AjaxResult updateExistingUser(SysUser existingUser, SysUser remoteUser)
    {
        // 更新用户信息
        existingUser.setNickName(remoteUser.getNickName());
        existingUser.setEmail(remoteUser.getEmail());
        existingUser.setPhonenumber(remoteUser.getPhonenumber());
        existingUser.setSex(remoteUser.getSex());
        existingUser.setStatus(remoteUser.getStatus());
        existingUser.setPwd(remoteUser.getPwd());
        existingUser.setFreightForwarderId(remoteUser.getFreightForwarderId());
        existingUser.setDefaultProject(remoteUser.getDefaultProject());
        existingUser.setUpdateTime(new Date());
        existingUser.setUpdateBy("system");

        int result = userMapper.updateUser(existingUser);
        if (result > 0)
        {
            return AjaxResult.success("用户更新成功: " + existingUser.getUserName());
        }
        else
        {
            return AjaxResult.error("用户更新失败: " + existingUser.getUserName());
        }
    }

    /**
     * 新增用户
     */
    private AjaxResult insertNewUser(SysUser remoteUser)
    {
        // 设置默认值
        remoteUser.setDelFlag("0");
        remoteUser.setCreateTime(new Date());
        remoteUser.setCreateBy("system");
        
        // 如果没有设置默认项目权限，设置为1（所有项目）
        if (remoteUser.getDefaultProject() == null)
        {
            remoteUser.setDefaultProject(1L);
        }
        // 新增用户
        int result = userMapper.insertUser(remoteUser);
        if (result > 0)
        {
            // 为新用户分配默认角色
            boolean roleAssigned = assignDefaultRole(remoteUser);
            if (roleAssigned)
            {
                return AjaxResult.success("用户新增成功并分配角色: " + remoteUser.getUserName());
            }
            else
            {
                return AjaxResult.success("用户新增成功但角色分配失败: " + remoteUser.getUserName());
            }
        }
        else
        {
            return AjaxResult.error("用户新增失败: " + remoteUser.getUserName());
        }
    }

    /**
     * 检查用户是否存在（根据用户名）
     * 
     * @param userName 用户名
     * @return 用户信息，不存在返回null
     */
    @Override
    public SysUser checkUserExists(String userName)
    {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 为新用户分配默认角色（普通角色）
     * 
     * @param remoteUser 用户ID
     * @return 分配结果
     */
    @Override
    public boolean assignDefaultRole(SysUser remoteUser)
    {
        Long userId = remoteUser.getUserId();
        try
        {
            // 删除用户与角色关联（如果存在）
            userRoleMapper.deleteUserRoleByUserId(userId);

            boolean adminFlag = false;
            List<SysRole> roles = remoteUser.getRoles();
            if (CollectionUtil.isNotEmpty(roles)){
                List<SysRole> roleList = roles.stream().filter(r -> StringUtils.equals(r.getRoleKey(), "admin")).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(roleList)){
                    adminFlag = true;
                }
            }
            SysUserRole userRole = new SysUserRole();
            if (adminFlag){
                // 新增用户与角色管理
                userRole.setUserId(userId);
                userRole.setRoleId(1L);
            }else{
                // 新增用户与角色管理
                userRole.setUserId(userId);
                userRole.setRoleId(COMMON_ROLE_ID);
            }

            List<SysUserRole> userRoleList = new ArrayList<>();
            userRoleList.add(userRole);
            
            int result = userRoleMapper.batchUserRole(userRoleList);
            return result > 0;
        }
        catch (Exception e)
        {
            logger.error("分配默认角色失败，用户ID: " + userId, e);
            return false;
        }
    }

    /**
     * 获取普通角色ID
     *
     * @return 普通角色ID
     */
    @Override
    public Long getCommonRoleId()
    {
        return COMMON_ROLE_ID;
    }

    /**
     * 根据用户编号获取详细信息
     *
     * @param userId 用户ID
     * @return 用户详细信息
     */
    @Override
    public CommonResult<SysUserRes> getUserInfo(Long userId)
    {
        try
        {
            logger.info("开始获取用户详细信息，用户ID: {}", userId);

            // 构建请求URL
            String url = ApiUrlConstants.getBaseUrl() + ApiUrlConstants.USER_INFO;
            if (userId != null)
            {
                url = url + "/" + userId;
            }
            // 发送GET请求
            JSONObject response = remoteApiUtil.getWithSign(url, new HashMap<>());
            CommonResult<SysUserRes> result = objectMapper.readValue(JSONUtil.toJsonStr(response), new TypeReference<CommonResult<SysUserRes>>() {});
            if (result != null && result.isSuccess())
            {
                logger.info("成功获取用户详细信息,远程接口调用成功，返回数据: {}", result);
                return result;
            }
            else
            {
                String errorMsg = result != null ? result.getMsg() : "远程接口返回异常";
                logger.error("获取用户详细信息失败: {}", errorMsg);
                return CommonResult.error(errorMsg);
            }
        }
        catch (Exception e)
        {
            logger.error("获取用户详细信息异常，用户ID: " + userId, e);
            return CommonResult.error("获取用户详细信息异常: " + e.getMessage());
        }
    }
}
