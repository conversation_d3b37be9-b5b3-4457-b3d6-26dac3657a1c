package com.zly.project.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollectionUtil;
import com.zly.common.constant.CacheConstants;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.ValidateUtils;
import com.zly.common.utils.ip.IpUtils;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.system.controller.vo.EnterpriseInfoRes;
import com.zly.project.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.zly.common.constant.Constants;
import com.zly.common.utils.SecurityUtils;
import com.zly.framework.security.LoginBody;
import com.zly.framework.security.LoginUser;
import com.zly.framework.security.service.SysLoginService;
import com.zly.framework.security.service.SysPermissionService;
import com.zly.framework.security.service.TokenService;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.system.domain.SysMenu;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.service.ISysMenuService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysUserService userService;

    @Resource
    private RedisCache redisCache;

    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(HttpServletRequest request,@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        abnormalAccessRestrictionForIp(request,null);
        if (!ValidateUtils.checkMobilePhone(loginBody.getUsername())) {
            return AjaxResult.error("手机号格式异常");
        }
        List<EnterpriseInfoRes> list = userService.getEnterpriseInfo(loginBody.getUsername());
        if (StringUtils.isEmpty(list)) {
            return AjaxResult.error("未查询到当前手机号所对应公司，请检查手机号");
        }
        if (CollectionUtil.isNotEmpty(list) && list.size() >1){
            return AjaxResult.error("账号异常，请联系管理员");
        }
        EnterpriseInfoRes enterpriseInfoRes = list.get(0);
        Long freightForwarderId = enterpriseInfoRes.getFreightForwarderId();
        String username = loginBody.getUsername();
        if (freightForwarderId != null){
            username = username + "_" + freightForwarderId;
        }
        // 生成令牌
        String token = loginService.login(username, loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions))
        {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }


    /**
     * 接口限流
     *
     * @param httpServletRequest
     */
    public void abnormalAccessRestrictionForIp(HttpServletRequest httpServletRequest, String controller) {
        String ip = IpUtils.getIpAddr(httpServletRequest);
        // 锁定的key，不会清除
        HashMap<String, String> lockMap = redisCache.getCacheObject(CacheConstants.ABNORMAL_ACCESS_RESTRICTION_IP_LOCK_KEY);
        if (null != lockMap && lockMap.containsKey(ip)) {
            log.info("该IP异常访问接口1分钟内访问次数达到50次，已被禁止访问，{}", ip);
            throw new RuntimeException("该IP异常访问接口1分钟内访问次数达到50次，已被禁止访问，如有误操作请联系客服人员申诉");
        }
        String key = CacheConstants.ABNORMAL_ACCESS_RESTRICTION_IP_KEY + ip;
        // 如果外部传入的就直接拼接接口参数
        if (org.apache.commons.lang3.StringUtils.isNotBlank(controller)) {
            key = key + "_" + controller;
        }
        Integer visits = redisCache.getCacheObject(key);
        // 记录1分钟的访问记录。 如果为空说明是新访问，开始计数
        if (null == visits) {
            visits = 1;
        } else {
            visits++;
        }
        // 如果达到次数禁用
        if (visits == 50) {
            if (null == lockMap) {
                lockMap = new HashMap<>();
            }
            lockMap.put(ip, "超过访问次数限制，冻结账户");
            redisCache.setCacheObject(CacheConstants.ABNORMAL_ACCESS_RESTRICTION_IP_LOCK_KEY, lockMap);
        }
        redisCache.setCacheObject(key, visits, 1, TimeUnit.MINUTES);
    }
}
