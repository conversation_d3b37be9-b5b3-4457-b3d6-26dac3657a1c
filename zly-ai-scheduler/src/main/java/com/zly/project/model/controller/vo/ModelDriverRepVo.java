package com.zly.project.model.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: PortraitDriverRepVo
 * @Description: 意向司机数据 请求
 * @Author: cdh
 * @Date: 2025/3/21 17:34
 **/
@ApiModel("匹配意向司机 请求实体")
@Data
public class ModelDriverRepVo {

    @ApiModelProperty("匹配到的司机数组")
    private List<MatchedDriver> matchedDrivers;

    @ApiModelProperty("匹配司机总数")
    private Integer totalMatches;

    @ApiModelProperty("分析时间戳")
    private Long analysisTime;

    @ApiModelProperty("短信内容")
    private String buildSmsContent;

    @Data
    public static class MatchedDriver {

        @ApiModelProperty("司机画像ID")
        private Long driverProfileId;

        @ApiModelProperty("司机ID")
        private Long driverId;

        @ApiModelProperty("司机姓名")
        private String driverName;

        @ApiModelProperty("身份证")
        private String identityCard;

        @ApiModelProperty("联系电话")
        private String telephone;

        @ApiModelProperty("匹配度(%)")
        private Integer matchScore;

        @ApiModelProperty("AI分析选择原因")
        private String aiAnalysis;
    }
}
