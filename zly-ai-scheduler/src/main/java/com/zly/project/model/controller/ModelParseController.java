package com.zly.project.model.controller;


import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.framework.web.controller.BaseController;
import com.zly.project.driver.service.DriverProfileService;
import com.zly.project.model.controller.vo.ModelDataRepVo;
import com.zly.project.model.controller.vo.ModelParseReqVO;
import com.zly.project.model.controller.vo.ModelDriverRepVo;
import com.zly.project.model.controller.vo.ModelDriverReqVo;
import com.zly.project.model.service.ModelParseService;
import com.zly.project.plan.domain.TaskPlanDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zly.framework.web.domain.AjaxResult;


/**
 * 物流文本解析 Controller
 * <AUTHOR>
 */
@Api(tags ="管理后台 - 大模型解析")
@RestController
@RequestMapping("/scheduler/logistics/parse")
@Validated
@Slf4j
public class ModelParseController extends BaseController {

    @Resource
    private ModelParseService logisticsParseService;
    @Resource
    private DriverProfileService driverProfileService;

    @PreAuthorize("@ss.hasPermi('model:Parse:parseLogisticsText')")
    @PostMapping("/text")
    @ApiOperation("解析物流文本")
    public AjaxResult parseLogisticsText(@Valid @RequestBody ModelParseReqVO reqVO) {
        ModelDataRepVo result = logisticsParseService.parseLogisticsText(reqVO.getContent());
        return success(result);
    }

    @PreAuthorize("@ss.hasPermi('model:Parse:portraitDriver')")
    @PostMapping("/portraitDriver")
    @ApiOperation("获取意向司机")
    public AjaxResult portraitDriver(@Valid @RequestBody ModelDriverReqVo reqVO) {
        ModelDriverRepVo result = logisticsParseService.portraitDriver(reqVO.getTaskPlanId());
        return success(result);
    }

    @Anonymous
    @PostMapping("/analyzeDriverMatchRag")
    @ApiOperation("模型匹配司机")
    public AjaxResult analyzeDriverMatchRag() throws NoApiKeyException, InputRequiredException {
        Map<String, String> knowledgeBaseMap = driverProfileService.retrieveFromKnowledgeBaseMap(
                "常州到南京货物煤炭"
                , null, 200);
        System.out.println(knowledgeBaseMap);
        TaskPlanDO taskPlanDO = new TaskPlanDO();
        taskPlanDO.setStartPoint("常州市");
        taskPlanDO.setEndPoint("南京市");
        taskPlanDO.setCargoName("煤炭");
        taskPlanDO.setVehicleLength(12.00);
        List<String> drivers = new ArrayList<>(8);
        for (Map.Entry<String, String> entry : knowledgeBaseMap.entrySet()) {
            String value = entry.getValue();
            drivers.add(value);
        }
        String analyzeDriverMatchRag = logisticsParseService.analyzeDriverMatchRag(taskPlanDO,drivers);
        log.info("analyzeDriverMatchRag:{}",analyzeDriverMatchRag);
        return success(analyzeDriverMatchRag);
    }
}