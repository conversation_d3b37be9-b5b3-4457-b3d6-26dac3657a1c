package com.zly.project.model.controller.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 物流数据 响应结果集
 *
 * <AUTHOR>
 */
@Data
public class  ModelDataRepVo {
    private String pickupDate;      // 提货时间
    private String vehicleType;     // 车型
    private String licensePlate;    // 车牌
    private String cargoName;       // 货物名称
    private Double weight;          // 重量值
    private String weightType;      // 重量单位

    // 司机信息字段
    private String driverName;      // 司机姓名
    private String driverPhone;     // 司机电话
    private String driverIdCard;    // 司机身份证
    private String transportationQuantity;  // 运输数量
    private String destination;     // 目的地点

    private String vehicleNumberStr;  // 第几车 比如(第一车)
    private Double palletLength;      // 托盘长
    private Double palletWidth;       // 托盘宽
    private String arrivalTime;       // 到达时间
    private String routeFrom;         // 线路起始地
    private String routeTo;           // 线路目的地
    private String driverBankAccount; // 司机银行账号
    private Double vehicleLength;       // 车辆长度
    private Double vehicleWidth;      // 宽度
    private Double vehicleHeight;     // 高度
    private String vehicleMeasurement;  //长宽高的计量单位
    
    private String senderAddressRaw;  // 寄件地址
    private String receiverPhone;     // 收件方
    private String noteField;         // 注意项
    private String remark;            // 备注
    private String segmentationType;  // 细分类别
    
    // 运输明细字段
    private String daytimeUnload;     // 白天卸货标识
    private String freightDetail;     // 运费明细
    private String cargoRequirement;  // 装货要求
    private String contactInfo;       // 车联信息
    private String contactInfoPhone;  // 车联信息电话
//    private AddressInfoRepVo pickupAddress;   // 提货地址信息
//    private AddressInfoRepVo deliveryAddress; // 收货地址信息

    // 处理可能是字符串格式的宽度和高度
    @JsonProperty("vehicleLength")
    private void setVehicleLengthFromString(Object length) {
        this.vehicleLength = parseDoubleValue(length);
    }
    @JsonProperty("vehicleWidth")
    private void setVehicleWidthFromString(Object width) {
        this.vehicleWidth = parseDoubleValue(width);
    }

    @JsonProperty("vehicleHeight")
    private void setVehicleHeightFromString(Object height) {
        this.vehicleHeight = parseDoubleValue(height);
    }
    
    /**
     * 通用方法：解析对象为Double值
     * 
     * @param value 需要解析的对象
     * @return 解析后的Double值，解析失败返回null
     */
    private Double parseDoubleValue(Object value) {
        if (value instanceof String) {
            String valueStr = (String) value;
            try {
                return Double.parseDouble(valueStr.replaceAll("[^0-9.]", ""));
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return null;
    }

    /**
     * 地址信息数据结构
     */
    @Data
    public static class AddressInfoRepVo {
        private String addressDetail;   // 详细地址
        private String contactName;     // 联系人
        private String contactPhone;    // 联系电话
        private String companyName;     // 公司名称
    }
}