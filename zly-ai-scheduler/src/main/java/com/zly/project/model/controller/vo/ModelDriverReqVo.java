package com.zly.project.model.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName: PortraitDriverRepVo
 * @Description: 意向司机数据
 * @Author: cdh
 * @Date: 2025/3/21 17:34
 **/
@Data
public class ModelDriverReqVo {

	@ApiModelProperty(name = "计划ID")
	@NotNull(message = "计划ID不能为空")
	private Long taskPlanId;

}
