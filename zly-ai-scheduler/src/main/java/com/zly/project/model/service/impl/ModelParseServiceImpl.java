package com.zly.project.model.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.ResponseFormat;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.driver.DriverPortraitMarkdownConverter;
import com.zly.enums.plan.TaskPlanStatusEnum;
import com.zly.framework.config.AiModelBaiLianConfig;
import com.zly.project.driver.domain.DriverProfileDO;
import com.zly.project.driver.domain.SchedulerBlacklist;
import com.zly.project.driver.mapper.DriverProfileMapper;
import com.zly.project.driver.service.DriverProfileService;
import com.zly.project.driver.service.ISchedulerBlacklistService;
import com.zly.project.model.controller.vo.ModelDataRepVo;
import com.zly.project.model.controller.vo.ModelDriverRepVo;
import com.zly.project.model.service.ModelParseService;
import com.zly.project.plan.controller.vo.TaskPlanUpdateReqVO;
import com.zly.project.plan.domain.TaskPlanDO;
import com.zly.project.plan.service.TaskPlanService;
import com.zly.project.match.domain.TaskDriverMatchDO;
import com.zly.project.match.service.TaskDriverMatchService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.BASE_SERVER_ERROR;
import static com.zly.enums.ErrorCodeConstants.LOGISTICS_PARSE_ERROR;
import static com.zly.framework.web.domain.AjaxResult.error;

/**
 * 物流文本解析 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModelParseServiceImpl implements ModelParseService {

    @Resource
    private AiModelBaiLianConfig aiModelBaiLianConfig;

    @Resource
    private TaskPlanService taskPlanService;

    @Resource
    private TaskDriverMatchService taskDriverMatchService;

    @Resource
    private DriverProfileService driverProfileService;

    @Resource
    private DriverProfileMapper driverProfileMapper;

    @Resource
    private ISchedulerBlacklistService schedulerBlacklistService;

    @Override
    public ModelDataRepVo parseLogisticsText(String content) {
        try {
            // 调用通义千问API解析文本
            GenerationResult result = callQianWenApi(content);
            String jsonContent = result.getOutput().getChoices().get(0).getMessage().getContent();
            // 提取JSON内容
            jsonContent = extractJsonContent(jsonContent);
            log.info("parseLogisticsText,jsonContent:{}",jsonContent);
            // 解析JSON到LogisticsDataDTO对象集合
            return parseJsonToLogisticsDataList(jsonContent);
        } catch (Exception e) {
            log.error("解析物流文本失败", e);
            throw exception(LOGISTICS_PARSE_ERROR, e.getMessage());
        }
    }

    /**
     * // 司机数据开头基本信息\n 画像ID : 3\n 司机ID : 1530625353856\n 姓名 : 刘占芝\n 手机号 : 13703454896\n 身份证 : 412931197308010138\n 司机来源 : 未知\n 常运货物 : 台灯、日光灯、电机、车灯\n车辆信息\n 车辆长度 : 8米、11米\n 车辆结构 : 集装箱、高栏\n常跑路线\n 安徽省六安市-河南省南阳市 \n运单数量: 1\n平均里程: 451.0km\n路线总里程: 451.0km\n平均运费: 3200.0元\n最近订单: 2025-01-15 09:01:39\n 江苏常州-北京 \n运单数量: 0\n 苏州-济南 \n运单数量: 0司机数据结尾!
     * @param taskPlanId 计划ID
     */
    @Override
    public ModelDriverRepVo portraitDriver(Long taskPlanId) {
        // 获取任务计划
        TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskPlanId);
        if (taskPlan == null) {
            throw exception(BASE_SERVER_ERROR, "任务计划不存在");
        }
        // 判断任务计划状态，只有未调度状态才能执行匹配
        if (taskPlan.getStatus() == null || !TaskPlanStatusEnum.SCHEDULING.getStatus().equals(taskPlan.getStatus())) {
            throw exception(BASE_SERVER_ERROR, "只有未调度状态的计划可以执行司机匹配");
        }
        LocalDateTime now = LocalDateTime.now();
        long minutesBetween = java.time.Duration.between(now, taskPlan.getPlanStartTime()).toMinutes();
        // 如果距离计划开始时间只剩30分钟内，手动暂停呼叫任务
        if (minutesBetween > 0 && minutesBetween <= 30) {
            log.info("任务距离计划开始时间只剩{}分钟无法手动开启任务", minutesBetween);
            // 从处理列表中移除即将开始的任务
            throw exception(BASE_SERVER_ERROR, "任务距离计划开始时间已不足半小时无法匹配司机");
        }
        try {
              //直接匹配数据库 模糊匹配
            ModelDriverRepVo modelDriverRepVo;
            List<String> driverInfoList = new ArrayList<>(8);
            List<ModelDriverRepVo.MatchedDriver> manualMatchedDrivers = new ArrayList<>();
			// 根据起始位置和结束位置从司机画像表模糊查询匹配的司机数据
            getQueryManualMatchedDrivers(taskPlan, manualMatchedDrivers);
            // 对匹配到的司机进行打分筛选
            List<Map<String, String>> driverKeyInfoList = new ArrayList<>();
            // 从知识库中检索相关司机信息
            Map<String, String> knowledgeBaseMap = driverProfileService.retrieveFromKnowledgeBaseMap(
                    taskPlan.getStartPoint() + "到" + taskPlan.getEndPoint() + "货物" + taskPlan.getCargoName() + (taskPlan.getVehicleLength() != null ?
                            ",车长" + taskPlan.getVehicleLength() + "米" :
                            "")
                    // 从知识库中检索相关司机信息
                    , null, 200);
            if (knowledgeBaseMap != null && !knowledgeBaseMap.isEmpty()) {
                manualHandleMatchDriver(knowledgeBaseMap, driverKeyInfoList, taskPlan, manualMatchedDrivers, driverInfoList);
            }
            log.info("司机关键信息列表(仅画像ID和姓名): {}", driverKeyInfoList);
            // 执行司机匹配分析
            String result = "[]";
            if (CollectionUtil.isNotEmpty(driverInfoList)){
                result = analyzeDriverMatchRag(taskPlan,driverInfoList);
            }
            log.info("analyzeDriverMatch,result:{}",result);
            modelDriverRepVo = parseDriverDataListRag(result,manualMatchedDrivers);
            handleModelMatchDrivers(taskPlanId, modelDriverRepVo, taskPlan);
            String buildSmsContent = taskDriverMatchService.buildSmsContent(taskPlan);
			modelDriverRepVo.setBuildSmsContent(buildSmsContent);
			return modelDriverRepVo;
        }catch (Exception e){
            throw exception(BASE_SERVER_ERROR, "模型解析异常");
        }
    }

    /**
     * 处理匹配到的司机数据
     * @param taskPlanId
     * @param modelDriverRepVo
     * @param taskPlan
     */
    private void handleModelMatchDrivers(Long taskPlanId, ModelDriverRepVo modelDriverRepVo, TaskPlanDO taskPlan) {
        // 批量插入匹配司机数据到TaskDriverMatchDO表
        if (modelDriverRepVo.getMatchedDrivers() != null && !modelDriverRepVo.getMatchedDrivers().isEmpty()) {
            // 先查询该任务已经匹配的司机列表
            List<TaskDriverMatchDO> existingMatches = taskDriverMatchService.getListByTaskPlanId(taskPlanId);
            // 创建一个集合存储已存在的司机电话号码，用于快速查找
            List<String> existingDriver = new ArrayList<>();
            if (existingMatches != null && !existingMatches.isEmpty()) {
                for (TaskDriverMatchDO match : existingMatches) {
                    if (StringUtils.isNotBlank(match.getTelephone())) {
                        existingDriver.add(match.getDriverName());
                    }
                }
            }
            List<ModelDriverRepVo.MatchedDriver> newMatchedDriverList = new ArrayList<>(8);
            // 获取需要新增的司机匹配列表（过滤掉已存在的）
            List<TaskDriverMatchDO> taskDriverMatchList = getTaskDriverMatchDOS(taskPlanId, modelDriverRepVo, existingDriver,newMatchedDriverList);
            // 如果有匹配的司机数据，则批量插入
            if (!taskDriverMatchList.isEmpty()) {
                boolean taskDriverMatchBatch = taskDriverMatchService.addTaskDriverMatchBatch(taskDriverMatchList);
                log.info("成功批量插入{}条司机匹配数据，taskDriverMatchBatch:{}", taskDriverMatchList.size(), taskDriverMatchBatch);
            } else {
                log.info("没有新的司机匹配数据需要插入");
            }
            modelDriverRepVo.setMatchedDrivers(newMatchedDriverList);
            modelDriverRepVo.setTotalMatches(newMatchedDriverList.size());
            // 修改调度状态为调度中
            updateTaskPlanStatus(taskPlanId, taskPlan.getId(),TaskPlanStatusEnum.SCHEDULING.getStatus());
        }
    }

    /**
     * 司机数据去重
     * @param matchedDrivers
     */
    private static void handleRepDrivers(List<ModelDriverRepVo.MatchedDriver> matchedDrivers) {
        if (CollectionUtil.isNotEmpty(matchedDrivers)) {
            Map<String, ModelDriverRepVo.MatchedDriver> uniqueDrivers = new HashMap<>();
            for (ModelDriverRepVo.MatchedDriver driver : matchedDrivers) {
                // 使用姓名+画像ID作为唯一键
                String uniqueKey = driver.getDriverName() + "_" + (driver.getDriverProfileId() != null ? driver.getDriverProfileId() : "");
                // 如果已存在相同司机，保留分数更高的那个
                if (!uniqueDrivers.containsKey(uniqueKey) ||
                        driver.getMatchScore() > uniqueDrivers.get(uniqueKey).getMatchScore()) {
                    uniqueDrivers.put(uniqueKey, driver);
                }
            }
            // 清空原列表并添加去重后的司机
            matchedDrivers.clear();
            matchedDrivers.addAll(uniqueDrivers.values());
            log.info("去重后匹配到{}个司机", matchedDrivers.size());
        }
    }

    /**
     * 数据库模糊查询司机数据
     * @param taskPlan
     * @param manualMatchedDrivers
     */
    private void getQueryManualMatchedDrivers(TaskPlanDO taskPlan, List<ModelDriverRepVo.MatchedDriver> manualMatchedDrivers) {
        List<DriverProfileDO> matchedDrivers = driverProfileMapper.selectDriversByRoutes(taskPlan.getStartPoint(), taskPlan.getEndPoint());
        log.info("根据路线[{}-{}]匹配到{}个司机", taskPlan.getStartPoint(), taskPlan.getEndPoint(), matchedDrivers.size());
        // 如果匹配到的司机数量超过50，则不执行AI调度
        if (CollectionUtil.isNotEmpty(matchedDrivers)) {
            log.info("匹配到的司机数量超过50，不执行AI调度逻辑");
            // 处理从数据库匹配到的司机
            for (DriverProfileDO driver : matchedDrivers) {
                try {
                    // 解析司机常跑路线JSON数据
                    String routesJson = driver.getSchedulerHaulway();
                    if (StringUtils.isBlank(routesJson)) {
                        routesJson = driver.getWlhyHaulway();
                    }
                    // 计算匹配分数
                    String point = routesJson;
                    String goods = driver.getSchedulerGoods();
                    String length = driver.getSchedulerVehicleLength();
                    length = DriverPortraitMarkdownConverter
                            .mergeAndProcessVehicleLengths(null, length, null);
                    String structure = driver.getSchedulerVehicleStructure();
                    String driverName = driver.getDriverName();
                    String identityCard = driver.getIdentityCard();
                    String profileId = String.valueOf(driver.getId());
                    String startPoint = taskPlan.getStartPoint();
                    String endPoint = taskPlan.getEndPoint();
                    String cargoName = taskPlan.getCargoName();
                    Double vehicleLength = taskPlan.getVehicleLength();
                    String vehicleType = taskPlan.getVehicleType();
                    StringBuilder matchReason = new StringBuilder();
                    // 计算匹配分数
                    int matchScore = getMatchScore(point, startPoint, endPoint, matchReason, goods, cargoName, length, vehicleLength, structure, vehicleType);
                    // 如果得分达到40分以上，认为匹配成功
                    if (matchScore >= 40) {
                        ModelDriverRepVo.MatchedDriver matchedDriver = new ModelDriverRepVo.MatchedDriver();
                        matchedDriver.setDriverId(Long.valueOf(driver.getDriverId()));
                        matchedDriver.setDriverName(driverName);
                        matchedDriver.setIdentityCard(identityCard);
                        matchedDriver.setDriverProfileId(Long.valueOf(profileId));
                        matchedDriver.setTelephone(driver.getSchedulerTelephone());
                        matchedDriver.setMatchScore(matchScore);
                        matchedDriver.setAiAnalysis(matchReason.toString());
                        manualMatchedDrivers.add(matchedDriver);
                    }
                } catch (Exception e) {
                    log.error("处理司机数据异常: {}", e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 结构算分
     * @param structure
     * @param vehicleType
     * @param matchScore
     * @param matchReason
     * @return
     */
    private static int getStructureScore(String structure, String vehicleType, int matchScore, StringBuilder matchReason) {
        // 车辆结构匹配判断（10分）
        if (StringUtils.isNotBlank(structure) && StringUtils.isNotBlank(vehicleType)) {
            if (structure.contains(vehicleType)) {
                matchScore += 10;
                if (matchReason.length() > 0) {
                    matchReason.append("，");
                }
                matchReason.append("车辆结构匹配");
            }
        }
        return matchScore;
    }

    /**
     * 车辆长度算分
     * @param length
     * @param vehicleLength
     * @param matchScore
     * @param matchReason
     * @return
     */
    private static int getVehicleLengthScore(String length, Double vehicleLength, int matchScore, StringBuilder matchReason) {
        // 车长匹配判断（20分）
        if (StringUtils.isNotBlank(length) && vehicleLength != null) {
            // 提取数字部分进行比较
            String[] split = length.split("、");
            for (String lengthNum : split) {
                String lengthNumStr = lengthNum.replaceAll("[^0-9.]", "");
                try {
                    double driverVehicleLength = Double.parseDouble(lengthNumStr);
                    // 允许0.7米的误差
                    if (Math.abs(driverVehicleLength - vehicleLength) <= 0.7) {
                        matchScore += 20;
                        if (matchReason.length() > 0) {
                            matchReason.append("，");
                        }
                        matchReason.append("车长匹配");
                    }
                } catch (NumberFormatException e) {
                    log.warn("解析司机车长失败: {}", length);
                }
            }
        }
        return matchScore;
    }

    /**
     * //{"姓名":"刘占芝","常跑路线":"安徽省六安市-河南省南阳市,江苏常州-北京,苏州-济南","画像ID":"3","常运货物":"台灯、日光灯、电机、车灯","车辆长度":"8米、11米"}
     * 手动匹配司机数据
     * @param knowledgeBaseMap
     * @param driverKeyInfoList
     * @param taskPlan
     * @param matchedDrivers
     * @param driverInfoList
     */
    private static void manualHandleMatchDriver(Map<String, String> knowledgeBaseMap, List<Map<String, String>> driverKeyInfoList, TaskPlanDO taskPlan, List<ModelDriverRepVo.MatchedDriver> matchedDrivers,
            List<String> driverInfoList) {
        for (Map.Entry<String, String> stringEntry : knowledgeBaseMap.entrySet()) {
            String key = stringEntry.getKey();
            String value = stringEntry.getValue();
            Map<String, String> stringMap = getDriverKeyInfoList(key, driverKeyInfoList);
            String cargoName = taskPlan.getCargoName();
            Double vehicleLength = taskPlan.getVehicleLength();
            String startPoint = taskPlan.getStartPoint();
            String endPoint = taskPlan.getEndPoint();
            String vehicleType = taskPlan.getVehicleType();
            if (MapUtil.isNotEmpty(stringMap)){
                String point = stringMap.get("常跑路线");
                String goods = stringMap.get("常运货物");
                String length = stringMap.get("车辆长度");
                String structure = stringMap.get("车辆结构");
                String driverName = stringMap.get("姓名");
                String profileId = stringMap.get("画像ID");
                StringBuilder matchReason = new StringBuilder();
                // 计算匹配分数
                int matchScore = getMatchScore(point, startPoint, endPoint, matchReason, goods, cargoName, length, vehicleLength, structure, vehicleType);
                // 如果得分达到40分以上，认为匹配成功
                if (matchScore >= 40) {
                    ModelDriverRepVo.MatchedDriver matchedDriver = new ModelDriverRepVo.MatchedDriver();
                    try {
                        matchedDriver.setDriverProfileId(Long.parseLong(profileId));
                    } catch (NumberFormatException e) {
                        log.warn("解析司机画像ID失败: {}", profileId);
                        continue;
                    }
                    matchedDriver.setDriverName(driverName);
                    matchedDriver.setMatchScore(matchScore);
                    matchedDriver.setAiAnalysis(matchReason.toString());
                    matchedDrivers.add(matchedDriver);
                    log.info("司机匹配成功: 姓名={}, 分数={}, 原因={}", driverName, matchScore, matchReason);
                }else {
                    driverInfoList.add(value);
                }
            }
        }
    }

    /**
     * 计算所以匹配的分数总和
     * @param point
     * @param startPoint
     * @param endPoint
     * @param matchReason
     * @param goods
     * @param cargoName
     * @param length
     * @param vehicleLength
     * @param structure
     * @param vehicleType
     * @return
     */
    private static int getMatchScore(String point, String startPoint, String endPoint, StringBuilder matchReason, String goods, String cargoName, String length, Double vehicleLength, String structure,
            String vehicleType) {
        int matchScore = 0;
        // 路线匹配判断（最高50分）
        matchScore = getMatchScore(point, startPoint, endPoint, matchScore, matchReason);
        // 货物匹配判断 (20分)
        matchScore = getGoodsScore(goods, cargoName, matchScore, matchReason);
        // 车长匹配判断（20分）
        matchScore = getVehicleLengthScore(length, vehicleLength, matchScore, matchReason);
        // 车辆结构匹配判断（10分）
        matchScore = getStructureScore(structure, vehicleType, matchScore, matchReason);
        return matchScore;
    }

    /**
     * 获取算分
     * @param goods
     * @param cargoName
     * @param matchScore
     * @param matchReason
     * @return
     */
    private static int getGoodsScore(String goods, String cargoName, int matchScore, StringBuilder matchReason) {
        // 货物匹配判断（20分）
        if (StringUtils.isNotBlank(goods) && StringUtils.isNotBlank(cargoName)) {
            if (goods.contains(cargoName)) {
                matchScore += 20;
                if (matchReason.length() > 0) {
                    matchReason.append("，");
                }
                matchReason.append("货物匹配");
            }
        }
        return matchScore;
    }

    /**
     * 路线匹配得分
     * @param point
     * @param startPoint
     * @param endPoint
     * @param matchScore
     * @param matchReason
     * @return
     */
    private static int getMatchScore(String point, String startPoint, String endPoint, int matchScore, StringBuilder matchReason) {
        if (StringUtils.isNotBlank(point)) {
            // 将起点和终点拆分为可能的关键词
            String[] startKeywords = extractKeywords(startPoint);
            String[] endKeywords = extractKeywords(endPoint);
            boolean startMatched = false;
            boolean endMatched = false;
            // 检查起点匹配
            for (String startKeyword : startKeywords) {
                if (point.contains(startKeyword)) {
                    startMatched = true;
                    break;
                }
            }
            // 检查终点匹配
            for (String endKeyword : endKeywords) {
                if (point.contains(endKeyword)) {
                    endMatched = true;
                    break;
                }
            }
            // 计算匹配分数
            if (startMatched && endMatched) {
                // 起点和终点都匹配，得40分
                matchScore += 40;
                matchReason.append("起点和终点匹配");
                // 检查完整路线匹配，额外加10分
                String routeToMatch = startPoint + "-" + endPoint;
                String routeToMatchReverse = endPoint + "-" + startPoint;
                if (point.contains(routeToMatch) || point.contains(routeToMatchReverse) ||
                    point.contains(startPoint + "到" + endPoint) || point.contains(endPoint + "到" + startPoint)) {
                    matchScore += 10;
                    matchReason.append("(完整路线匹配)");
                }
            } else if (startMatched) {
                // 只有起点匹配，得20分
                matchScore += 20;
                matchReason.append("起点匹配");
            } else if (endMatched) {
                // 只有终点匹配，得20分
                matchScore += 20;
                matchReason.append("终点匹配");
            }
        }
        return matchScore;
    }

    /**
     * 从地点名称中提取关键词
     * @param location 地点名称
     * @return 关键词数组
     */
    private static String[] extractKeywords(String location) {
        if (StringUtils.isBlank(location)) {
            return new String[0];
        }
        // 移除常见的省市区后缀
        String cleaned = location.replaceAll("(省|市|区|县|自治区|自治州|特别行政区)$", "");
        // 分割地址，处理可能的多级地址
        List<String> keywords = new ArrayList<>();
        if (!cleaned.equals(location)) {
            keywords.add(cleaned);
        }
        keywords.add(location); // 添加完整地址作为关键词
        // 修改正则表达式，将多字符行政区划作为整体进行分割
        String[] parts = location.split("省|市|区|县|自治区|自治州|特别行政区");
        for (String part : parts) {
            if (StringUtils.isNotBlank(part)) {
                keywords.add(part.trim());
            }
        }
        return keywords.toArray(new String[0]);
    }

    /**
     * 解析向量分片信息
     * @param cleanInfo
     * @param driverKeyInfoList
     * @return
     */
    private static Map<String, String> getDriverKeyInfoList(String cleanInfo,List<Map<String, String>> driverKeyInfoList) {
        cleanInfo = cleanInfo.replace("\\n", "\n");
        if (cleanInfo.startsWith("司机数据开头基本信息")) {
            cleanInfo = cleanInfo.substring("司机数据开头基本信息".length());
        }
        if (cleanInfo.endsWith("司机数据结尾!")) {
            cleanInfo = cleanInfo.substring(0, cleanInfo.length() - "司机数据结尾!".length());
        }
        // 按换行符分割
        String[] lines = cleanInfo.split("\\n");
        Map<String, String> driverMap = new HashMap<>();
        // 标记当前处理的部分
        String currentSection = "基本信息";
        List<String> routes = new ArrayList<>();
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }
            // 判断当前处理的部分
            if (line.equals("车辆信息")) {
                currentSection = "车辆信息";
                continue;
            } else if (line.equals("常跑路线")) {
                currentSection = "常跑路线";
                continue;
            }
            if (line.contains(": ")) {
                String[] parts = line.split(": ", 2);
                if (parts.length == 2) {
                    String key = parts[0].trim();
                    String value = parts[1].trim();
                    if ("画像ID".equals(key) || "姓名".equals(key) || "常运货物".equals(key)) {
                        driverMap.put(key, value);
                    } else if (("车辆长度".equals(key) || "车辆结构".equals(key)) && "车辆信息".equals(currentSection)) {
                        driverMap.put(key, value);
                    }
                }
            } else if ("常跑路线".equals(currentSection) && !line.startsWith("运单数量") &&
                    !line.startsWith("平均里程") && !line.startsWith("路线总里程") &&
                    !line.startsWith("平均运费") && !line.startsWith("最近订单")) {
                // 提取路线名称（不包含详细信息）
                routes.add(line.trim());
            }
        }
        // 将常跑路线添加到司机信息中
        if (!routes.isEmpty()) {
            driverMap.put("常跑路线", String.join(",", routes));
        }
        // 只有当包含画像ID和姓名时才添加到列表
        if (driverMap.containsKey("画像ID") && driverMap.containsKey("姓名")) {
            driverKeyInfoList.add(driverMap);
        }
        return driverMap;
    }

    /**
     * 解析 RAG 模型返回的特定格式司机匹配字符串
     * @param result RAG 返回的原始字符串，格式如: [id:...,name:...,fraction:...,reason:...;...]
     * @return 解析后的 ModelDriverRepVo 对象
     */
    private ModelDriverRepVo parseDriverDataListRag(String result,List<ModelDriverRepVo.MatchedDriver> manualMatchedDrivers) {
        ModelDriverRepVo repVo = new ModelDriverRepVo();
        repVo.setAnalysisTime(System.currentTimeMillis()); // 设置分析时间戳
        if (StringUtils.isBlank(result) || StringUtils.equals(result,"[]")) {
            log.warn("数据不存在");
            repVo.setMatchedDrivers(manualMatchedDrivers);
            repVo.setTotalMatches(manualMatchedDrivers.size());
            return repVo;
        }
		List<ModelDriverRepVo.MatchedDriver> matchedDrivers = new ArrayList<>(manualMatchedDrivers);
        // 移除首尾可能存在的方括号
        result = result.trim();
        if (result.startsWith("[")) {
            result = result.substring(1);
        }
        if (result.endsWith("]")) {
            result = result.substring(0, result.length() - 1);
        }
        // 按分号分割每个司机记录
        String[] driverRecords = result.split(";");
        for (String record : driverRecords) {
            record = record.trim();
            if (record.isEmpty()) {
                continue;
            }
            ModelDriverRepVo.MatchedDriver driver = new ModelDriverRepVo.MatchedDriver();
            // 按逗号分割每个字段
            String[] pairs = record.split(",");
            boolean idFound = false;
            boolean nameFound = false;
            for (String pair : pairs) {
                // 按冒号分割键值对，限制为2部分以处理可能出现在原因中的冒号
                String[] kv = pair.split(":", 2);
                if (kv.length == 2) {
                    String key = kv[0].trim();
                    String value = kv[1].trim();
                    try {
                        switch (key) {
                            case "id":
                                driver.setDriverProfileId(Long.parseLong(value));
                                idFound = true;
                                break;
                            case "name":
                                driver.setDriverName(value);
                                nameFound = true;
                                break;
                            case "fraction":
                                driver.setMatchScore(Integer.parseInt(value));
                                break;
                            case "reason":
                                driver.setAiAnalysis(value);
                                break;
                            default:
                                log.warn("Unknown key '{}' in RAG result record: {}", key, record);
                                break;
                        }
                    } catch (NumberFormatException e) {
                        log.error("数据格式转换异常 '{}' 数据记录: {}", key, record, e);
                        // 如果 id 或 fraction 解析失败，可以选择跳过此记录或设置默认值
                        if ("id".equals(key) || "fraction".equals(key)) {
                           log.error("解析错误数据: {}", record);
                           driver = null; // 标记此记录无效
                           break; // 停止处理此记录的剩余字段
                        }
                    }
                } else {
                    log.warn("模型数据返回格式错误: {}", pair);
                }
            } // end for pairs
            // 只有当记录有效（未被标记为null）且包含必须的 id 和 name 时才添加
            if (driver != null && idFound && nameFound) {
                matchedDrivers.add(driver);
            } else if (driver != null) {
                 log.warn("跳过该数据部分字段缺失 id' or 'name': {}", record);
            }
        }
        // 对匹配到的司机进行去重处理（根据姓名和画像ID）
        handleRepDrivers(matchedDrivers);
        repVo.setMatchedDrivers(matchedDrivers);
        repVo.setTotalMatches(matchedDrivers.size());
        return repVo;
    }

    @Override
    public String analyzeDriverMatchRag(TaskPlanDO taskPlan, List<String> driverInfoList) throws NoApiKeyException, InputRequiredException {
        Generation gen = new Generation();
        String startPoint = taskPlan.getStartPoint();
        String endPoint = taskPlan.getEndPoint();
        StringBuilder userContent = new StringBuilder();
        userContent.append("【任务信息】\n");
        userContent.append("起点: ").append(startPoint).append("\n");
        userContent.append("终点: ").append(endPoint).append("\n");
        userContent.append("货物: ").append(taskPlan.getCargoName()).append("\n");
        if (taskPlan.getVehicleLength() != null) {
            userContent.append("车辆长度: ").append(taskPlan.getVehicleLength()).append("米\n");
        }else {
            userContent.append("车辆长度: ").append("未知\n");
        }
        // 构建系统提示词
        StringBuilder systemContent = new StringBuilder();
        systemContent.append("作为物流调度专家，请根据以下任务需求和司机信息分析司机匹配度，按以下规则返回结果：\n\n");
        // 添加匹配规则
        systemContent.append("【匹配规则】（优先级从高到低）：\n");
        systemContent.append("1. 常跑路线匹配度高的司机（运单数量多的优先）\n");
        systemContent.append("2. 常运货物匹配,车长匹配，车辆结构匹配的优先\n");
        systemContent.append("3. 常跑路线部分重叠的司机（按重合度排序）\n");
        // 添加返回格式要求
        systemContent.append("【返回格式要求】\n");
        systemContent.append("- 每个司机信息用逗号分隔\n");
        systemContent.append("- 必须包含字段：画像ID,司机姓名,匹配分数,匹配原因\n");
        systemContent.append("- 格式示例：id:12345,name:张三,fraction:85,reason:参考选择可以多个(路线匹配，车辆结构匹配，车长匹配，货物匹配，路线重叠，随机匹配);" +
                "id:67890,name:李四,fraction:72,reason:参考选择可以多个(路线匹配，车辆结构匹配，车长匹配，货物匹配，路线重叠，随机匹配)\n\n");
        // 添加匹配分数计算规则
        systemContent.append("【匹配分数计算规则】\n");
        systemContent.append("- 路线匹配度40%\n");
        systemContent.append("- 车长匹配度20%\n");
        systemContent.append("- 常运货物匹配度20%\n");
        systemContent.append("- 常跑路线线路重叠10%\n");
        systemContent.append("- 车辆结构匹配10%\n\n");
        // 添加特别说明
        systemContent.append("【特别说明】\n");
        systemContent.append("1. 匹配到的司机数据都需要返回");
        systemContent.append("2. 必须返回真实数据，不要编造\n");
        systemContent.append("3. 优先返回状态生效的司机\n");
        systemContent.append("4. 常跑路线匹配度高的司机排名靠前\n");
        systemContent.append("5. 相似路线的司机也要包含在内\n");
        systemContent.append("6. 画像ID和姓名必须准确无误\n");
        systemContent.append("7. 多个司机信息用分号(,)分隔\n");
        systemContent.append("8. 给出的匹配原因简短不要超过30字\n");
        // 在现有系统提示词中添加
        systemContent.append("【车长匹配特别说明-严格执行】\n");
        systemContent.append("- 当【任务信息】中车辆长度为未知时，禁止在reason中出现'车长匹配'字样\n");
        systemContent.append("- 当车辆长度未知时，必须在reason中明确标注'车长未知'\n");
        systemContent.append("- 即使有其他匹配项，也必须严格遵循车长匹配规则\n");
        systemContent.append("- 禁止出现'车长部分匹配'，只能是'车长匹配'、'车长不匹配'或'车长未知'\n");
        systemContent.append("- 对于已知车长的情况:\n");
        systemContent.append("  • 17米以上长车：相差>1米为不匹配\n");
        systemContent.append("  • 10-17米车：相差>0.7米为不匹配\n");
        systemContent.append("  • 10米以下车：相差>0.5米为不匹配\n");
        // 添加知识库检索到的司机信息
        systemContent.append("【司机数据】\n");
        if (driverInfoList != null && !driverInfoList.isEmpty()) {
			for (String s : driverInfoList) {
				systemContent.append(s).append("\n");
			}
        } else {
            systemContent.append("未找到相关司机数据\n");
        }
        log.info("analyzeDriverMatchRag,systemContent:{}",systemContent);
        log.info("analyzeDriverMatchRag,userContent:{}",userContent);
        // 获取当前时间作为基准
        Message systemMsg = Message.builder()
                .role(Role.SYSTEM.getValue())
                .content(systemContent.toString())
                .build();
        Message userMsg = Message.builder()
                .role(Role.USER.getValue())
                .content(userContent + "\n系统提示词中输入的【司机数据】，都必须在结果中输出，并按照系统提示词中指定的结构返回,返回司机数据用[]包围，如果系统提示词中司机数据为空则则直接返回[]，请按照系统提示词中要求正确返回结果"
                        + "\n请特别注意：【任务信息】中出现 '车辆长度: 未知'，因此所有司机的匹配原因中不得出现'车长匹配'字样，必须标注为'车长未知'，没有出现则按照'车长匹配特别说明'匹配，请严格执行！")
                .build();
        GenerationParam param = GenerationParam.builder()
                .apiKey(aiModelBaiLianConfig.getApiKey())
                .model("qwen-max-latest")
                .messages(Arrays.asList(systemMsg, userMsg))
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .temperature(0.1F)
                .build();
        GenerationParam.builder().responseFormat(ResponseFormat.from(ResponseFormat.JSON_OBJECT));
        return gen.call(param).getOutput().getChoices().get(0).getMessage().getContent();
    }

    @NotNull
    private List<TaskDriverMatchDO> getTaskDriverMatchDOS(Long taskPlanId, ModelDriverRepVo modelDriverRepVo, List<String> existingDriver, List<ModelDriverRepVo.MatchedDriver> newMatchedDriverList) {
        List<TaskDriverMatchDO> taskDriverMatchList = new ArrayList<>();
        // 创建一个集合存储已经查询到的司机画像ID
        SchedulerBlacklist schedulerBlacklist = new SchedulerBlacklist();
        schedulerBlacklist.setState(0);
        List<SchedulerBlacklist> list = schedulerBlacklistService.selectSchedulerBlacklistList(schedulerBlacklist);
        for (ModelDriverRepVo.MatchedDriver driver : modelDriverRepVo.getMatchedDrivers()) {
            if (StringUtils.isBlank(driver.getDriverName())) {
                log.info("司机[{}]信息为空", driver.getDriverName());
                continue;
            }
            // 如果司机电话已存在于已匹配列表中，则跳过
            if (StringUtils.isNotBlank(driver.getDriverName())
                    && existingDriver.contains(driver.getDriverName())) {
                log.info("司机[{}]已存在匹配记录，跳过新增", driver.getDriverName());
                continue;
            }
            TaskDriverMatchDO taskDriverMatch = new TaskDriverMatchDO();
            taskDriverMatch.setTaskPlanId(taskPlanId);
            // 查询司机画像ID
            DriverProfileDO driverProfileDO = driverProfileMapper.selectById(driver.getDriverProfileId());
            if (driverProfileDO == null){
                continue;
            }
            Long driverProfileId = queryDriverProfileId(driver.getDriverProfileId(),driver.getDriverName(),list,driverProfileDO);
            // 如果找到了司机画像ID，并且不在已找到的集合中，则添加到集合
            if (driverProfileId != null) {
                taskDriverMatch.setDriverProfileId(driverProfileId);
            }else {
                log.info("司机[{}]未在画像库中，跳过新增", driver.getDriverName());
                continue;
            }
            newMatchedDriverList.add(driver);
            taskDriverMatch.setDriverName(driver.getDriverName());
            taskDriverMatch.setIdentityCard(driverProfileDO.getIdentityCard());
            taskDriverMatch.setTelephone(driverProfileDO.getSchedulerTelephone());
            taskDriverMatch.setMatchScore(driver.getMatchScore());
            taskDriverMatch.setAiAnalysis(driver.getAiAnalysis());
            taskDriverMatch.setDealStatus(0); // 默认未成交
            taskDriverMatch.setIsSendSms(0);
            taskDriverMatch.setIsConfirmSms(0);
            taskDriverMatch.setIsOutbound(1);
            taskDriverMatch.setHasIntention(0);
            taskDriverMatchList.add(taskDriverMatch);
        }

        //查询scheduler_task_driver_match表根据 电话号码 查询判断当天是否被状态是已呼叫的数据 已呼叫 设置为9
        if (CollectionUtil.isNotEmpty(taskDriverMatchList)) {
            // 获取当前日期的开始时间（00:00:00）
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date todayStart = calendar.getTime();
            // 获取当前日期的结束时间（23:59:59）
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date todayEnd = calendar.getTime();
            // 收集所有司机电话号码
            List<String> driverPhones = taskDriverMatchList.stream()
                    .map(TaskDriverMatchDO::getTelephone)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(driverPhones)) {
                // 查询今天已经被呼叫过的司机
                List<TaskDriverMatchDO> calledDrivers = taskDriverMatchService.selectCalledDriversByPhones(
                        driverPhones, todayStart, todayEnd);
                // 创建已呼叫司机电话号码的集合，用于快速查找
                Set<String> calledPhones = calledDrivers.stream()
                        .map(TaskDriverMatchDO::getTelephone)
                        .collect(Collectors.toSet());
                // 标记已呼叫的司机状态为9
                for (TaskDriverMatchDO driverMatch : taskDriverMatchList) {
                    if (StringUtils.isNotBlank(driverMatch.getTelephone()) &&
                            calledPhones.contains(driverMatch.getTelephone())) {
                        driverMatch.setIsOutbound(9); // 设置为已呼叫状态
                        log.info("司机[{}]今天已被呼叫过，设置状态为9", driverMatch.getDriverName());
                    }
                }
            }
        }
        return taskDriverMatchList;
    }

    private void updateTaskPlanStatus(Long taskPlanId, Long id,Integer status) {
        try {
            // 更新任务计划状态为调度中
            TaskPlanUpdateReqVO updateReqVO = new TaskPlanUpdateReqVO();
            updateReqVO.setId(id);
            updateReqVO.setStatus(status);
            boolean updated = taskPlanService.updateTaskPlan(updateReqVO);
            if (updated) {
                log.info("任务计划[{}]状态已更新为调度中", taskPlanId);
            } else {
                log.warn("任务计划[{}]状态更新失败", taskPlanId);
            }
        } catch (Exception e) {
            log.error("更新任务计划状态失败", e);
        }
    }

    /**
     * 根据司机姓名、身份证和电话查询司机画像表获取司机画像ID
     *
     * @param driverName 司机姓名
     * @return 司机画像ID，如果未找到则返回null
     */
    private Long queryDriverProfileId(Long driverProfileId,String driverName,List<SchedulerBlacklist> list,DriverProfileDO driverProfileDO) {
        try {
            // 如果电话号码查询不到，则使用姓名和电话组合查询
            if (StringUtils.isNotBlank(driverName)) {
                //手机号在黑名单列表中直接返回
                if (CollectionUtil.isNotEmpty(list)){
                    List<String> numberList =
                            list.stream().map(SchedulerBlacklist::getBlackMember).collect(Collectors.toList());
                    String schedulerTelephone = driverProfileDO.getSchedulerTelephone();
                    if (numberList.contains(schedulerTelephone)){
                        return null;
                    }
                }
                // 手机号为空过滤掉
                String schedulerTelephone = driverProfileDO.getSchedulerTelephone();
                if (StringUtils.isBlank(schedulerTelephone)){
                    return null;
                }
                // 设置黑名单司机过滤掉
                Integer blacklistStatus = driverProfileDO.getBlacklistStatus();
                if (blacklistStatus != null && blacklistStatus == 1){
                    return null;
                }
                String driverName1 = driverProfileDO.getDriverName();
                if (StringUtils.equals(driverName,driverName1)){
                    return driverProfileId;
                }
            }
            return null; // 如果查询不到，返回null
        } catch (Exception e) {
            log.error("查询司机画像ID失败", e);
            return null;
        }
    }

    public String analyzeDriverMatch(TaskPlanDO request) {
        try {
            // 构建专业提示词模板
            String formattedPrompt = getFormattedPrompt(request);
            ApplicationParam param = ApplicationParam.builder()
                    .apiKey(aiModelBaiLianConfig.getApiKey())
                    .appId(aiModelBaiLianConfig.getAppId())
                    .prompt(formattedPrompt)
                    .build();
            Application application = new Application();
            ApplicationResult result = application.call(param);
            return result.getOutput().getText();
        } catch (NoApiKeyException | InputRequiredException e) {
            log.error("百炼API调用失败: {}", e.getMessage());
            throw exception(BASE_SERVER_ERROR, "大模型调用失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("司机匹配分析异常", e);
            throw exception(BASE_SERVER_ERROR, "分析过程发生错误: " + e.getMessage());
        }
    }

    private String getFormattedPrompt(TaskPlanDO request) {
        // 确保所有参数都有值，避免空指针异常
        String startPoint = request.getStartPoint() != null ? request.getStartPoint() : "";
        String endPoint = request.getEndPoint() != null ? request.getEndPoint() : "";
        String cargoName = request.getCargoName() != null ? request.getCargoName() : "";
        String vehicleLength = request.getVehicleLength() != null ? String.valueOf(request.getVehicleLength()) : "";
        // 使用不含格式化占位符的字符串拼接方式
        String routeInfo = startPoint + "-" + endPoint;
        Integer vehicleCount = request.getVehicleCount() * 10;
        // 构建提示词模板，注意所有百分号都使用%%转义
        String promptTemplate = "作为物流调度专家，请根据以下任务需求分析司机匹配度，按以下规则返回结果：\n"
                + "运输路线：%s\n"
                + "货物名称：%s\n"
                + "车辆长度：%s米\n"
                + "匹配规则（满足任意条件即返回）：\n"
                + "1. 司机状态是生效的（优先级高）\n"
                + "2. 常跑路线匹配度高的司机，运单数量多的（排名高）\n"
                + "3. 常跑路线部分重叠的司机（按重合度排序）\n"
                + "4. 近期有相似运输经验的司机（按经验值排序）\n\n"
                + "返回字段要求（JSON数组）：\n"
                + "- driverProfileId: 知识库中的司机画像ID（必填）\n"
                + "- driverId: 知识库中的司机ID（必填）\n"
                + "- driverName: 司机姓名\n"
                + "- identityCard: 身份证号\n"
                + "- telephone: 联系电话\n"
                + "- matchScore: 匹配度（计算规则：路线匹配度40%% + 司机状态生效20%% + 车辆匹配度10%% + 常跑路线线路重叠的20%% + 近期有相似运输经验的司机10%%）\n"
                + "- aiAnalysis: 分析包含（基础信息/路线匹配情况/车辆适配度/历史表现分析等）输出字符串即可\n\n"
                + "特别说明：\n"
                + "1. 匹配不少于%s位司机,如果没有合适也不需要输出脏数据按照实际情况返回\n"
                + "1. 只要常跑路线和重叠路线的司机全都匹配出来\n"
                + "2. 详细分析过程可以不在结果中输出，优先返回JSON数据即可，按照分数从高到底排序\n"
                + "3. 最多返回50条记录\n"
                + "4. 按匹配度降序排列\n"
                + "5. 数据必须来自知识库实时数据\n";
        return String.format(promptTemplate, routeInfo, cargoName, vehicleLength,vehicleCount);
    }

    /**
     * 调用通义千问API
     */
    private GenerationResult callQianWenApi(String content) throws ApiException, NoApiKeyException, InputRequiredException {
        Generation gen = new Generation();
        // 获取当前时间作为基准
        String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        Message systemMsg = Message.builder()
                .role(Role.SYSTEM.getValue())
                .content("你是一个专业的物流文本解析助手。请从用户提供的物流调度文本中提取结构化信息，并以JSON格式返回。"
                        + "需要提取的字段包括：提货时间(pickupDate)、车型(vehicleType)、车牌(licensePlate)、货物名称(cargoName)、"
                        + "重量值(weight)、重量单位(weightType)、司机姓名(driverName)、司机电话(driverPhone)、"
                        + "司机身份证(driverIdCard)、运输数量(transportationQuantity)、目的地点(destination)、车辆数量(vehicleNumberStr)、"
                        + "托盘长(palletLength)、托盘宽(palletWidth)、到达时间(arrivalTime)、线路起始地(routeFrom)、"
                        + "线路目的地(routeTo)、司机银行账号(driverBankAccount)、车辆长度(vehicleLength)、车辆宽度(vehicleWidth)、车辆高度(vehicleHeight)、长宽高的计量单位(vehicleMeasurement)"
                        + "寄件地址(senderAddressRaw)、收件方(receiverPhone)、注意项(noteField)、备注(remark)、"
                        + "细分类别如品牌信息(segmentationType)、白天卸货标识(daytimeUnload)、运费明细只要输出数值比如2000(freightDetail)、"
                        + "装货要求(cargoRequirement)"
                        + "文本中只有一段物流任务，请解析并返回单个对象。"
                        + "对于无法确定的字段，请设置为null。"
                        + "对于返回的字段中时间相关的字段特殊处理，车辆数量(vehicleNumberStr)能解析到具体数量就以具体数量为准，解析不到则默认车辆数为null,"
                        + "提货时间(pickupDate)，到达时间(arrivalTime)等，如果出现明天后天等词请以当前系统时间为基准进行计算当前时间"+currentTime+"返回指定日期格式yyyy-MM-dd HH:mm:ss'"
                        + "示例：若文本说'明天上午9点'且当前时间是2024-03-25 14:00:00，则应返回2024-03-26 09:00:00"
                        + "只返回JSON格式的解析结果，不要添加额外解释。")
                .build();
        Message userMsg = Message.builder()
                .role(Role.USER.getValue())
                .content(content)
                .build();
        GenerationParam param = GenerationParam.builder()
                .apiKey(aiModelBaiLianConfig.getApiKey())
                .model(aiModelBaiLianConfig.getModel())
                .messages(Arrays.asList(systemMsg, userMsg))
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .build();
        GenerationParam.builder().responseFormat(ResponseFormat.from(ResponseFormat.JSON_OBJECT));
        return gen.call(param);
    }

    /**
     * 从可能包含其他文本的内容中提取JSON部分
     */
    private String extractJsonContent(String content) {
        // 首先尝试查找JSON数组的开始和结束位置
        int arrayStartIndex = content.indexOf('[');
        int arrayEndIndex = content.lastIndexOf(']') + 1;
        // 然后尝试查找JSON对象的开始和结束位置
        int objectStartIndex = content.indexOf('{');
        int objectEndIndex = content.lastIndexOf('}') + 1;
        // 判断是否找到了JSON数组
        if (arrayStartIndex >= 0 && arrayEndIndex > arrayStartIndex) {
            return content.substring(arrayStartIndex, arrayEndIndex);
        }
        // 判断是否找到了JSON对象
        if (objectStartIndex >= 0 && objectEndIndex > objectStartIndex) {
            return content.substring(objectStartIndex, objectEndIndex);
        }
        // 如果没找到JSON格式，返回原内容
        return content;
    }

    /**
     * 解析司机匹配数据JSON字符串到ModelDriverRepVo对象
     */
    private ModelDriverRepVo parseDriverDataList(String result) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 配置忽略未知属性
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            // 判断是否为数组格式
            if (result.trim().startsWith("[")) {
                // 如果是数组格式，先解析为MatchedDriver列表
                List<ModelDriverRepVo.MatchedDriver> drivers = mapper.readValue(result, 
                        new TypeReference<List<ModelDriverRepVo.MatchedDriver>>() {});
                // 创建ModelDriverRepVo对象并设置匹配到的司机列表
                ModelDriverRepVo repVo = new ModelDriverRepVo();
                repVo.setMatchedDrivers(drivers);
                // 设置分析时间
                repVo.setAnalysisTime(System.currentTimeMillis());
                return repVo;
            } else {
                // 如果是对象格式，直接解析为ModelDriverRepVo
                return mapper.readValue(result, ModelDriverRepVo.class);
            }
        } catch (Exception e) {
            log.error("解析司机匹配数据JSON失败: {}", e.getMessage(), e);
            // 解析失败时返回空对象而不是null
            return new ModelDriverRepVo();
        }
    }

    /**
     * 解析JSON字符串到LogisticsDataDTO对象集合
     */
    private ModelDataRepVo parseJsonToLogisticsDataList(String jsonContent) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            // 配置忽略未知属性
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            // 判断是否为对象格式
            if (jsonContent.trim().startsWith("{")) {
                // 如果是对象格式，直接解析为ModelDataRepVo
                return mapper.readValue(jsonContent, ModelDataRepVo.class);
            } else {
                // 从JSON字符串解析为LogisticsDataDTO对象列表
                return mapper.readValue(jsonContent, new TypeReference<ModelDataRepVo>() {});
            }
        } catch (Exception e) {
            log.error("解析JSON失败: {}", e.getMessage(), e);
            return new ModelDataRepVo();
        }
    }
}