package com.zly.project.model.service;


import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.zly.project.model.controller.vo.ModelDataRepVo;
import com.zly.project.model.controller.vo.ModelDriverRepVo;
import com.zly.project.plan.domain.TaskPlanDO;

import java.util.List;

/**
 * 物流文本解析 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelParseService {

    /**
     * 解析物流文本
     *
     * @param content 物流文本内容
     * @return 解析后的物流数据列表
     */
    ModelDataRepVo parseLogisticsText(String content);

    /**
     * 调用大模型匹配意向司机
     * @param taskPlanId 计划ID
     * @return ModelDriverRepVo
     */
    ModelDriverRepVo portraitDriver(Long taskPlanId);

    /**
     * 模型匹配司机
     *
     * @return
     * @throws NoApiKeyException
     * @throws InputRequiredException
     */
    String analyzeDriverMatchRag(TaskPlanDO taskPlanDO, List<String> driverInfoList) throws NoApiKeyException, InputRequiredException;
}