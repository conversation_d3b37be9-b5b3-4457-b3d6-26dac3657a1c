package com.zly.project.plan.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 任务调度计划 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskPlanDO extends BaseEntity {

    /**
     * 任务计划编号
     */
    private Long id;

    /**
     * 每日货源ID
     */
    private Long dailyCargoId;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 起始地点
     */
    private String startPoint;

    /**
     * 目的地点
     */
    private String endPoint;

    /** 包装方式 */
    private String packagingMethod;

    /** 货物数量(吨) */
    private BigDecimal cargoWeight;

    /** 货物数量(方) */
    private BigDecimal cargoVolume;

    /** 装货省编码 */
    private String loadingProvinceCode;

    /** 装货省名称 */
    private String loadingProvinceName;

    /** 装货市编码 */
    private String loadingCityCode;

    /** 装货市名称 */
    private String loadingCityName;

    /** 装货区编码 */
    private String loadingDistrictCode;

    /** 装货区名称 */
    private String loadingDistrictName;

    /** 装货详细地址 */
    private String loadingAddress;

    /** 装货地址经度 */
    private BigDecimal loadingLongitude;

    /** 装货地址纬度 */
    private BigDecimal loadingLatitude;

    /** 卸货省编码 */
    private String unloadingProvinceCode;

    /** 卸货省名称 */
    private String unloadingProvinceName;

    /** 卸货市编码 */
    private String unloadingCityCode;

    /** 卸货市名称 */
    private String unloadingCityName;

    /** 卸货区编码 */
    private String unloadingDistrictCode;

    /** 卸货区名称 */
    private String unloadingDistrictName;

    /** 卸货详细地址 */
    private String unloadingAddress;

    /** 卸货地址经度 */
    private BigDecimal unloadingLongitude;

    /** 卸货地址纬度 */
    private BigDecimal unloadingLatitude;

    /**
     * 货物名称
     */
    private String cargoName;

    /**
     * 需要车辆类型
     */
    private String vehicleType;

    /**
     * 需要车辆长度，单位米
     */
    private Double vehicleLength;

    /**
     * 需要车辆数量
     */
    private Integer vehicleCount;

    /**
     * 调度发起方
     */
    private String initiator;

    /**
     * 超时状态 0未超时 1:即将超时 2:已超时
     */
    private Integer timeoutStatus;

    /**
     * 意向司机数量
     */
    private Integer interestedDriverCount;

    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime planEndTime;

    /**
     * 预计费用，单位元
     */
    private BigDecimal estimatedCost;

    /**
     * 特殊要求
     */
    private String requirements;

    /**
     * 状态1未调度、2调度中、3已完成、4已停止、5已失败、6已作废)
     */
    private Integer status;

    /**
     * 调度员姓名
     */
    private String dispatcherName;

    /**
     * 调度联系电话
     */
    private String dispatcherPhone;

    /**
     * 沟通方式(1:短信，2：外呼)
     */
    private Integer communicationMethod;

    /**
     * 是否手动暂停 0:是暂停1：开始  页面手动暂停 & 匹配到足够的意向司机后手动暂停 暂停后需要手动开启
     */
    private Integer isManualPause;

    /**
     * 是否自动暂停 0:是暂停1：开始 只处理每天9点开启 5:30结束调度任务 每天自动暂停 自动开启
     */
    private Integer isAutoPause;

    /**
     * 会话类型 电议 一口价
     */
    private Integer sessionType;

    /**
     * 任务会话ID
     */
    private Integer sessionTaskId;

    /**
     * 任务ID
     */
    private String outTaskId;

    /**
     * 外呼任务状态 1占用 2释放
     */
    private Integer taskState;

    /**
     * 完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /**
     * 作废日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
}