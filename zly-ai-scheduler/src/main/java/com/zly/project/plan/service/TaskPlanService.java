package com.zly.project.plan.service;


import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.plan.controller.vo.TaskPlanCreateReqVO;
import com.zly.project.plan.controller.vo.TaskPlanPageReqVO;
import com.zly.project.plan.controller.vo.TaskPlanUpdateReqVO;
import com.zly.project.plan.domain.TaskPlanDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 任务调度计划 Service 接口
 *
 * <AUTHOR>
 */
public interface TaskPlanService {

    /**
     * 创建任务调度计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTaskPlan(@Valid TaskPlanCreateReqVO createReqVO);

    /**
     * 更新任务调度计划
     *
     * @param updateReqVO 更新信息
     */
    Boolean updateTaskPlan(TaskPlanUpdateReqVO updateReqVO);

    /**
     * 删除任务调度计划
     *
     * @param id 编号
     */
    void deleteTaskPlan(Long id);

    /**
     * 获得任务调度计划
     *
     * @param id 编号
     * @return 任务调度计划
     */
    TaskPlanDO getTaskPlan(Long id);

    /**
     * 获得任务调度计划列表
     *
     * @param ids 编号
     * @return 任务调度计划列表
     */
    List<TaskPlanDO> getTaskPlanList(Collection<Long> ids);

    /**
     * 获得任务调度计划分页
     *
     * @param pageReqVO 分页查询
     * @return 任务调度计划分页
     */
    List<TaskPlanDO> getTaskPlanPage(TaskPlanPageReqVO pageReqVO);

    /**
     * 获得指定状态的任务调度计划列表
     *
     * @param status 状态
     * @return 任务调度计划列表
     */
    List<TaskPlanDO> getTaskPlanListByStatus(Integer status);

    /**
     * 根据条件查询计划
     * @param pageReqVO
     * @return
     */
    List<TaskPlanDO> getTaskPlansByCondition(TaskPlanPageReqVO pageReqVO);

    /**
     * 根据任务状态和外呼任务ID查询调度计划
     * @param uuidStr
     * @param status
     * @return
     */
    List<TaskPlanDO> getTaskPlanByOutTaskIdAndStatus(String uuidStr,Integer status);
}