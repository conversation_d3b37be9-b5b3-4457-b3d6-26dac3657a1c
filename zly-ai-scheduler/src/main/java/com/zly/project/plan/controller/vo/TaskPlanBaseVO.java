package com.zly.project.plan.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 任务调度计划 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@ApiModel("调度计划基础 vo")
@Data
public class TaskPlanBaseVO {

    @ApiModelProperty(value = "计划名称",  example = "上海到北京运输计划")
    private String planName;

    @ApiModelProperty(value = "起始地点",  example = "上海市浦东新区")
    @NotEmpty(message = "起始地点不能为空")
    private String startPoint;

    @ApiModelProperty(value = "目的地点",  example = "北京市朝阳区")
    @NotEmpty(message = "目的地点不能为空")
    private String endPoint;

    @ApiModelProperty(value = "货物名称",  example = "钢铁")
    @NotEmpty(message = "货物名称不能为空")
    private String cargoName;

    @ApiModelProperty(value = "包装方式", example = "散装")
    private String packagingMethod;

    @ApiModelProperty(value = "货物数量(吨)", example = "30.00")
    private BigDecimal cargoWeight;

    @ApiModelProperty(value = "货物数量(方)", example = "25.00")
    private BigDecimal cargoVolume;

    @ApiModelProperty(value = "装货省编码", example = "310000")
    private String loadingProvinceCode;

    @ApiModelProperty(value = "装货省名称", example = "上海市")
    private String loadingProvinceName;

    @ApiModelProperty(value = "装货市编码", example = "310100")
    private String loadingCityCode;

    @ApiModelProperty(value = "装货市名称", example = "上海市")
    private String loadingCityName;

    @ApiModelProperty(value = "装货区编码", example = "310115")
    private String loadingDistrictCode;

    @ApiModelProperty(value = "装货区名称", example = "浦东新区")
    private String loadingDistrictName;

    @ApiModelProperty(value = "装货详细地址", example = "张江高科技园区")
    private String loadingAddress;

    @ApiModelProperty(value = "装货地址经度", example = "121.585721")
    private BigDecimal loadingLongitude;

    @ApiModelProperty(value = "装货地址纬度", example = "31.201429")
    private BigDecimal loadingLatitude;

    @ApiModelProperty(value = "卸货省编码", example = "110000")
    private String unloadingProvinceCode;

    @ApiModelProperty(value = "卸货省名称", example = "北京市")
    private String unloadingProvinceName;

    @ApiModelProperty(value = "卸货市编码", example = "110100")
    private String unloadingCityCode;

    @ApiModelProperty(value = "卸货市名称", example = "北京市")
    private String unloadingCityName;

    @ApiModelProperty(value = "卸货区编码", example = "110105")
    private String unloadingDistrictCode;

    @ApiModelProperty(value = "卸货区名称", example = "朝阳区")
    private String unloadingDistrictName;

    @ApiModelProperty(value = "卸货详细地址", example = "国贸中心")
    private String unloadingAddress;

    @ApiModelProperty(value = "卸货地址经度", example = "116.466861")
    private BigDecimal unloadingLongitude;

    @ApiModelProperty(value = "卸货地址纬度", example = "39.914566")
    private BigDecimal unloadingLatitude;

    @ApiModelProperty(value = "需要车辆类型",  example = "平板车")
    @NotEmpty(message = "需要车辆类型不能为空")
    private String vehicleType;

    @ApiModelProperty(value = "需要车辆长度，单位米",  example = "19.7")
    @NotNull(message = "需要车辆长度不能为空")
    private Double vehicleLength;

    @ApiModelProperty(value = "需要车辆数量",  example = "19.7")
    @NotNull(message = "需要车辆数量不能为空")
    private Integer vehicleCount;

    @ApiModelProperty(value = "调度发起方",  example = "测试")
    private String initiator;

    @ApiModelProperty(value = "超时状态1:未超时 2:即将超时 3：已超时",  example = "测试")
    private Integer timeoutStatus;

    @ApiModelProperty(value = "意向司机数量",  example = "测试")
    private Integer interestedDriverCount;

    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "计划开始时间不能为空")
    private LocalDateTime planStartTime;

    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planEndTime;

    @ApiModelProperty(value = "预计费用，单位元", example = "12000")
    private BigDecimal estimatedCost;

    @ApiModelProperty(value = "特殊要求", example = "车辆要带斗篷，司机要有运输经验")
    private String requirements;

    @ApiModelProperty(value = "调度员姓名")
    private String dispatcherName;

    @ApiModelProperty(value = "调度联系电话")
    private String dispatcherPhone;

    @ApiModelProperty(value = "沟通方式(1:短信，2：短信+外呼)")
    private Integer communicationMethod;

    /**
     * 是否手动暂停 0:是暂停1：开始  页面手动暂停 & 匹配到足够的意向司机后手动暂停 暂停后需要手动开启
     */
    @ApiModelProperty(value = "是否手动暂停 0:是暂停1：开始  ")
    private Integer isManualPause;

    /**
     * 是否自动暂停 0:是暂停1：开始 只处理每天9点开启 5:30结束调度任务 每天自动暂停 自动开启
     */
    @ApiModelProperty(value = "是否自动暂停 0:是暂停1：开始")
    private Integer isAutoPause;

    @ApiModelProperty(value = "会话类型 电议 一口价",  example = "上海到北京运输计划")
    @NotNull(message = "会话类型不能为空")
    private Integer sessionType;

    @ApiModelProperty(value = "任务会话ID")
    private Integer sessionTaskId;

    @ApiModelProperty(value = "任务ID")
    private String outTaskId;

    @ApiModelProperty(value = "外呼任务状态 1占用 2释放")
    private Integer taskState;

    @ApiModelProperty(value = "完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    @ApiModelProperty(value = "作废日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    @ApiModelProperty(value = "备注")
    private String remark;


}