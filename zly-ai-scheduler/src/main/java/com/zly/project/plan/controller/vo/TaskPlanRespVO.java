package com.zly.project.plan.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 任务调度计划 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskPlanRespVO extends TaskPlanBaseVO {

    @ApiModelProperty(name = "任务计划编号",  example = "1024")
    private Long id;

    @ApiModelProperty(name = "每日货源ID",  example = "1024")
    private Long dailyCargoId;

    @ApiModelProperty(name = "状态",  example = "1")
    private Integer status;

    @ApiModelProperty(name = "创建人姓名",  example = "1")
    private String createBy;

    @ApiModelProperty(name = "创建人姓名",  example = "1")
    private String createByName;

    @ApiModelProperty(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;
}