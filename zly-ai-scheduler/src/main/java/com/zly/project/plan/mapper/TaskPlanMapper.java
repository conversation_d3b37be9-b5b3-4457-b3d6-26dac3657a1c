package com.zly.project.plan.mapper;


import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.plan.controller.vo.TaskPlanPageReqVO;
import com.zly.project.plan.domain.TaskPlanDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 任务调度计划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskPlanMapper {

    List<TaskPlanDO> selectTaskPlanList(TaskPlanPageReqVO reqVO);

    List<TaskPlanDO> selectListByStatus(Integer status);

    int insertTaskPlan(TaskPlanDO taskPlan);

    TaskPlanDO selectTaskPlanById(Long id);

    List<TaskPlanDO> selectTasksBatchIds(Collection<Long> ids);

    int deleteTaskPlanById(Long id);

    int updateTaskPlanById(TaskPlanDO updateObj);

    /**
     * 根据条件查询符合自动外呼条件的任务计划列表
     * 
     * @param queryVO 查询条件
     * @return 符合条件的任务计划列表
     */
    List<TaskPlanDO> selectTaskPlansByCondition(TaskPlanPageReqVO queryVO);

    /**
     * 根据 外呼任务ID 和状态查询计划集合
     * @param outTaskId
     * @param status
     * @return
     */
	List<TaskPlanDO> getTaskPlanByOutTaskIdAndStatus(String outTaskId, Integer status);
}