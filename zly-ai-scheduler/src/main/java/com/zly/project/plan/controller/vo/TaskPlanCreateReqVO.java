package com.zly.project.plan.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 任务调度计划创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskPlanCreateReqVO extends TaskPlanBaseVO {

	@ApiModelProperty(value = "调度信息文本", example = "常州到江苏南京的餐巾纸，需要 9 米 6 的平板车，明天上午 9 点装货，单车计划运价 2000 元")
	private String planContent;

	@ApiModelProperty("每日货源ID")
	private Long dailyCargoId;

}