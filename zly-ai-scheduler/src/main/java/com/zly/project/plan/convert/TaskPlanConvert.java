package com.zly.project.plan.convert;

import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.plan.controller.vo.TaskPlanCreateReqVO;
import com.zly.project.plan.controller.vo.TaskPlanRespVO;
import com.zly.project.plan.controller.vo.TaskPlanUpdateReqVO;
import com.zly.project.plan.domain.TaskPlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 任务调度计划 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskPlanConvert {

    TaskPlanConvert INSTANCE = Mappers.getMapper(TaskPlanConvert.class);

    TaskPlanDO convert(TaskPlanCreateReqVO bean);

    TaskPlanDO convert(TaskPlanUpdateReqVO bean);

    TaskPlanRespVO convert(TaskPlanDO bean);

    TaskPlanUpdateReqVO convertUpdateReqVO(TaskPlanDO bean);

    List<TaskPlanRespVO> convertList(List<TaskPlanDO> list);

    TableDataInfo<TaskPlanRespVO> convertPage(TableDataInfo<TaskPlanDO> page);

}