package com.zly.project.plan.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台 - 任务调度计划更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskPlanUpdateReqVO extends TaskPlanBaseVO {

    @ApiModelProperty(name = "任务计划编号")
    @NotNull(message = "任务计划编号不能为空")
    private Long id;

    @ApiModelProperty(name = "状态", example = "1")
    private Integer status;
}