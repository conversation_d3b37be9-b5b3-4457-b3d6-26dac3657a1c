package com.zly.project.plan.controller.vo;

import com.zly.framework.web.page.PageDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.zly.common.utils.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel(description = "管理后台 - 任务调度计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskPlanPageReqVO extends PageDomain {

    @ApiModelProperty(value = "计划名称", example = "上海到北京运输计划")
    private String planName;

    @ApiModelProperty(value = "状态", example = "1")
    private Integer status;

    @ApiModelProperty(value = "沟通方式(1:短信，2：短信+外呼)", example = "1")
    private Integer communicationMethod;

    @ApiModelProperty(value = "是否手动暂停0:是暂停1：开始", example = "1")
    private Integer isManualPause;

    @ApiModelProperty(value = "是否自动暂停0:是暂停1：开始", example = "1")
    private Integer isAutoPause;

    @ApiModelProperty(value = "超时状态0未超时 1:即将超时 2:已超时", example = "1")
    List<Integer> timeoutStatus;

    @ApiModelProperty(value = "计划开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] planStartTime;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}