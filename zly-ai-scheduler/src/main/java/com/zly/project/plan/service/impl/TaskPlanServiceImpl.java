package com.zly.project.plan.service.impl;


import com.zly.common.utils.PageUtils;
import com.zly.common.utils.TextUtil;
import com.zly.enums.plan.TaskPlanStatusEnum;
import com.zly.project.cargo.service.IDailyCargoService;
import com.zly.project.plan.controller.vo.TaskPlanCreateReqVO;
import com.zly.project.plan.controller.vo.TaskPlanPageReqVO;
import com.zly.project.plan.controller.vo.TaskPlanUpdateReqVO;
import com.zly.project.plan.convert.TaskPlanConvert;
import com.zly.project.plan.domain.TaskPlanDO;
import com.zly.project.plan.mapper.TaskPlanMapper;
import com.zly.project.plan.service.TaskPlanService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.*;
import static com.zly.project.match.service.impl.TaskDriverMatchServiceImpl.isAfter530;
import static com.zly.project.match.service.impl.TaskDriverMatchServiceImpl.isStartTomorrow;

/**
 * 任务调度计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TaskPlanServiceImpl implements TaskPlanService {

    @Resource
    private TaskPlanMapper taskPlanMapper;

    @Resource
    private IDailyCargoService dailyCargoService;

    @Override
    @Transactional
    public Long createTaskPlan(TaskPlanCreateReqVO createReqVO) {
        LocalDateTime planStartTime = createReqVO.getPlanStartTime();
        LocalDateTime now = LocalDateTime.now();
        boolean after;
        // 判断是否带小时（小时不为0表示带小时）
        if (planStartTime.getHour() != 0) {
            // 带小时的情况，直接比较完整时间
            after = now.isAfter(planStartTime);
        } else {
            // 不带小时的情况，只比较日期部分
            // 如果是同一天，则允许创建
            after = now.toLocalDate().isAfter(planStartTime.toLocalDate());
        }
        if (after) {
            throw exception(BASE_SERVER_ERROR, "调度任务计划已经超时,请确定计划开始日期");
        }
        LocalDateTime planEndTime = createReqVO.getPlanEndTime();
        if (planEndTime != null) {
            // 判断开始和结束时间是否都不带小时（小时为0）
            if (planStartTime.getHour() == 0 && planEndTime.getHour() == 0) {
                // 如果都不带小时，则只比较日期部分，允许同一天
                if (planEndTime.toLocalDate().isBefore(planStartTime.toLocalDate())) {
                    throw exception(BASE_SERVER_ERROR, "计划结束日期不能小于计划开始日期");
                }
            } else {
                // 至少有一个带小时，需要严格比较时间
                if (planEndTime.isBefore(planStartTime)) {
                    throw exception(BASE_SERVER_ERROR, "计划结束时间不能小于计划开始时间");
                }
            }
        }
        // 插入
        TaskPlanDO taskPlan = TaskPlanConvert.INSTANCE.convert(createReqVO);
        boolean isAfter530 = isAfter530(now);
        if (now.getHour() >= 9 && !isAfter530) {
            taskPlan.setIsAutoPause(1);
        }else {
            taskPlan.setIsAutoPause(0);
        }
        taskPlan.setIsManualPause(0);
        // 自动生成计划名称
        StringBuilder planName = new StringBuilder();
        // 起止地点
        if (StringUtils.isNoneBlank(createReqVO.getStartPoint(), createReqVO.getEndPoint())) {
            planName.append(createReqVO.getStartPoint()).append("到").append(createReqVO.getEndPoint());
        }
        // 货物信息
        if (StringUtils.isNotBlank(createReqVO.getCargoName())) {
            planName.append("的").append(createReqVO.getCargoName());
        }
        // 车辆需求
        if (createReqVO.getVehicleCount() != null && createReqVO.getVehicleType() != null) {
            planName.append("，需要")
                    .append(createReqVO.getVehicleCount())
                    .append("辆")
                    .append(formatVehicleType(createReqVO.getVehicleType())); // 需要实现车辆类型格式化方法
        }
        // 装货时间
        if (createReqVO.getPlanStartTime() != null) {
            planName.append("，")
                    .append(formatLoadingTime(createReqVO.getPlanStartTime(), createReqVO.getPlanEndTime()));
        }
        // 计划运价
        if (createReqVO.getEstimatedCost() != null) {
            planName.append("，单车计划运价")
                    .append(createReqVO.getEstimatedCost())
                    .append("元");
        }
        taskPlan.setPlanName(planName.toString());
        // 默认设置为未分配状态
        taskPlan.setStatus(TaskPlanStatusEnum.SCHEDULING.getStatus());
        taskPlan.setInterestedDriverCount(0);
        taskPlan.setTimeoutStatus(0);
        taskPlan.setId(TextUtil.generateId());
        int i = taskPlanMapper.insertTaskPlan(taskPlan);
        if (createReqVO.getDailyCargoId()!=null){
            //设置每日调度任务状态为调度中
            dailyCargoService.updateAiScheduleStatus(createReqVO.getDailyCargoId(), "1", taskPlan.getId());
        }
        // 返回创建的ID
        return taskPlan.getId();
    }

    @Override
    public Boolean updateTaskPlan(TaskPlanUpdateReqVO updateReqVO) {
        // 校验存在
        validateTaskPlanExists(updateReqVO.getId());
        // 更新
        TaskPlanDO updateObj = TaskPlanConvert.INSTANCE.convert(updateReqVO);
        int i = taskPlanMapper.updateTaskPlanById(updateObj);
        return i>0;
    }

    @Override
    public void deleteTaskPlan(Long id) {
        // 校验存在
        validateTaskPlanExists(id);
        // 删除
        int i = taskPlanMapper.deleteTaskPlanById(id);
    }

    @Override
    public TaskPlanDO getTaskPlan(Long id) {
        return taskPlanMapper.selectTaskPlanById(id);
    }

    @Override
    public List<TaskPlanDO> getTaskPlanList(Collection<Long> ids) {
        return taskPlanMapper.selectTasksBatchIds(ids);
    }

    @Override
    public List<TaskPlanDO> getTaskPlanPage(TaskPlanPageReqVO pageReqVO) {
		return taskPlanMapper.selectTaskPlanList(pageReqVO);
    }

    @Override
    public List<TaskPlanDO> getTaskPlanListByStatus(Integer status) {
        return taskPlanMapper.selectListByStatus(status);
    }

    @Override
    public List<TaskPlanDO> getTaskPlansByCondition(TaskPlanPageReqVO pageReqVO) {
        //通知类型：2-短信+外呼 1, // 状态：1-调度中 手动暂停状态：1-(开始状态)  任务未超时 或者即将超时
        return taskPlanMapper.selectTaskPlansByCondition(pageReqVO);
    }

    @Override
    public List<TaskPlanDO> getTaskPlanByOutTaskIdAndStatus(String uuidStr, Integer status) {
        return taskPlanMapper.getTaskPlanByOutTaskIdAndStatus(uuidStr,status);
    }

    private TaskPlanDO validateTaskPlanExists(Long id) {
        TaskPlanDO taskPlan = taskPlanMapper.selectTaskPlanById(id);
        if (taskPlan == null) {
            throw exception(TASK_PLAN_NOT_EXISTS);
        }
        return taskPlan;
    }

    private String formatVehicleType(String vehicleTypeCode) {
        switch (vehicleTypeCode) {
            case "FLATBED_9M6": return "9米6平板车";
            case "REFRIGERATED_4M2": return "4米2冷藏车";
            case "HIGH_RAIL_12M": return "12米高栏车";
            default: return vehicleTypeCode;
        }
    }

    // 格式化装货时间（支持 LocalDateTime）
    private static String formatLoadingTime(LocalDateTime startTime, LocalDateTime endTime) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("M月d日");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("H点");
        String format = "";
        if (startTime != null){
            format = startTime.format(dateFormatter);
            if (startTime.getHour() != 0){
                format = format + startTime.format(timeFormatter);
			}
			if (endTime == null){
				format = format + "装货";
			}
		}
        if (startTime != null && endTime !=null){
            boolean isSameDay = isStartTomorrow(startTime, endTime);
            if (isSameDay){
                if (endTime.getHour() != 0){
                    format = format +"-"+ endTime.format(timeFormatter) + "装货" ;
                }else {
                    format = format + "装货";
                }
            }else {
                String endTimeStr = endTime.format(dateFormatter);
                format =format + "-" + endTimeStr;
                if (endTime.getHour() != 0){
                    format = format + endTime.format(timeFormatter) + "装货" ;
                }else {
                    format = format + "装货";
                }
            }
        }
        return format;
    }
}