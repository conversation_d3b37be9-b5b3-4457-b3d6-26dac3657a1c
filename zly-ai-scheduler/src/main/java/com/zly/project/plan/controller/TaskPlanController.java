package com.zly.project.plan.controller;

import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.plan.controller.vo.TaskPlanCreateReqVO;
import com.zly.project.plan.controller.vo.TaskPlanPageReqVO;
import com.zly.project.plan.controller.vo.TaskPlanRespVO;
import com.zly.project.plan.convert.TaskPlanConvert;
import com.zly.project.plan.domain.TaskPlanDO;
import com.zly.project.plan.service.TaskPlanService;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags ="管理后台 - 任务调度计划")
@RestController
@RequestMapping("/scheduler/task-plan")
@Validated
public class TaskPlanController extends BaseController {

    @Resource
    private TaskPlanService taskPlanService;

    @Resource
    private ISysUserService sysUserService;

    @PreAuthorize("@ss.hasPermi('scheduler:task-plan:create')")
    @PostMapping("/create")
    @ApiOperation("创建任务调度计划")
    public AjaxResult createTaskPlan(@Valid @RequestBody TaskPlanCreateReqVO createReqVO) {
        return success(taskPlanService.createTaskPlan(createReqVO));
    }

    @PreAuthorize("@ss.hasPermi('scheduler:task-plan:page')")
    @GetMapping("/page")
    @ApiOperation("获得任务调度计划分页")
    public TableDataInfo list(@Valid TaskPlanPageReqVO pageVO) {
        startPage();
        List<TaskPlanDO> list = taskPlanService.getTaskPlanPage(pageVO);
        List<String> createByList = list.stream().map(TaskPlanDO::getCreateBy).collect(Collectors.toList());
        List<TaskPlanRespVO> taskPlanRespVOS = TaskPlanConvert.INSTANCE.convertList(list);
        Map<String, SysUser> sysUserListByCreateByList = sysUserService.getSysUserListByCreateByList(createByList);
        for (TaskPlanRespVO taskPlanRespVO : taskPlanRespVOS) {
            if (taskPlanRespVO.getCreateTime() != null) {
                taskPlanRespVO.setCreateTime(taskPlanRespVO.getCreateTime().plusHours(8));
            }
            SysUser sysUser = sysUserListByCreateByList.get(taskPlanRespVO.getCreateBy());
            if (sysUser !=null){
                taskPlanRespVO.setCreateByName(sysUser.getUserName());
            }
        }
        return getDataTable(taskPlanRespVOS);
    }

    @PreAuthorize("@ss.hasPermi('scheduler:task-plan:getTaskPlan')")
    @GetMapping("/get")
    @ApiOperation("获得任务调度计划")
    public AjaxResult getTaskPlan(@RequestParam("id") Long id) {
        TaskPlanDO taskPlan = taskPlanService.getTaskPlan(id);
        TaskPlanRespVO taskPlanRespVO = TaskPlanConvert.INSTANCE.convert(taskPlan);
        if (taskPlanRespVO.getCreateTime() != null) {
            taskPlanRespVO.setCreateTime(taskPlanRespVO.getCreateTime().plusHours(8));
        }
        List<String> createByList = new ArrayList<>(2);
        createByList.add(taskPlan.getCreateBy());
        Map<String, SysUser> sysUserListByCreateByList = sysUserService.getSysUserListByCreateByList(createByList);
        SysUser sysUser = sysUserListByCreateByList.get(taskPlanRespVO.getCreateBy());
        if (sysUser !=null){
            taskPlanRespVO.setCreateByName(sysUser.getNickName());
        }
        return success(taskPlanRespVO);
    }

//    @PutMapping("/update")
//    @Operation(summary = "更新任务调度计划")
//    @PreAuthorize("@ss.hasPermission('scheduler:task-plan:update')")
//    public CommonResult<Boolean> updateTaskPlan(@Valid @RequestBody TaskPlanUpdateReqVO updateReqVO) {
//        taskPlanService.updateTaskPlan(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除任务调度计划")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('scheduler:task-plan:delete')")
//    public CommonResult<Boolean> deleteTaskPlan(@RequestParam("id") Long id) {
//        taskPlanService.deleteTaskPlan(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得任务调度计划")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('scheduler:task-plan:query')")
//    public CommonResult<TaskPlanRespVO> getTaskPlan(@RequestParam("id") Long id) {
//        TaskPlanDO taskPlan = taskPlanService.getTaskPlan(id);
//        return success(TaskPlanConvert.INSTANCE.convert(taskPlan));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得任务调度计划列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('scheduler:task-plan:query')")
//    public CommonResult<List<TaskPlanRespVO>> getTaskPlanList(@RequestParam("ids") Collection<Long> ids) {
//        List<TaskPlanDO> list = taskPlanService.getTaskPlanList(ids);
//        return success(TaskPlanConvert.INSTANCE.convertList(list));
//    }
//


}