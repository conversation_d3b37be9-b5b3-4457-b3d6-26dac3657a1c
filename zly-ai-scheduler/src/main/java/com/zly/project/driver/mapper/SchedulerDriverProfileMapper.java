package com.zly.project.driver.mapper;

import java.util.List;
import com.zly.project.driver.domain.SchedulerDriverProfile;

/**
 * 司机画像Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface SchedulerDriverProfileMapper 
{
    /**
     * 查询司机画像
     * 
     * @param id 司机画像主键
     * @return 司机画像
     */
    public SchedulerDriverProfile selectSchedulerDriverProfileById(Long id);

    /**
     * 查询司机画像列表
     * 
     * @param schedulerDriverProfile 司机画像
     * @return 司机画像集合
     */
    public List<SchedulerDriverProfile> selectSchedulerDriverProfileList(SchedulerDriverProfile schedulerDriverProfile);

    /**
     * 新增司机画像
     * 
     * @param schedulerDriverProfile 司机画像
     * @return 结果
     */
    public int insertSchedulerDriverProfile(SchedulerDriverProfile schedulerDriverProfile);

    /**
     * 修改司机画像
     * 
     * @param schedulerDriverProfile 司机画像
     * @return 结果
     */
    public int updateSchedulerDriverProfile(SchedulerDriverProfile schedulerDriverProfile);

    /**
     * 删除司机画像
     * 
     * @param id 司机画像主键
     * @return 结果
     */
    public int deleteSchedulerDriverProfileById(Long id);

    /**
     * 批量删除司机画像
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchedulerDriverProfileByIds(Long[] ids);
}
