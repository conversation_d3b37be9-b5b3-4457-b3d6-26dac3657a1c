package com.zly.project.driver.controller.vo;

import com.zly.framework.web.page.PageDomain;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel(description = "管理后台 - 司机画像分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DriverProfilePageReqVO extends PageDomain {
    // 继承了 PageParam，已经包含了 pageSize 和 pageNum
	//增量标识 0表示全量 1表示增量 默认是0
	private Integer addFlag = 0;
}