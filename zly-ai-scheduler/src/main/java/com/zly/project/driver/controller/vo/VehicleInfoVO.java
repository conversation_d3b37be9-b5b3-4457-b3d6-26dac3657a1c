package com.zly.project.driver.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆信息 VO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 车辆信息 VO")
@Data
public class VehicleInfoVO {

    @ApiModelProperty(value = "司机ID", example = "2048")
    private Long driverId;
    
    @ApiModelProperty(value = "车牌号", example = "沪A12345")
    private String vehicleNumber;
    
    @ApiModelProperty(value = "车辆类型", example = "1")
    private String vehicleType;

    @ApiModelProperty(value = "车辆类型名称", example = "重型半挂牵引车")
    private String vehicleTypeName;
    
    @ApiModelProperty(value = "车长(米)", example = "12")
    private Long vehicleLength;
    
    @ApiModelProperty(value = "车宽(米)", example = "2")
    private Long vehicleWidth;
    
    @ApiModelProperty(value = "车高(米)", example = "3")
    private Long vehicleHeight;
    
    @ApiModelProperty(value = "能源类型(1:汽油 2:柴油 3:天然气 4:纯电动 5:混合动力)", example = "2")
    private Long vehicleEnergyType;
    
    @ApiModelProperty(value = "能源类型描述", example = "柴油")
    private String vehicleEnergyTypeDesc;

    @ApiModelProperty(value = "核定载质量（吨）", example = "30.5")
    private BigDecimal vehicleTonnage;

    @ApiModelProperty(value = "吨位 (吨)", example = "25.8")
    private BigDecimal grossMass;

    @ApiModelProperty(value = "状态（0：生效 1：失效 -1：删除）", example = "0")
    private Integer state;

    @ApiModelProperty(value = "是否装有GPS(0:未知 1:是 2:否)", example = "1")
    private Integer isGps;

    @ApiModelProperty(value = "车辆是否入网(0:未知 1:是 2:否)", example = "1")
    private Integer isExist;

    @ApiModelProperty(value = "车辆使用性质", example = "营运")
    private String useCharacter;

    @ApiModelProperty(value = "最近服务时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastServiceTime;

    @ApiModelProperty(value = "服务次数", example = "15")
    private Long serviceCount;

    @ApiModelProperty(value = "总里程(km)", example = "10000.5")
    private BigDecimal totalCourse;

    @ApiModelProperty(value = "是否资料齐全（0是 1不是）", example = "0")
    private Integer infoIsComplete;

    @ApiModelProperty(value = "牌照类型(1:蓝色 2:黄色 3:黑色 4:白色 5:绿色 9:其他 91:农黄色 92:农绿色 93:黄绿色 94:渐变绿)", example = "2")
    private Integer vehiclePlateColorCode;

    @ApiModelProperty(value = "车辆GPS信息")
    private VehicleGpsInfoVO gpsInfo;
}