package com.zly.project.driver.mapper;

import java.util.List;

import com.zly.project.driver.domain.SchedulerDriverProfile;
import com.zly.project.driver.domain.req.SchedulerDriverProfileReq;

/**
 * 司机画像Mapper接口扩展
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface SchedulerDriverProfileMapperEx {

	/**
	 * 查询司机画像列表
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 司机画像集合
	 */
	public List<SchedulerDriverProfile> selectSchedulerDriverProfileList(SchedulerDriverProfileReq schedulerDriverProfile);

	/**
	 * 查询重复手机号司机画像列表
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 司机画像集合
	 */
	public List<SchedulerDriverProfile> selectRepeatedSchedulerDriverProfileList(SchedulerDriverProfileReq schedulerDriverProfile);

	/**
	 * 修改司机画像
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 结果
	 */
	public int updateSchedulerDriverProfile(SchedulerDriverProfile schedulerDriverProfile);

}
