package com.zly.project.driver.service;


import com.zly.project.driver.controller.vo.DriverProfilePageReqVO;
import com.zly.project.driver.controller.vo.DriverProfilePageRespVO;
import com.zly.project.driver.domain.DriverProfileDO;

import java.util.List;
import java.util.Map;

/**
 * 司机画像 Service 接口
 *
 * <AUTHOR>
 */
public interface DriverProfileService {

    /**
     * 获取司机画像分页，并保存到本地数据库
     *
     * @param pageReqVO 分页查询参数
     * @return 司机画像分页结果
     */
    DriverProfilePageRespVO getDriverProfilePage(DriverProfilePageReqVO pageReqVO);

    /**
     * 根据司机ID获取司机画像
     *
     * @param driverId 司机ID
     * @return 司机画像
     */
    DriverProfileDO getDriverProfile(String driverId);
    
    /**
     * 分批查询并同步所有司机画像数据
     * 首次查询100条，获取总数后分页查询所有数据
     *
     * @return 同步结果，包含总数和成功同步数量
     */
    Map<String, Object> syncAllDriverProfiles();
    
    /**
     * 导出司机画像数据为Markdown文件
     *
     * @return 生成的Markdown文件内容的字节数组
     */
    byte[] exportDriverProfilesMarkdown();

    /**
     * 查询知识库
     * @param query
     * @param indexId
     * @param topK
     * @return
     */
    List<String> retrieveFromKnowledgeBase(String query, String indexId, Integer topK);

    /**
     * 查询知识库
     * @param query
     * @param indexId
     * @param topK
     * @return
     */
    Map<String,String> retrieveFromKnowledgeBaseMap(String query, String indexId, Integer topK);
    
    /**
     * 导出司机画像数据为Excel格式
     *
     * @return Excel文件的字节数组
     */
    byte[] exportDriverProfilesExcel();
    
    /**
     * 自动上传司机画像到知识库
     * 
     * @return 上传结果
     */
    Map<String, Object> uploadDriverProfilesToKnowledgeBase(String DEFAULT_AGENT_KEY,String DEFAULT_MODEL_ID,String DEFAULT_INDEX_ID);

    /**
     * 更新司机画像信息
     * @param driverProfileDO
     * @return
     */
    int updateDriverProfile(DriverProfileDO driverProfileDO);

    /**
     * 分批查询并同步所有司机画像数据
     * 首次查询100条，获取总数后分页查询所有数据
     *
     * @return 同步结果，包含总数和成功同步数量
     */
    Map<String, Object> syncAllDriverProfilesNoAddFlag();

}