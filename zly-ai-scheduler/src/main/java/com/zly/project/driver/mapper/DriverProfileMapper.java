package com.zly.project.driver.mapper;

import com.zly.project.driver.domain.DriverProfileDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 司机画像 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DriverProfileMapper {
    
    /**
     * 根据司机ID查询司机画像
     *
     * @param driverId 司机ID
     * @return 司机画像
     */
    DriverProfileDO selectByDriverId(String driverId);

    /**
     * 根据司机ID查询司机画像
     *
     * @param id 司机ID
     * @return 司机画像
     */
    DriverProfileDO selectById(Long id);
    
    /**
     * 查询所有司机画像
     *
     * @return 司机画像列表
     */
     List<DriverProfileDO> selectAllDriverList();
    
    /**
     * 根据司机ID列表批量查询司机画像
     *
     * @param driverIds 司机ID列表
     * @return 司机画像列表
     */
    List<DriverProfileDO> selectListByDriverIds(Collection<String> driverIds);
    
    /**
     * 根据身份证号查询司机画像
     *
     * @param identityCard 身份证号
     * @return 司机画像
     */
    DriverProfileDO selectByIdentityCard(String identityCard);
    
    /**
     * 根据手机号查询司机画像
     *
     * @param telephone 手机号
     * @return 司机画像
     */
    DriverProfileDO selectByTelephone(String telephone);
    
    /**
     * 根据姓名和手机号查询司机画像
     *
     * @param driverName 司机姓名
     * @param telephone 手机号
     * @return 司机画像
     */
    DriverProfileDO selectByNameAndTelephone(String driverName, String telephone);

    /**
     * 新增数据
     * @param profile
     * @return
     */
    int insertDriverProfile(DriverProfileDO profile);

    /**
     * 更新司机画像信息
     * @param profile
     * @return
     */
    int updateDriverProfile(DriverProfileDO profile);

    /**
     *
     * @param driverName 司机姓名
     * @param telephone 手机号
     * @param identityCard 身份证
     * @return 司机画像
     */
    DriverProfileDO selectByNameAndTelephoneAndIdentityCard(String driverName, String telephone, String identityCard);

    /**
     * 批量插入
     * @param toInsertList
     * @return
     */
    int insertBatch(@Param("list")List<DriverProfileDO> toInsertList);

    /**
     *  批量更新
     * @param toUpdateList
     * @return
     */
    int updateDriverProfileBatch(@Param("list")List<DriverProfileDO> toUpdateList);

    /**
     * 分页查询有效且非黑名单的司机数据
     *
     * @param pageSize 页码，从1开始
     * @param offset 偏移量
     * @return 司机画像列表
     */
    List<DriverProfileDO> selectActiveNonBlacklistDrivers( @Param("offset") int offset,@Param("pageSize") int pageSize);

    /**
     * 查询数量
     * @return
     */
    int countDriverProfiles();

    /**
     * 根据起始位置和结束位置模糊查询匹配的司机数据
     *
     * @param startPoint 起始位置
     * @param endPoint 结束位置
     * @return 匹配的司机列表
     */
    List<DriverProfileDO> selectDriversByRoutes(@Param("startPoint") String startPoint, @Param("endPoint") String endPoint);
}