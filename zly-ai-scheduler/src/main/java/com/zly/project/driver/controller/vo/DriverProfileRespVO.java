package com.zly.project.driver.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 司机画像信息 Response VO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 司机画像信息 Response VO")
@Data
public class DriverProfileRespVO {

    @ApiModelProperty(value = "ID", example = "168361594535800480")
    private Long id;

    @ApiModelProperty(value = "司机ID", example = "168361594535800480")
    private String driverId;

    @ApiModelProperty(value = "司机姓名", example = "尹招展")
    private String driverName;

    @ApiModelProperty(value = "电话号码", example = "13615578519")
    private String telephone;

    @ApiModelProperty(value = "调度中心电话号码", example = "13615578519")
    private String schedulerTelephone;

    @ApiModelProperty(value = "身份证号", example = "342201197702027953")
    private String identityCard;

    @ApiModelProperty(value = "驾龄(年)", example = "10")
    private Integer drivingYears;

    @ApiModelProperty(value = "资料完整度(%)", example = "100")
    private Integer profileCompleteness;

    @ApiModelProperty(value = "服务次数", example = "1")
    private Integer serviceCount;

    @ApiModelProperty(value = "总里程", example = "0.0")
    private String totalCourse;

    @ApiModelProperty(value = "司机来源", example = "0")
    private Integer operation;

    @ApiModelProperty(value = "司机来源描述", example = "未知")
    private String operationDesc;

    @ApiModelProperty(value = "驾驶证是否有效", example = "-1")
    private Integer isTheDrivrLicenseValid;

    @ApiModelProperty(value = "驾驶证类型", example = "A1A2")
    private String vehicleClass;

    @ApiModelProperty(value = "驾驶证有效期", example = "2025-01-06")
    private String validPeriodTo;

    @ApiModelProperty(value = "从业资格证", example = "342201197702027953")
    private String qualificationCertificate;

    @ApiModelProperty(value = "从业资格证是否有效", example = "-2")
    private Integer isTheQualificationValid;

    @ApiModelProperty(value = "实际承运截止日期", example = "")
    private String actualCarrierEnd;

    @ApiModelProperty(value = "最近服务时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastServiceTime;

    @ApiModelProperty(value = "状态", example = "0")
    private Integer state;

    @ApiModelProperty(value = "地址", example = "0")
    private String address;

    @ApiModelProperty(value = "车辆数据JSON", example = "0")
    private String vehicles;

    @ApiModelProperty(value = "常跑路线JSON", example = "0")
    private String longDistanceRoutes;

    @ApiModelProperty("调度中心这边的车辆结构 (JSON)")
    private String schedulerVehicleStructure;

    /** 4.0系统那边的车辆结构 */
    @ApiModelProperty("4.0系统那边的车辆结构")
    private String wlhyVehicleStructure;

    @ApiModelProperty("需要排除的车辆结构 (JSON)")
    private String excludedVehicleStructure;

    @ApiModelProperty(" 调度中心这边的常运货物 (JSON)")
    private String schedulerGoods;

    /** 4.0系统那边的常运货物 */
    @ApiModelProperty("4.0系统那边的常运货物")
    private String wlhyGoods;

    @ApiModelProperty("需要排除的常运货物 (JSON)")
    private String excludedGoods;

    @ApiModelProperty("调度中心这边的常跑线路 (JSON)")
    private String schedulerHaulway;

    /** 4.0系统那边的常跑线路 */
    @ApiModelProperty("4.0系统那边的常跑线路")
    private String wlhyHaulway;

    @ApiModelProperty("需要排除的常跑线路 (JSON)")
    private String excludedHaulway;

    @ApiModelProperty("调度中心这边的车辆长度 (JSON)")
    private String schedulerVehicleLength;

    @ApiModelProperty("4.0系统那边的车辆长度")
    private String wlhyVehicleLength;

    @ApiModelProperty("需要排除的车辆长度 (JSON)")
    private String excludedVehicleLength;

    @ApiModelProperty("货物重量")
    private BigDecimal goodsWeight;

    @ApiModelProperty("货物体积")
    private BigDecimal goodsVolume;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;
}