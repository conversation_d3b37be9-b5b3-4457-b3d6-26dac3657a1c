package com.zly.project.driver.controller;

import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.framework.config.AiModelBaiLianConfig;
import com.zly.framework.knowledge.KnowledgeBaseService;
import com.zly.framework.knowledge.UploadResult;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.driver.service.DriverProfileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zly.common.utils.DateUtils;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.zly.framework.web.domain.AjaxResult.error;
import static com.zly.framework.web.domain.AjaxResult.success;

/**
 * 管理后台 - 司机画像
 *
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 司机画像")
@RestController
@RequestMapping("/scheduler/driver-profile")
@Validated
@Slf4j
public class DriverProfileController {

    @Resource
    private DriverProfileService driverProfileService;

    @Resource
    private AiModelBaiLianConfig aiModelBaiLianConfig;

    @GetMapping("/export-excel")
    @ApiOperation("导出司机画像数据为Excel文件")
    public ResponseEntity<byte[]> exportDriverProfilesExcel() {
        byte[] excelContent = driverProfileService.exportDriverProfilesExcel();
        // 设置HTTP响应头，使浏览器将响应视为文件下载
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/vnd.ms-excel"));
        headers.setContentDispositionFormData("attachment", "driver_profiles_" + 
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".xlsx");
        // 返回文件下载响应
        return new ResponseEntity<>(excelContent, headers, HttpStatus.OK);
    }

    @GetMapping("/export-markdown")
    @ApiOperation("导出司机画像数据为Markdown文件")
    public ResponseEntity<byte[]> exportDriverProfilesMarkdown() {
        byte[] markdownContent = driverProfileService.exportDriverProfilesMarkdown();
        // 设置HTTP响应头，使浏览器将响应视为文件下载
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "driver_profiles_" + 
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".md");
        // 返回文件下载响应
        return new ResponseEntity<>(markdownContent, headers, HttpStatus.OK);
    }

    @Anonymous
    @GetMapping("/sync-all")
    @ApiOperation("同步所有司机画像数据")
    public AjaxResult syncAllDriverProfiles() {
        Map<String, Object> result = driverProfileService.syncAllDriverProfiles();
        return success(result);
    }

    @Anonymous
    @GetMapping("/sync-all-noAddFlag")
    @ApiOperation("全量同步所有司机画像数据")
    public AjaxResult syncAllDriverProfilesNoAddFlag() {
        Map<String, Object> result = driverProfileService.syncAllDriverProfilesNoAddFlag();
        return success(result);
    }

    @Anonymous
    @PostMapping("/upload-to-knowledge-base")
    @ApiOperation("上传司机画像到知识库")
    public AjaxResult uploadDriverProfilesToKnowledgeBase() {
        String agentKey = aiModelBaiLianConfig.getAgentKey();
        String modelId = aiModelBaiLianConfig.getModelId();
        String indexId = aiModelBaiLianConfig.getIndexId();
        Map<String, Object> result = driverProfileService.uploadDriverProfilesToKnowledgeBase(agentKey,modelId,indexId);
        if (result != null && Boolean.TRUE.equals(result.get("success"))) {
            return success(result);
        } else {
            // 如果上传失败，返回错误信息
            String message = result != null && result.get("message") != null ?
                    result.get("message").toString() : "上传司机画像到知识库失败";
            return error(message, result);
        }
    }

    @Anonymous
    @PostMapping("/deleteKnowledgeBase/{pageNumber}/{pageSize}")
    @ApiOperation("删除知识库")
    public AjaxResult deleteKnowledgeBase(@PathVariable("pageNumber") Integer pageNumber,
            @PathVariable("pageSize") Integer pageSize) {
        String modelId = aiModelBaiLianConfig.getModelId();
        String indexId = aiModelBaiLianConfig.getIndexId();
        List<String> knowledgeBaseFileIds = KnowledgeBaseService.listKnowledgeBaseFileIds(modelId, indexId,pageNumber,pageSize);
        Map<String,List<String>> map = new HashMap<>(8);
        List<String> deletedFileIds = new ArrayList<>();
        List<String> failedFileIds = new ArrayList<>();
        for (String knowledgeBaseFileId : knowledgeBaseFileIds) {
            try {
                UploadResult delIndexUploadResult = KnowledgeBaseService.deleteFileFromKnowledgeBase(knowledgeBaseFileId, indexId,modelId);
                log.info("deleteKnowledgeBase,delIndexUploadResult:{}",delIndexUploadResult);
                if (delIndexUploadResult.getIsSuccess()) {
                    deletedFileIds.add(knowledgeBaseFileId);
                    log.info("成功删除旧文件: {}", knowledgeBaseFileId);
                } else {
                    failedFileIds.add(knowledgeBaseFileId);
                    log.warn("删除旧文件失败: {}, 知识库删除结果: {}", knowledgeBaseFileId, "失败");
                }
            } catch (Exception e) {
                failedFileIds.add(knowledgeBaseFileId);
                log.warn("删除旧文件异常({}): {}", knowledgeBaseFileId, e.getMessage());
            }
        }
        map.put("sucDel",deletedFileIds);
        map.put("failDel",failedFileIds);
        return success(map);
    }

    @Anonymous
    @ApiOperation("手动同步司机画像数据并上传到知识库")
    @PostMapping("/syncAndUpload")
    public AjaxResult syncAndUploadDriverProfiles() {
        try {
            log.info("{}开始同步司机画像数据", DateUtils.getTime());
            // 调用同步方法
            Map<String, Object> result = driverProfileService.syncAllDriverProfiles();
            // 记录同步结果
            Integer totalCount = (Integer) result.getOrDefault("totalCount", 0);
            Integer syncCount = (Integer) result.getOrDefault("syncCount", 0);
            String message = (String) result.getOrDefault("message", "");
            log.info("同步司机画像数据执行完成，总数: {}，同步成功: {}，详情: {}",
                    totalCount, syncCount, message);
            // 上传数据分片到知识库
            String agentKey = aiModelBaiLianConfig.getAgentKey();
            String modelId = aiModelBaiLianConfig.getModelId();
            String indexId = aiModelBaiLianConfig.getIndexId();
            Map<String, Object> uploadDriverProfilesToKnowledgeBase = driverProfileService.uploadDriverProfilesToKnowledgeBase(agentKey,modelId,indexId);
            if (uploadDriverProfilesToKnowledgeBase != null && Boolean.TRUE.equals(uploadDriverProfilesToKnowledgeBase.get("success"))) {
                log.info("上传数据到知识库，uploadDriverProfilesToKnowledgeBase:{}",uploadDriverProfilesToKnowledgeBase);
                return AjaxResult.success("同步司机画像数据并上传到知识库成功");
            } else {
                // 如果上传失败，返回错误信息
                String errorMessage = uploadDriverProfilesToKnowledgeBase != null && uploadDriverProfilesToKnowledgeBase.get("message") != null ?
                        result.get("message").toString() : "上传司机画像到知识库失败";
                log.info("上传数据到知识库，异常信息:{}",errorMessage);
                return AjaxResult.error("同步司机画像数据成功，但上传到知识库失败：" + errorMessage);
            }
        } catch (Exception e) {
            log.error("同步司机画像数据执行异常: ", e);
            return AjaxResult.error("同步司机画像数据执行异常：" + e.getMessage());
        }
    }

}