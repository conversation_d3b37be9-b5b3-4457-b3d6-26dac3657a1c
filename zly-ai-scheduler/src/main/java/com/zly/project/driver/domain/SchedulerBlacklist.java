package com.zly.project.driver.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;

import io.swagger.annotations.ApiModelProperty;

/**
 * 黑名单配置对象 scheduler_blacklist
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public class SchedulerBlacklist extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**  */
	@Excel(name = "")
	private Long id;

	/** 黑名单介质 */
	@Excel(name = "黑名单介质")
	@ApiModelProperty(value = "黑名单介质", required = false)
	private String blackMember;

	/** 类型（1.手机号） */
	@Excel(name = "类型", readConverterExp = "1=.手机号")
	private Integer type;

	/** 状态（0.正常 1.停用） */
	@Excel(name = "状态", readConverterExp = "0=.正常,1=.停用")
	private Integer state;

	public void setId(Long id) {
		this.id = id;
	}

	public Long getId() {
		return id;
	}

	public void setBlackMember(String blackMember) {
		this.blackMember = blackMember;
	}

	public String getBlackMember() {
		return blackMember;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getType() {
		return type;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return state;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("blackMember", getBlackMember()).append("type", getType()).append("state", getState())
				.append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy()).append("updateTime", getUpdateTime()).toString();
	}
}
