package com.zly.project.driver.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 车辆GPS信息 VO
 *
 * <AUTHOR>
 */
@ApiModel(description = "管理后台 - 车辆GPS信息 VO")
@Data
public class VehicleGpsInfoVO {
    
    @ApiModelProperty(value = "车辆ID", example = "2048")
    private Long vehicleId;
    
    @ApiModelProperty(value = "经度", example = "121.4737")
    private String longitude;
    
    @ApiModelProperty(value = "纬度", example = "31.2304")
    private String latitude;
    
    @ApiModelProperty(value = "地址", example = "上海市浦东新区张江高科技园区")
    private String address;
    
    @ApiModelProperty(value = "速度(km/h)", example = "60.5")
    private Double speed;
    
    @ApiModelProperty(value = "方向(角度)", example = "90")
    private Integer direction;
    
    @ApiModelProperty(value = "海拔(米)", example = "12.5")
    private Double altitude;
    
    @ApiModelProperty(value = "省", example = "上海市")
    private String province;
    
    @ApiModelProperty(value = "城市", example = "上海市")
    private String city;
    
    @ApiModelProperty(value = "区", example = "浦东新区")
    private String country;
    
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}