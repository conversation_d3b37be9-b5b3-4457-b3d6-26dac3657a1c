package com.zly.project.driver.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.driver.domain.SchedulerBlacklist;
import com.zly.project.driver.service.ISchedulerBlacklistService;

import io.swagger.annotations.ApiOperation;

/**
 * 黑名单配置Controller
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@RestController
@RequestMapping("/driver/blacklist")
public class SchedulerBlacklistController extends BaseController {
	@Autowired
	private ISchedulerBlacklistService schedulerBlacklistService;

	/**
	 * 查询黑名单配置列表
	 */
	@ApiOperation("手机号黑名单列表")
	@PreAuthorize("@ss.hasPermi('driver:blacklist:list')")
	@GetMapping("/list")
	public TableDataInfo list(SchedulerBlacklist schedulerBlacklist) {
		startPage();
		List<SchedulerBlacklist> list = schedulerBlacklistService.selectSchedulerBlacklistList(schedulerBlacklist);
		return getDataTable(list);
	}

	/**
	 * 导出黑名单配置列表
	 */
	@PreAuthorize("@ss.hasPermi('driver:blacklist:export')")
	@Log(title = "黑名单配置", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, SchedulerBlacklist schedulerBlacklist) {
		List<SchedulerBlacklist> list = schedulerBlacklistService.selectSchedulerBlacklistList(schedulerBlacklist);
		ExcelUtil<SchedulerBlacklist> util = new ExcelUtil<SchedulerBlacklist>(SchedulerBlacklist.class);
		util.exportExcel(response, list, "黑名单配置数据");
	}

	/**
	 * 获取黑名单配置详细信息
	 */
	@PreAuthorize("@ss.hasPermi('driver:blacklist:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(schedulerBlacklistService.selectSchedulerBlacklistById(id));
	}

	/**
	 * 新增黑名单配置
	 */
	@ApiOperation("新增手机号黑名单")
	@PreAuthorize("@ss.hasPermi('driver:blacklist:add')")
	@Log(title = "黑名单配置", businessType = BusinessType.INSERT)
	@PostMapping
	public AjaxResult add(@RequestBody SchedulerBlacklist schedulerBlacklist) {
		return schedulerBlacklistService.insertSchedulerBlacklist(schedulerBlacklist);
	}

	/**
	 * 修改黑名单配置
	 */
	@PreAuthorize("@ss.hasPermi('driver:blacklist:edit')")
	@Log(title = "黑名单配置", businessType = BusinessType.UPDATE)
	@PutMapping
	public AjaxResult edit(@RequestBody SchedulerBlacklist schedulerBlacklist) {
		return toAjax(schedulerBlacklistService.updateSchedulerBlacklist(schedulerBlacklist));
	}

	/**
	 * 删除黑名单配置
	 */
	@ApiOperation("取消手机号黑名单")
	@PreAuthorize("@ss.hasPermi('driver:blacklist:remove')")
	@Log(title = "黑名单配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult remove(@PathVariable Long[] ids) {
		return toAjax(schedulerBlacklistService.deleteSchedulerBlacklistByIds(ids));
	}
}
