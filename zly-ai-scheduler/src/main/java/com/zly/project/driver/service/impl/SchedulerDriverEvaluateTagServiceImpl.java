package com.zly.project.driver.service.impl;

import java.util.*;

import com.zly.common.constant.BusinessConstants;
import com.zly.project.system.service.impl.SysClientLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.constant.Constants;
import com.zly.common.utils.DictUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.TextUtil;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.driver.domain.SchedulerDriverEvaluateTag;
import com.zly.project.driver.domain.SchedulerDriverProfile;
import com.zly.project.driver.mapper.SchedulerDriverEvaluateTagMapper;
import com.zly.project.driver.mapper.SchedulerDriverProfileMapper;
import com.zly.project.driver.service.ISchedulerDriverEvaluateTagService;
import com.zly.project.system.domain.SysDictData;
import com.zly.project.system.mapper.SysDictDataMapper;

import javax.annotation.Resource;

/**
 * 司机评价、标记Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@Service
public class SchedulerDriverEvaluateTagServiceImpl implements ISchedulerDriverEvaluateTagService {

	@Autowired
	private SchedulerDriverEvaluateTagMapper schedulerDriverEvaluateTagMapper;

	@Autowired
	private SchedulerDriverProfileMapper schedulerDriverProfileMapper;

	@Autowired
	private SysDictDataMapper dictDataMapper;

	@Resource
	private SysClientLogService sysClientLogService;

	/**
	 * 查询司机评价、标记
	 * 
	 * @param id
	 *            司机评价、标记主键
	 * @return 司机评价、标记
	 */
	@Override
	public SchedulerDriverEvaluateTag selectSchedulerDriverEvaluateTagById(Long id) {
		return schedulerDriverEvaluateTagMapper.selectSchedulerDriverEvaluateTagById(id);
	}

	/**
	 * 查询司机评价、标记列表
	 * 
	 * @param schedulerDriverEvaluateTag
	 *            司机评价、标记
	 * @return 司机评价、标记
	 */
	@Override
	public List<SchedulerDriverEvaluateTag> selectSchedulerDriverEvaluateTagList(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag) {
		return schedulerDriverEvaluateTagMapper.selectSchedulerDriverEvaluateTagList(schedulerDriverEvaluateTag);
	}

	/**
	 * 新增司机评价、标记
	 * 
	 * @param schedulerDriverEvaluateTag
	 *            司机评价、标记
	 * @return 结果
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult insertSchedulerDriverEvaluateTag(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag) {
		if (null == schedulerDriverEvaluateTag || null == schedulerDriverEvaluateTag.getDriverProfileId()) {
			return AjaxResult.error("司机对象为空");
		}
		if (null == schedulerDriverEvaluateTag.getType() || (schedulerDriverEvaluateTag.getType().intValue() != 1 && schedulerDriverEvaluateTag.getType().intValue() != 2)) {
			return AjaxResult.error("请确认对司机评价还是标记");
		}
		if (StringUtils.isBlank(schedulerDriverEvaluateTag.getContent())) {
			return AjaxResult.error(schedulerDriverEvaluateTag.getType().intValue() == 1 ? "司机评价内容为空" : "司机标记内容为空");
		}
		SchedulerDriverProfile driver = schedulerDriverProfileMapper.selectSchedulerDriverProfileById(schedulerDriverEvaluateTag.getDriverProfileId());
		if (null == driver) {
			return AjaxResult.error("没有找到对应的司机信息");
		}
		boolean isUpdateFlag = false;
		// 如果是标记，先删除后再新增
		if (schedulerDriverEvaluateTag.getType() == 2) {
			SchedulerDriverEvaluateTag delTag = new SchedulerDriverEvaluateTag();
			delTag.setDriverProfileId(schedulerDriverEvaluateTag.getDriverProfileId());
			delTag.setType(2);
			List<SchedulerDriverEvaluateTag> tagList = schedulerDriverEvaluateTagMapper.selectSchedulerDriverEvaluateTagList(delTag);
			Long[] ids = tagList.stream().map(SchedulerDriverEvaluateTag::getId).toArray(Long[]::new);
			if (null != ids && ids.length > 0) {
				schedulerDriverEvaluateTagMapper.deleteSchedulerDriverEvaluateTagByIds(ids);
				isUpdateFlag = true;
			}
			// 检查数据字典中有没有该标记，没有就新增一个
			List<SysDictData> dictDataList = dictDataMapper.selectDictDataByType(Constants.DICT_TYPE_DRIVER_TAG);
			Long dictSort = dictDataList.stream().filter(Objects::nonNull) // 过滤空对象
					.mapToLong(SysDictData::getDictSort).max().orElse(-1);// 获取最大排序值
			String dictValue = Optional.ofNullable(dictDataList).flatMap(list -> list.stream().filter(t -> t != null && t.getDictValue() != null).filter(t -> t.getDictValue().matches("\\d+"))
					.max(Comparator.comparing(t -> Long.parseLong(t.getDictValue())))).map(SysDictData::getDictValue).orElse("0");// 获取最大value值
			JSONArray jsonArray = JSONArray.parseArray(schedulerDriverEvaluateTag.getContent());
			for (Object obj : jsonArray) {
				boolean anyContains = dictDataList.stream().filter(data -> data != null).map(SysDictData::getDictLabel).anyMatch(label -> label != null && label.equals(obj.toString()));
				if (!anyContains) {
					dictSort++;
					Long dictLongValue = Long.valueOf(dictValue);
					dictLongValue++;
					dictValue = String.valueOf(dictLongValue);
					SysDictData insertDictData = new SysDictData();
					insertDictData.setDictType(Constants.DICT_TYPE_DRIVER_TAG);
					insertDictData.setDictSort(dictSort);
					insertDictData.setDictLabel(obj.toString());
					insertDictData.setDictValue(dictValue);
					insertDictData.setListClass("default");
					insertDictData.setCreateBy(SecurityUtils.getLoginUser().getUsername());
					insertDictData.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
					dictDataMapper.insertDictData(insertDictData);
				}
			}
			List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(Constants.DICT_TYPE_DRIVER_TAG);
			DictUtils.setDictCache(Constants.DICT_TYPE_DRIVER_TAG, dictDatas);
		}
		schedulerDriverEvaluateTag.setId(TextUtil.getTimeSequenceID(5));
		schedulerDriverEvaluateTag.setCreateBy(SecurityUtils.getLoginUser().getUsername());
		schedulerDriverEvaluateTag.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
		schedulerDriverEvaluateTagMapper.insertSchedulerDriverEvaluateTag(schedulerDriverEvaluateTag);
		if (schedulerDriverEvaluateTag.getType() != null && schedulerDriverEvaluateTag.getType() == 2) {
			//表示更新运力信息
			String action;
			int actionTypeUpdateOrAdd;
			if (isUpdateFlag){
				action = "修改运力,["+driver.getDriverName()+ driver.getIdentityCard()+"] 标记";
				actionTypeUpdateOrAdd = BusinessConstants.ACTION_TYPE_UPDATE;
			}else {
				action = "新增运力,["+driver.getDriverName()+ driver.getIdentityCard()+"] 标记";
				actionTypeUpdateOrAdd = BusinessConstants.ACTION_TYPE_ADD;
			}
			sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_DRIVER_PROFILE_TAG, actionTypeUpdateOrAdd, action,String.valueOf(driver.getId()), Collections.singletonList(driver.getId()));
		}
		// 如果是评价，需要更新司机的综合评价
		if (schedulerDriverEvaluateTag.getType() == 1) {
			SchedulerDriverEvaluateTag tmp = new SchedulerDriverEvaluateTag();
			tmp.setDriverProfileId(schedulerDriverEvaluateTag.getDriverProfileId());
			tmp.setType(schedulerDriverEvaluateTag.getType());
			List<SchedulerDriverEvaluateTag> list = schedulerDriverEvaluateTagMapper.selectSchedulerDriverEvaluateTagList(tmp);

			double totalAverage = 0d;// 每个单次评价的平均分之和
			for (SchedulerDriverEvaluateTag sdet : list) {
				JSONArray jsonArray = JSONArray.parseArray(sdet.getContent());
				int sum = 0, count = 0;
				for (Object obj : jsonArray) {
					JSONObject jsonObj = (JSONObject) obj;
					for (Map.Entry<String, Object> entry : jsonObj.entrySet()) {
						Object value = entry.getValue();
						if (value instanceof Number) {
							sum += ((Number) value).intValue();
							count++;
						}
					}
				}
				double average = count == 0 ? 0 : (double) sum / count;
				totalAverage += average;
				// System.out.println("总和: " + sum);
				// System.out.println("平均值: " + average);
			}
			double overall = totalAverage / list.size();// 综合评价 = 该司机每个单次评价的平均分之和 / 该司机的评价次数
			// System.out.println("总平均值: " + totalAverage);
			// System.out.println("综合评分: " + overall);
			String overallEvaluate = "-";
			if (overall > 4) {
				overallEvaluate = "优";
			} else if (overall > 3 && overall <= 4) {
				overallEvaluate = "中";
			} else {
				overallEvaluate = "低";
			}
			// 更新司机综合评价
			SchedulerDriverProfile schedulerDriverProfile = new SchedulerDriverProfile();
			schedulerDriverProfile.setId(schedulerDriverEvaluateTag.getDriverProfileId());
			schedulerDriverProfile.setOverallEvaluate(overallEvaluate);
			schedulerDriverProfile.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
			schedulerDriverProfileMapper.updateSchedulerDriverProfile(schedulerDriverProfile);
		}
		return AjaxResult.success();
	}

	/**
	 * 修改司机评价、标记
	 * 
	 * @param schedulerDriverEvaluateTag
	 *            司机评价、标记
	 * @return 结果
	 */
	@Override
	public int updateSchedulerDriverEvaluateTag(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag) {
		return schedulerDriverEvaluateTagMapper.updateSchedulerDriverEvaluateTag(schedulerDriverEvaluateTag);
	}

	/**
	 * 批量删除司机评价、标记
	 * 
	 * @param ids
	 *            需要删除的司机评价、标记主键
	 * @return 结果
	 */
	@Override
	public int deleteSchedulerDriverEvaluateTagByIds(Long[] ids) {
		return schedulerDriverEvaluateTagMapper.deleteSchedulerDriverEvaluateTagByIds(ids);
	}

	/**
	 * 删除司机评价、标记信息
	 * 
	 * @param id
	 *            司机评价、标记主键
	 * @return 结果
	 */
	@Override
	public int deleteSchedulerDriverEvaluateTagById(Long id) {
		return schedulerDriverEvaluateTagMapper.deleteSchedulerDriverEvaluateTagById(id);
	}
}
