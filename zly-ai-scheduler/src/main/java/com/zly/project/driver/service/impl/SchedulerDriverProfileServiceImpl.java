package com.zly.project.driver.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.json.JSONUtil;
import com.zly.common.constant.BusinessConstants;
import com.zly.project.system.service.impl.SysClientLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.TextUtil;
import com.zly.common.utils.driver.DriverPortraitMarkdownConverter;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.driver.domain.SchedulerDriverEvaluateTag;
import com.zly.project.driver.domain.SchedulerDriverProfile;
import com.zly.project.driver.domain.req.SchedulerDriverProfileReq;
import com.zly.project.driver.domain.res.SchedulerDriverProfileRes;
import com.zly.project.driver.mapper.SchedulerDriverEvaluateTagMapper;
import com.zly.project.driver.mapper.SchedulerDriverProfileMapper;
import com.zly.project.driver.mapper.SchedulerDriverProfileMapperEx;
import com.zly.project.driver.service.ISchedulerDriverProfileService;

import jodd.util.StringUtil;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 司机画像Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@Service
public class SchedulerDriverProfileServiceImpl implements ISchedulerDriverProfileService {

	@Autowired
	private SchedulerDriverProfileMapper schedulerDriverProfileMapper;

	@Autowired
	private SchedulerDriverProfileMapperEx schedulerDriverProfileMapperEx;

	@Autowired
	private SchedulerDriverEvaluateTagMapper schedulerDriverEvaluateTagMapper;

	@Resource
	private SysClientLogService sysClientLogService;

	/**
	 * 查询司机画像
	 * 
	 * @param id
	 *            司机画像主键
	 * @return 司机画像
	 */
	@Override
	public AjaxResult selectSchedulerDriverProfileById(Long id) {
		SchedulerDriverProfileRes res = new SchedulerDriverProfileRes();
		SchedulerDriverProfile driver = schedulerDriverProfileMapper.selectSchedulerDriverProfileById(id);
		if (null == driver) {
			return AjaxResult.error("没有找到对应的司机信息");
		}
		String mergedHaulway = DriverPortraitMarkdownConverter.mergeHaulway(driver.getWlhyHaulway(), driver.getSchedulerHaulway(), driver.getExcludedHaulway());
		mergedHaulway = StringUtil.isEmpty(mergedHaulway) ? null : new JSONArray(java.util.Arrays.asList(mergedHaulway.split("、"))).toJSONString();
		String mergedVehicleLengths = DriverPortraitMarkdownConverter.mergeAndProcessVehicleLengths(driver.getWlhyVehicleLength(), driver.getSchedulerVehicleLength(),
				driver.getExcludedVehicleLength());
		mergedVehicleLengths = StringUtil.isEmpty(mergedVehicleLengths) ? null : new JSONArray(java.util.Arrays.asList(mergedVehicleLengths.split("、"))).toJSONString();
		String mergedGoods = DriverPortraitMarkdownConverter.mergeAndProcessGoods(driver.getWlhyGoods(), driver.getSchedulerGoods(), driver.getExcludedGoods());
		mergedGoods = StringUtil.isEmpty(mergedGoods) ? null : new JSONArray(java.util.Arrays.asList(mergedGoods.split("、"))).toJSONString();
		String mergedVehicleStructure = DriverPortraitMarkdownConverter.mergeAndProcessVehicleStructure(driver.getWlhyVehicleStructure(), driver.getSchedulerVehicleStructure(),
				driver.getExcludedVehicleStructure());
		mergedVehicleStructure = StringUtil.isEmpty(mergedVehicleStructure) ? null : new JSONArray(java.util.Arrays.asList(mergedVehicleStructure.split("、"))).toJSONString();
		driver.setSchedulerHaulway(TextUtil.transNull(mergedHaulway));
		driver.setSchedulerVehicleLength(TextUtil.transNull(mergedVehicleLengths));
		driver.setSchedulerGoods(mergedGoods);
		driver.setSchedulerVehicleStructure(mergedVehicleStructure);
		res.setDriver(driver);
		// 获取司机评价信息
		SchedulerDriverEvaluateTag schedulerDriverEvaluateTag = new SchedulerDriverEvaluateTag();
		schedulerDriverEvaluateTag.setDriverProfileId(driver.getId());
		schedulerDriverEvaluateTag.setType(1);
		schedulerDriverEvaluateTag.setState(0);
		List<SchedulerDriverEvaluateTag> evaluateList = schedulerDriverEvaluateTagMapper.selectSchedulerDriverEvaluateTagList(schedulerDriverEvaluateTag);
		res.setEvaluateList(evaluateList);
		// 获取司机标记信息
		schedulerDriverEvaluateTag.setType(2);
		List<SchedulerDriverEvaluateTag> tagList = schedulerDriverEvaluateTagMapper.selectSchedulerDriverEvaluateTagList(schedulerDriverEvaluateTag);
		if (null != tagList && !tagList.isEmpty()) {
			res.setTag(tagList.get(0));
		}
		return AjaxResult.success(res);
	}

	/**
	 * 查询司机画像列表
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 司机画像
	 */
	@Override
	public List<SchedulerDriverProfile> selectSchedulerDriverProfileList(SchedulerDriverProfileReq schedulerDriverProfile) {
		List<SchedulerDriverProfile> resList = new ArrayList<SchedulerDriverProfile>();
		if (null == schedulerDriverProfile) {
			return resList;
		}
		if (StringUtil.isNotEmpty(schedulerDriverProfile.getSchedulerVehicleLength())) {
			// 车辆长度单位转换为毫米
			schedulerDriverProfile.setSchedulerVehicleLength(
					new BigDecimal(schedulerDriverProfile.getSchedulerVehicleLength()).multiply(new BigDecimal(1000)).setScale(0, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
		}
		if ("1".equals(schedulerDriverProfile.getTelephoneState())) {
			resList = schedulerDriverProfileMapperEx.selectRepeatedSchedulerDriverProfileList(schedulerDriverProfile);
		} else {
			resList = schedulerDriverProfileMapperEx.selectSchedulerDriverProfileList(schedulerDriverProfile);
		}
		for (SchedulerDriverProfile res : resList) {
			// System.out.println(res.getExcludedVehicleStructure());
			// System.out.println(res.getExcludedVehicleLength());
			// System.out.println(res.getExcludedHaulway());
			// System.out.println(res.getExcludedGoods());
			String mergedHaulway = DriverPortraitMarkdownConverter.mergeHaulway(res.getWlhyHaulway(), res.getSchedulerHaulway(), res.getExcludedHaulway());
			mergedHaulway = StringUtil.isEmpty(mergedHaulway) ? null : new JSONArray(java.util.Arrays.asList(mergedHaulway.split("、"))).toJSONString();
			String mergedVehicleLengths = DriverPortraitMarkdownConverter.mergeAndProcessVehicleLengths(res.getWlhyVehicleLength(), res.getSchedulerVehicleLength(), res.getExcludedVehicleLength());
			mergedVehicleLengths = StringUtil.isEmpty(mergedVehicleLengths) ? null : new JSONArray(java.util.Arrays.asList(mergedVehicleLengths.split("、"))).toJSONString();
			String mergedGoods = DriverPortraitMarkdownConverter.mergeAndProcessGoods(res.getWlhyGoods(), res.getSchedulerGoods(), res.getExcludedGoods());
			mergedGoods = StringUtil.isEmpty(mergedGoods) ? null : new JSONArray(java.util.Arrays.asList(mergedGoods.split("、"))).toJSONString();
			String mergedVehicleStructure = DriverPortraitMarkdownConverter.mergeAndProcessVehicleStructure(res.getWlhyVehicleStructure(), res.getSchedulerVehicleStructure(),
					res.getExcludedVehicleStructure());
			mergedVehicleStructure = StringUtil.isEmpty(mergedVehicleStructure) ? null : new JSONArray(java.util.Arrays.asList(mergedVehicleStructure.split("、"))).toJSONString();
			res.setSchedulerHaulway(TextUtil.transNull(mergedHaulway));
			res.setSchedulerVehicleLength(TextUtil.transNull(mergedVehicleLengths));
			res.setSchedulerGoods(mergedGoods);
			res.setSchedulerVehicleStructure(mergedVehicleStructure);
			// 没有运力管理角色的需要脱敏显示
			// if (!resList.isEmpty() && !SecurityUtils.hasRole(Constants.ROLE_CAPACITY_MANAGER)) {
			// res.setSchedulerTelephone(DesensitizedUtil.mobilePhone(res.getSchedulerTelephone()));
			// res.setIdentityCard(DesensitizedUtil.idCardNum(res.getIdentityCard(), 3, 4));
			// }
		}
		return resList;
	}

	/**
	 * 新增司机画像
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 结果
	 */
	@Override
	public int insertSchedulerDriverProfile(SchedulerDriverProfile schedulerDriverProfile) {
		schedulerDriverProfile.setCreateTime(DateUtils.getNowDate());
		return schedulerDriverProfileMapper.insertSchedulerDriverProfile(schedulerDriverProfile);
	}

	/**
	 * 修改司机画像
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 结果
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult updateSchedulerDriverProfile(SchedulerDriverProfile schedulerDriverProfile) {
		if (null == schedulerDriverProfile || null == schedulerDriverProfile.getId()) {
			return AjaxResult.error("要修改的司机对象为空");
		}
		if (null != schedulerDriverProfile.getBlacklistStatus() && schedulerDriverProfile.getBlacklistStatus().intValue() == 1 && StringUtils.isBlank(schedulerDriverProfile.getBlackenReason())) {
			return AjaxResult.error("请输入加黑名单原因");
		}
		// -1表示清空手机号
		if ("-1".equals(schedulerDriverProfile.getSchedulerTelephone()) && StringUtils.isBlank(schedulerDriverProfile.getChangeTelReason())) {
			return AjaxResult.error("请输入更换手机号原因");
		}
		if (StringUtils.isNotBlank(schedulerDriverProfile.getSchedulerTelephone()) && !"-1".equals(schedulerDriverProfile.getSchedulerTelephone())
				&& StringUtils.isBlank(schedulerDriverProfile.getChangeTelReason())) {
			return AjaxResult.error("请输入更换手机号原因");
		}
		SchedulerDriverProfile driver = schedulerDriverProfileMapper.selectSchedulerDriverProfileById(schedulerDriverProfile.getId());
		if (null == driver) {
			return AjaxResult.error("没有找到对应的司机信息");
		}

		if (schedulerDriverProfile.getSchedulerVehicleStructure() != null){
			String originalSchedulerVehicleStructure = driver.getSchedulerVehicleStructure();// 库里的原始数据
			String schedulerVehicleStructure = schedulerDriverProfile.getSchedulerVehicleStructure();// 前端传过来的数据
			String originalExcludedVehicleStructure = driver.getExcludedVehicleStructure();// 库里原始要排除的数据
			String excludedVehicleStructureDate = processExcludedDate(originalSchedulerVehicleStructure, schedulerVehicleStructure, originalExcludedVehicleStructure);
			schedulerDriverProfile.setExcludedVehicleStructure(excludedVehicleStructureDate);
			System.out.println("车辆结构需要排除的值: " + schedulerDriverProfile.getExcludedVehicleStructure());
		}

		if (schedulerDriverProfile.getSchedulerVehicleLength() != null){
			String originalSchedulerVehicleLength = driver.getSchedulerVehicleLength();
			String schedulerVehicleLength = schedulerDriverProfile.getSchedulerVehicleLength();
			// 车辆长度单位转换为毫米
			if (StringUtils.isNotEmpty(schedulerVehicleLength)) {
				JSONArray jsonArray = JSON.parseArray(schedulerVehicleLength);
				List<String> result = jsonArray.parallelStream().map(item -> {
					String str = (String) item;
					return new BigDecimal(str).multiply(new BigDecimal(1000)).setScale(0, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
				}).filter(Objects::nonNull).collect(Collectors.toList());
				JSONArray schedulerVehicleLengthArray = new JSONArray(result);
				schedulerDriverProfile.setSchedulerVehicleLength(schedulerVehicleLengthArray.toJSONString());
			}
			String originalExcludedVehicleLength = driver.getExcludedVehicleLength();// 库里原始要排除的数据
			String excludedVehicleLengthDate = processExcludedDate(originalSchedulerVehicleLength, schedulerDriverProfile.getSchedulerVehicleLength(), originalExcludedVehicleLength);
			schedulerDriverProfile.setExcludedVehicleLength(excludedVehicleLengthDate);
			System.out.println("车辆长度需要排除的值: " + schedulerDriverProfile.getExcludedVehicleLength());
		}

		if (schedulerDriverProfile.getSchedulerGoods() != null){
			String originalSchedulerGoods = driver.getSchedulerGoods();
			String schedulerGoods = schedulerDriverProfile.getSchedulerGoods();
			String originalExcludedGoods = driver.getExcludedGoods();// 库里原始要排除的数据
			String excludedGoodsDate = processExcludedDate(originalSchedulerGoods, schedulerGoods, originalExcludedGoods);
			schedulerDriverProfile.setExcludedGoods(excludedGoodsDate);
			System.out.println("常运货物需要排除的值: " + schedulerDriverProfile.getExcludedGoods());
		}

		if (schedulerDriverProfile.getSchedulerHaulway() != null){
			String originalSchedulerHaulway = driver.getSchedulerHaulway();
			String schedulerHaulway = schedulerDriverProfile.getSchedulerHaulway();
			String originalExcludedHaulway = driver.getExcludedHaulway();// 库里原始要排除的数据
			String excludedHaulwayDate = processExcludedDate(originalSchedulerHaulway, schedulerHaulway, originalExcludedHaulway);
			schedulerDriverProfile.setExcludedHaulway(excludedHaulwayDate);
			System.out.println("常跑线路需要排除的值: " + schedulerDriverProfile.getExcludedHaulway());
		}
		schedulerDriverProfile.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
		schedulerDriverProfileMapperEx.updateSchedulerDriverProfile(schedulerDriverProfile);
		//表示清空手机号
		if ("-1".equals(schedulerDriverProfile.getSchedulerTelephone())){
			//记录日志
			String action = "删除运力,["+driver.getDriverName()+"] 手机号";
			sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_DRIVER_PROFILE, BusinessConstants.ACTION_TYPE_DELETE, action,String.valueOf(schedulerDriverProfile.getId()), Collections.singletonList(schedulerDriverProfile.getId()));
			//表示更换手机号
		}else if (StringUtils.isNotBlank(schedulerDriverProfile.getSchedulerTelephone()) && StringUtils.isNotBlank(schedulerDriverProfile.getChangeTelReason())){
			//记录日志
			String action = "修改运力,["+driver.getDriverName()+"] 手机号";
			sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_DRIVER_PROFILE, BusinessConstants.ACTION_TYPE_UPDATE, action,String.valueOf(schedulerDriverProfile.getId()), Collections.singletonList(schedulerDriverProfile.getId()));
		}else if (schedulerDriverProfile.getBlacklistStatus() !=null && schedulerDriverProfile.getBlacklistStatus() == 1){
			Integer cooperativeStatus = driver.getCooperativeStatus();
			String action;
			if (cooperativeStatus!=null && cooperativeStatus == 1){
				action = "修改运力,["+driver.getDriverName()+ driver.getIdentityCard()+"] 身份状态，从运力池设为黑名单";
			}else {
				action = "修改运力,["+driver.getDriverName()+ driver.getIdentityCard()+"] 身份状态，从合作司机设为黑名单";
			}
			sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_FREQUENT_CARGO, BusinessConstants.ACTION_TYPE_ADD, StringUtils.substring(action, 0, 5000),String.valueOf(schedulerDriverProfile.getId()));
		}else if (schedulerDriverProfile.getBlacklistStatus() !=null && schedulerDriverProfile.getBlacklistStatus() == 0){
			String action = "修改运力,["+driver.getDriverName()+ driver.getIdentityCard()+"] 身份状态，取消黑名单";
			sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_FREQUENT_CARGO, BusinessConstants.ACTION_TYPE_ADD, StringUtils.substring(action, 0, 5000),String.valueOf(schedulerDriverProfile.getId()));
		}else if (schedulerDriverProfile.getCooperativeStatus() != null && schedulerDriverProfile.getCooperativeStatus() == 1){
			String action = "修改运力,["+driver.getDriverName()+ driver.getIdentityCard()+"] 身份状态，从运力池设为合作司机";
			sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_FREQUENT_CARGO, BusinessConstants.ACTION_TYPE_ADD, StringUtils.substring(action, 0, 5000),String.valueOf(schedulerDriverProfile.getId()));
		} else {
			//表示更新运力信息
			String action = "修改运力,["+driver.getDriverName()+ driver.getIdentityCard()+"] 运力信息";
			sysClientLogService.insertLog(BusinessConstants.ACTION_UPDATE_DRIVER_PROFILE, BusinessConstants.ACTION_TYPE_UPDATE, action,String.valueOf(schedulerDriverProfile.getId()), Collections.singletonList(schedulerDriverProfile.getId()));
		}
		return AjaxResult.success();
	}

	/**
	 * 批量删除司机画像
	 * 
	 * @param ids
	 *            需要删除的司机画像主键
	 * @return 结果
	 */
	@Override
	public int deleteSchedulerDriverProfileByIds(Long[] ids) {
		return schedulerDriverProfileMapper.deleteSchedulerDriverProfileByIds(ids);
	}

	/**
	 * 删除司机画像信息
	 * 
	 * @param id
	 *            司机画像主键
	 * @return 结果
	 */
	@Override
	public int deleteSchedulerDriverProfileById(Long id) {
		return schedulerDriverProfileMapper.deleteSchedulerDriverProfileById(id);
	}

	private String processExcludedDate(String originalSchedulerJson, String schedulerJson, String originalExcludedJson) {
		// 1. 安全解析JSON（处理空值和无效JSON）
		List<String> originalSchedulerList = parseJsonSafe(originalSchedulerJson);
		List<String> schedulerList = parseJsonSafe(schedulerJson);
		List<String> originalExcludedList = parseJsonSafe(originalExcludedJson);

		// 2. 创建scheduler的快速查询Set
		Set<String> schedulerSet = new LinkedHashSet<>(schedulerList);

		// 3. 第一步：过滤originalExcluded中存在于scheduler的值
		List<String> step1Result = originalExcludedList.stream().filter(item -> item != null && !schedulerSet.contains(item)).collect(Collectors.toList());

		// 4. 第二步：添加originalScheduler中存在但scheduler中没有的值
		Set<String> finalSet = new LinkedHashSet<>(step1Result);
		originalSchedulerList.stream().filter(item -> item != null && !schedulerSet.contains(item)).forEach(finalSet::add);

		// 5. 处理空值情况
		if (finalSet.isEmpty()) {
			return new JSONArray(Collections.emptyList()).toJSONString();
		}

		// 6. 保持顺序并返回
		return new JSONArray(finalSet).toJSONString();
	}

	/**
	 * 安全解析JSON数组（处理空值/无效格式）
	 */
	private List<String> parseJsonSafe(String json) {
		try {
			if (json == null || json.trim().isEmpty()) {
				return Collections.emptyList();
			}
			return JSON.parseArray(json, String.class);
		} catch (Exception e) {
			System.err.println("JSON解析异常: " + e.getMessage());
			return Collections.emptyList();
		}
	}

	public static void main(String[] args) {
		String mergedHaulway = DriverPortraitMarkdownConverter.mergeHaulway("[\"上海市-南通市\", \"江苏省常州市-江苏省盐城市\", \"江苏省泰州市-江苏省南京市\"]", null, "[\"上海市-南通市\", \"江苏省泰州市-江苏省南京市\"]");
		System.out.print(mergedHaulway);
	}
}
