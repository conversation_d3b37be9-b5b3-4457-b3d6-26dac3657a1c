package com.zly.project.driver.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.driver.domain.SchedulerDriverProfile;
import com.zly.project.driver.domain.req.SchedulerDriverProfileReq;
import com.zly.project.driver.service.ISchedulerDriverProfileService;

import io.swagger.annotations.ApiOperation;

/**
 * 司机画像Controller
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@RestController
@RequestMapping("/driver/profile")
public class SchedulerDriverProfileController extends BaseController {
	@Autowired
	private ISchedulerDriverProfileService schedulerDriverProfileService;

	/**
	 * 查询司机画像列表
	 */
	@ApiOperation("查询合作司机、运力池列表，合作司机cooperativeStatus参数传1，运力池不传")
	@PreAuthorize("@ss.hasPermi('driver:profile:list')")
	@PostMapping("/list")
	public TableDataInfo list(@RequestBody SchedulerDriverProfileReq schedulerDriverProfile) {
		startPage();
		List<SchedulerDriverProfile> list = schedulerDriverProfileService.selectSchedulerDriverProfileList(schedulerDriverProfile);
		return getDataTable(list);
	}

	/**
	 * 导出司机画像列表
	 */
	@PreAuthorize("@ss.hasPermi('driver:profile:export')")
	@Log(title = "司机画像", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, SchedulerDriverProfileReq schedulerDriverProfile) {
		List<SchedulerDriverProfile> list = schedulerDriverProfileService.selectSchedulerDriverProfileList(schedulerDriverProfile);
		ExcelUtil<SchedulerDriverProfile> util = new ExcelUtil<SchedulerDriverProfile>(SchedulerDriverProfile.class);
		util.exportExcel(response, list, "司机画像数据");
	}

	/**
	 * 获取司机画像详细信息
	 */
	@ApiOperation("司机详情")
	@PreAuthorize("@ss.hasPermi('driver:profile:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return schedulerDriverProfileService.selectSchedulerDriverProfileById(id);
	}

	/**
	 * 新增司机画像
	 */
	@PreAuthorize("@ss.hasPermi('driver:profile:add')")
	@Log(title = "司机画像", businessType = BusinessType.INSERT)
	@PostMapping
	public AjaxResult add(@RequestBody SchedulerDriverProfile schedulerDriverProfile) {
		return toAjax(schedulerDriverProfileService.insertSchedulerDriverProfile(schedulerDriverProfile));
	}

	/**
	 * 修改司机画像
	 */
	@ApiOperation("修改司机信息:1.更换手机号 2.更新运力信息 3.取消合作 4.设为合作司机 5.设为黑名单 6.取消黑名单")
	@PreAuthorize("@ss.hasPermi('driver:profile:edit')")
	@Log(title = "司机画像", businessType = BusinessType.UPDATE)
	@PutMapping
	public AjaxResult edit(@RequestBody SchedulerDriverProfile schedulerDriverProfile) {
		return schedulerDriverProfileService.updateSchedulerDriverProfile(schedulerDriverProfile);
	}

	/**
	 * 删除司机画像
	 */
	@PreAuthorize("@ss.hasPermi('driver:profile:remove')")
	@Log(title = "司机画像", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult remove(@PathVariable Long[] ids) {
		return toAjax(schedulerDriverProfileService.deleteSchedulerDriverProfileByIds(ids));
	}
}
