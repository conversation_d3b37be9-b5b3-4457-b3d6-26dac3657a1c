package com.zly.project.driver.mapper;

import java.util.List;

import com.zly.project.driver.domain.SchedulerBlacklist;

/**
 * 黑名单配置Mapper扩展接口
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface SchedulerBlacklistMapperEx {
	/**
	 * 批量新增黑名单配置
	 * 
	 * @param schedulerBlacklist
	 *            黑名单配置集合
	 * @return 结果
	 */
	public int insertBatchSchedulerBlacklist(List<SchedulerBlacklist> schedulerBlacklist);

	/**
	 * 按手机号批量删除黑名单配置
	 * 
	 * @param blackMembers
	 *            需要删除的手机号集合
	 * @return 结果
	 */
	public int deleteSchedulerBlacklistByBlackMembers(List<String> blackMembers);
}
