package com.zly.project.driver.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.TextUtil;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.driver.domain.SchedulerBlacklist;
import com.zly.project.driver.mapper.SchedulerBlacklistMapper;
import com.zly.project.driver.mapper.SchedulerBlacklistMapperEx;
import com.zly.project.driver.service.ISchedulerBlacklistService;

/**
 * 黑名单配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@Service
public class SchedulerBlacklistServiceImpl implements ISchedulerBlacklistService {

	@Autowired
	private SchedulerBlacklistMapper schedulerBlacklistMapper;

	@Autowired
	private SchedulerBlacklistMapperEx schedulerBlacklistMapperEx;

	/**
	 * 查询黑名单配置
	 * 
	 * @param id
	 *            黑名单配置主键
	 * @return 黑名单配置
	 */
	@Override
	public SchedulerBlacklist selectSchedulerBlacklistById(Long id) {
		return schedulerBlacklistMapper.selectSchedulerBlacklistById(id);
	}

	/**
	 * 查询黑名单配置列表
	 * 
	 * @param schedulerBlacklist
	 *            黑名单配置
	 * @return 黑名单配置
	 */
	@Override
	public List<SchedulerBlacklist> selectSchedulerBlacklistList(SchedulerBlacklist schedulerBlacklist) {
		return schedulerBlacklistMapper.selectSchedulerBlacklistList(schedulerBlacklist);
	}

	/**
	 * 新增黑名单配置
	 * 
	 * @param schedulerBlacklist
	 *            黑名单配置
	 * @return 结果
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult insertSchedulerBlacklist(SchedulerBlacklist schedulerBlacklist) {
		if (null == schedulerBlacklist || StringUtils.isBlank(schedulerBlacklist.getBlackMember())) {
			return AjaxResult.error("黑名单手机号为空");
		}
		List<String> list = Arrays.stream(schedulerBlacklist.getBlackMember().split(",")).map(String::trim).distinct()
				.collect(Collectors.toList());
		List<SchedulerBlacklist> insertList = new ArrayList<SchedulerBlacklist>();
		for (String blackMember : list) {
			SchedulerBlacklist sb = new SchedulerBlacklist();
			sb.setId(TextUtil.getTimeSequenceID(5));
			sb.setBlackMember(blackMember);
			sb.setCreateBy(SecurityUtils.getLoginUser().getUsername());
			sb.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
			insertList.add(sb);
		}
		schedulerBlacklistMapperEx.deleteSchedulerBlacklistByBlackMembers(list);
		schedulerBlacklistMapperEx.insertBatchSchedulerBlacklist(insertList);
		return AjaxResult.success();
	}

	/**
	 * 修改黑名单配置
	 * 
	 * @param schedulerBlacklist
	 *            黑名单配置
	 * @return 结果
	 */
	@Override
	public int updateSchedulerBlacklist(SchedulerBlacklist schedulerBlacklist) {
		return schedulerBlacklistMapper.updateSchedulerBlacklist(schedulerBlacklist);
	}

	/**
	 * 批量删除黑名单配置
	 * 
	 * @param ids
	 *            需要删除的黑名单配置主键
	 * @return 结果
	 */
	@Override
	public int deleteSchedulerBlacklistByIds(Long[] ids) {
		return schedulerBlacklistMapper.deleteSchedulerBlacklistByIds(ids);
	}

	/**
	 * 删除黑名单配置信息
	 * 
	 * @param id
	 *            黑名单配置主键
	 * @return 结果
	 */
	@Override
	public int deleteSchedulerBlacklistById(Long id) {
		return schedulerBlacklistMapper.deleteSchedulerBlacklistById(id);
	}
}
