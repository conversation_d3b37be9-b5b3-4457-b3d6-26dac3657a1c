package com.zly.project.driver.service;

import java.util.List;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.driver.domain.SchedulerDriverEvaluateTag;

/**
 * 司机评价、标记Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface ISchedulerDriverEvaluateTagService {
	/**
	 * 查询司机评价、标记
	 * 
	 * @param id
	 *            司机评价、标记主键
	 * @return 司机评价、标记
	 */
	public SchedulerDriverEvaluateTag selectSchedulerDriverEvaluateTagById(Long id);

	/**
	 * 查询司机评价、标记列表
	 * 
	 * @param schedulerDriverEvaluateTag
	 *            司机评价、标记
	 * @return 司机评价、标记集合
	 */
	public List<SchedulerDriverEvaluateTag> selectSchedulerDriverEvaluateTagList(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag);

	/**
	 * 新增司机评价、标记
	 * 
	 * @param schedulerDriverEvaluateTag
	 *            司机评价、标记
	 * @return 结果
	 */
	public AjaxResult insertSchedulerDriverEvaluateTag(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag);

	/**
	 * 修改司机评价、标记
	 * 
	 * @param schedulerDriverEvaluateTag
	 *            司机评价、标记
	 * @return 结果
	 */
	public int updateSchedulerDriverEvaluateTag(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag);

	/**
	 * 批量删除司机评价、标记
	 * 
	 * @param ids
	 *            需要删除的司机评价、标记主键集合
	 * @return 结果
	 */
	public int deleteSchedulerDriverEvaluateTagByIds(Long[] ids);

	/**
	 * 删除司机评价、标记信息
	 * 
	 * @param id
	 *            司机评价、标记主键
	 * @return 结果
	 */
	public int deleteSchedulerDriverEvaluateTagById(Long id);
}
