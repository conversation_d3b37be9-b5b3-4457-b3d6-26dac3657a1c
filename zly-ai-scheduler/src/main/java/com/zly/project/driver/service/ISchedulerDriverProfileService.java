package com.zly.project.driver.service;

import java.util.List;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.driver.domain.SchedulerDriverProfile;
import com.zly.project.driver.domain.req.SchedulerDriverProfileReq;

/**
 * 司机画像Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface ISchedulerDriverProfileService {
	/**
	 * 查询司机画像
	 * 
	 * @param id
	 *            司机画像主键
	 * @return 司机画像
	 */
	public AjaxResult selectSchedulerDriverProfileById(Long id);

	/**
	 * 查询司机画像列表
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 司机画像集合
	 */
	public List<SchedulerDriverProfile> selectSchedulerDriverProfileList(SchedulerDriverProfileReq schedulerDriverProfile);

	/**
	 * 新增司机画像
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 结果
	 */
	public int insertSchedulerDriverProfile(SchedulerDriverProfile schedulerDriverProfile);

	/**
	 * 修改司机画像
	 * 
	 * @param schedulerDriverProfile
	 *            司机画像
	 * @return 结果
	 */
	public AjaxResult updateSchedulerDriverProfile(SchedulerDriverProfile schedulerDriverProfile);

	/**
	 * 批量删除司机画像
	 * 
	 * @param ids
	 *            需要删除的司机画像主键集合
	 * @return 结果
	 */
	public int deleteSchedulerDriverProfileByIds(Long[] ids);

	/**
	 * 删除司机画像信息
	 * 
	 * @param id
	 *            司机画像主键
	 * @return 结果
	 */
	public int deleteSchedulerDriverProfileById(Long id);
}
