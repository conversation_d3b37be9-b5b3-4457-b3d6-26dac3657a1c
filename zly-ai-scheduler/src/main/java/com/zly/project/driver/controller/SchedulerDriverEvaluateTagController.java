package com.zly.project.driver.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.driver.domain.SchedulerDriverEvaluateTag;
import com.zly.project.driver.service.ISchedulerDriverEvaluateTagService;

import io.swagger.annotations.ApiOperation;

/**
 * 司机评价、标记Controller
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@RestController
@RequestMapping("/driver/tag")
public class SchedulerDriverEvaluateTagController extends BaseController {
	@Autowired
	private ISchedulerDriverEvaluateTagService schedulerDriverEvaluateTagService;

	/**
	 * 查询司机评价、标记列表
	 */
	@PreAuthorize("@ss.hasPermi('driver:tag:list')")
	@GetMapping("/list")
	public TableDataInfo list(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag) {
		startPage();
		List<SchedulerDriverEvaluateTag> list = schedulerDriverEvaluateTagService.selectSchedulerDriverEvaluateTagList(schedulerDriverEvaluateTag);
		return getDataTable(list);
	}

	/**
	 * 导出司机评价、标记列表
	 */
	@PreAuthorize("@ss.hasPermi('driver:tag:export')")
	@Log(title = "司机评价、标记", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, SchedulerDriverEvaluateTag schedulerDriverEvaluateTag) {
		List<SchedulerDriverEvaluateTag> list = schedulerDriverEvaluateTagService.selectSchedulerDriverEvaluateTagList(schedulerDriverEvaluateTag);
		ExcelUtil<SchedulerDriverEvaluateTag> util = new ExcelUtil<SchedulerDriverEvaluateTag>(SchedulerDriverEvaluateTag.class);
		util.exportExcel(response, list, "司机评价、标记数据");
	}

	/**
	 * 获取司机评价、标记详细信息
	 */
	@PreAuthorize("@ss.hasPermi('driver:tag:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(schedulerDriverEvaluateTagService.selectSchedulerDriverEvaluateTagById(id));
	}

	/**
	 * 新增司机评价、标记
	 */
	@ApiOperation("新增司机评价、标记")
	@PreAuthorize("@ss.hasPermi('driver:tag:add')")
	@Log(title = "司机评价、标记", businessType = BusinessType.INSERT)
	@PostMapping
	public AjaxResult add(@RequestBody SchedulerDriverEvaluateTag schedulerDriverEvaluateTag) {
		return schedulerDriverEvaluateTagService.insertSchedulerDriverEvaluateTag(schedulerDriverEvaluateTag);
	}

	/**
	 * 修改司机评价、标记
	 */
	@PreAuthorize("@ss.hasPermi('driver:tag:edit')")
	@Log(title = "司机评价、标记", businessType = BusinessType.UPDATE)
	@PutMapping
	public AjaxResult edit(@RequestBody SchedulerDriverEvaluateTag schedulerDriverEvaluateTag) {
		return toAjax(schedulerDriverEvaluateTagService.updateSchedulerDriverEvaluateTag(schedulerDriverEvaluateTag));
	}

	/**
	 * 删除司机评价、标记
	 */
	@PreAuthorize("@ss.hasPermi('driver:tag:remove')")
	@Log(title = "司机评价、标记", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult remove(@PathVariable Long[] ids) {
		return toAjax(schedulerDriverEvaluateTagService.deleteSchedulerDriverEvaluateTagByIds(ids));
	}
}
