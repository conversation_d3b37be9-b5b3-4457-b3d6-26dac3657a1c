package com.zly.project.driver.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@ApiModel(description = "管理后台 - 司机画像分页 Response VO")
@Data
public class DriverProfilePageRespVO {

    @ApiModelProperty(value = "总记录数")
    private Integer total;

    @ApiModelProperty(value = "司机画像列表")
    private List<DriverProfileRespVO> rows;

    @ApiModelProperty(value = "状态码", example = "200")
    private Integer code;

    @ApiModelProperty(value = "消息", example = "查询成功")
    private String msg;
}