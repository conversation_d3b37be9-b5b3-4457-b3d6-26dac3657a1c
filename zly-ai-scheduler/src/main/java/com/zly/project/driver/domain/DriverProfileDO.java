package com.zly.project.driver.domain;

import com.zly.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 司机画像 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DriverProfileDO extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 司机ID
     */
    private String driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 电话号码
     */
    private String telephone;

    /**
     * 调度中心这边的司机手机号
     */
    private String schedulerTelephone;

    /**
     * 身份证号
     */
    private String identityCard;

    /**
     * 驾龄(年)
     */
    private Integer drivingYears;

    /**
     * 资料完整度(%)
     */
    private Integer profileCompleteness;

    /**
     * 服务次数
     */
    private Integer serviceCount;

    /**
     * 总里程
     */
    private String totalCourse;

    /**
     * 司机来源(1:PC 2:微信小程序 3:司机名片推送 4:数据迁移 0:未知)
     */
    private Integer operation;

    /**
     * 司机来源描述
     */
    private String operationDesc;

    /**
     * 驾驶证是否有效
     */
    private Integer isTheDrivrLicenseValid;

    /**
     * 驾驶证类型
     */
    private String vehicleClass;

    /**
     * 驾驶证有效期
     */
    private String validPeriodTo;

    /**
     * 从业资格证
     */
    private String qualificationCertificate;

    /**
     * 从业资格证是否有效
     */
    private Integer isTheQualificationValid;

    /**
     * 实际承运截止日期
     */
    private String actualCarrierEnd;

    /**
     * 最近服务时间
     */
    private Date lastServiceTime;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 地址
     */
    private String address;

    /**
     * 车辆数据JSON
     */
    private String vehicles;

    /**
     * 常跑路线JSON
     */
    private String longDistanceRoutes;

    /**
     * 合作状态(0:否 1:是)
     */
    private Integer cooperativeStatus;

    /**
     * 黑名单状态(0:否 1:是)
     */
    private Integer blacklistStatus;

    /**
     * 综合评价
     */
    private String overallEvaluate;

    /**
     * 调度中心这边的车辆结构 (JSON)
     */
    private String schedulerVehicleStructure;

    /**
     * 4.0系统那边的车辆结构 (JSON)
     */
    private String wlhyVehicleStructure;

    /**
     * 需要排除的车辆结构 (JSON)
     */
    private String excludedVehicleStructure;

    /**
     * 调度中心这边的车辆长度 (JSON)
     */
    private String schedulerVehicleLength;

    /**
     * 4.0系统那边的车辆长度 (JSON)
     */
    private String wlhyVehicleLength;

    /**
     * 需要排除的车辆长度 (JSON)
     */
    private String excludedVehicleLength;

    /**
     * 调度中心这边的常运货物 (JSON)
     */
    private String schedulerGoods;

    /**
     * 4.0系统那边的常运货物 (JSON)
     */
    private String wlhyGoods;

    /**
     * 需要排除的常运货物 (JSON)
     */
    private String excludedGoods;

    /**
     * 调度中心这边的常跑线路 (JSON)
     */
    private String schedulerHaulway;

    /**
     * 4.0系统那边的常跑线路 (JSON)
     */
    private String wlhyHaulway;

    /**
     * 需要排除的常跑线路 (JSON)
     */
    private String excludedHaulway;

    /**
     * 加黑原因
     */
    private String blackenReason;

    /**
     * 更换手机号原因
     */
    private String changeTelReason;

    /**
     * 司机评价
     */
    private String driverRating;

    /**
     * 司机标记
     */
    private String driverTag;

    /** 货物重量 */
    private BigDecimal goodsWeight;

    /** 货物体积 */
    private BigDecimal goodsVolume;

}