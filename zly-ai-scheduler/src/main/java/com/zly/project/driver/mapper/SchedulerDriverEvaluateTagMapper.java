package com.zly.project.driver.mapper;

import java.util.List;
import com.zly.project.driver.domain.SchedulerDriverEvaluateTag;

/**
 * 司机评价、标记Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface SchedulerDriverEvaluateTagMapper 
{
    /**
     * 查询司机评价、标记
     * 
     * @param id 司机评价、标记主键
     * @return 司机评价、标记
     */
    public SchedulerDriverEvaluateTag selectSchedulerDriverEvaluateTagById(Long id);

    /**
     * 查询司机评价、标记列表
     * 
     * @param schedulerDriverEvaluateTag 司机评价、标记
     * @return 司机评价、标记集合
     */
    public List<SchedulerDriverEvaluateTag> selectSchedulerDriverEvaluateTagList(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag);

    /**
     * 新增司机评价、标记
     * 
     * @param schedulerDriverEvaluateTag 司机评价、标记
     * @return 结果
     */
    public int insertSchedulerDriverEvaluateTag(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag);

    /**
     * 修改司机评价、标记
     * 
     * @param schedulerDriverEvaluateTag 司机评价、标记
     * @return 结果
     */
    public int updateSchedulerDriverEvaluateTag(SchedulerDriverEvaluateTag schedulerDriverEvaluateTag);

    /**
     * 删除司机评价、标记
     * 
     * @param id 司机评价、标记主键
     * @return 结果
     */
    public int deleteSchedulerDriverEvaluateTagById(Long id);

    /**
     * 批量删除司机评价、标记
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchedulerDriverEvaluateTagByIds(Long[] ids);
}
