package com.zly.project.driver.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;

import io.swagger.annotations.ApiModelProperty;

/**
 * 司机评价、标记对象 scheduler_driver_evaluate_tag
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public class SchedulerDriverEvaluateTag extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**  */
	@Excel(name = "")
	private Long id;

	/** 类型（1.评价 2.标记） */
	@Excel(name = "类型", readConverterExp = "1=.评价,2=.标记")
	@ApiModelProperty(value = "类型(0:评价 1:标记)", required = false)
	private Integer type;

	/** 操作来源(1:调度任务 2:合作司机 3:运力池 4:黑名单) */
	@Excel(name = "操作来源(1:调度任务 2:合作司机 3:运力池 4:黑名单)")
	@ApiModelProperty(value = "评论来源(1:调度任务 2:合作司机 3:运力池 4:黑名单)", required = false)
	private Integer operation;

	/** 司机画像ID */
	@Excel(name = "司机画像ID")
	private Long driverProfileId;

	/** 内容 */
	@Excel(name = "内容")
	@ApiModelProperty(value = "评价或标记内容", required = false)
	private String content;

	/** 状态（0.正常 1.停用） */
	@Excel(name = "状态", readConverterExp = "0=.正常,1=.停用")
	private Integer state;

	/** 备注 */
	@Excel(name = "备注")
	@ApiModelProperty(value = "备注", required = false)
	private String remark;

	public void setId(Long id) {
		this.id = id;
	}

	public Long getId() {
		return id;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getType() {
		return type;
	}

	public void setOperation(Integer operation) {
		this.operation = operation;
	}

	public Integer getOperation() {
		return operation;
	}

	public void setDriverProfileId(Long driverProfileId) {
		this.driverProfileId = driverProfileId;
	}

	public Long getDriverProfileId() {
		return driverProfileId;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getContent() {
		return content;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return state;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("type", getType()).append("operation", getOperation())
				.append("driverProfileId", getDriverProfileId()).append("content", getContent()).append("state", getState()).append("remark", getRemark()).append("createBy", getCreateBy())
				.append("createTime", getCreateTime()).append("updateBy", getUpdateBy()).append("updateTime", getUpdateTime()).toString();
	}
}
