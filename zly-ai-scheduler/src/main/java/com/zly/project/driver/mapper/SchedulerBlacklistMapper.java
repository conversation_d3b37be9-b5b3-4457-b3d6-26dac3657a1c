package com.zly.project.driver.mapper;

import java.util.List;
import com.zly.project.driver.domain.SchedulerBlacklist;

/**
 * 黑名单配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface SchedulerBlacklistMapper 
{
    /**
     * 查询黑名单配置
     * 
     * @param id 黑名单配置主键
     * @return 黑名单配置
     */
    public SchedulerBlacklist selectSchedulerBlacklistById(Long id);

    /**
     * 查询黑名单配置列表
     * 
     * @param schedulerBlacklist 黑名单配置
     * @return 黑名单配置集合
     */
    public List<SchedulerBlacklist> selectSchedulerBlacklistList(SchedulerBlacklist schedulerBlacklist);

    /**
     * 新增黑名单配置
     * 
     * @param schedulerBlacklist 黑名单配置
     * @return 结果
     */
    public int insertSchedulerBlacklist(SchedulerBlacklist schedulerBlacklist);

    /**
     * 修改黑名单配置
     * 
     * @param schedulerBlacklist 黑名单配置
     * @return 结果
     */
    public int updateSchedulerBlacklist(SchedulerBlacklist schedulerBlacklist);

    /**
     * 删除黑名单配置
     * 
     * @param id 黑名单配置主键
     * @return 结果
     */
    public int deleteSchedulerBlacklistById(Long id);

    /**
     * 批量删除黑名单配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchedulerBlacklistByIds(Long[] ids);
}
