package com.zly.project.driver.domain;

import java.util.Date;

import com.zly.framework.aspectj.lang.annotation.ClientLog;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;

import io.swagger.annotations.ApiModelProperty;

/**
 * 司机画像对象 scheduler_driver_profile
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public class SchedulerDriverProfile extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/** 主键ID */
	@ApiModelProperty(value = "主键ID")
	@Excel(name = "主键ID")
	private Long id;

	/** 司机ID */
	@ApiModelProperty(value = "司机ID")
	@Excel(name = "司机ID")
	private String driverId;

	/** 司机姓名 */
	@Excel(name = "司机姓名")
	@ApiModelProperty(value = "司机姓名", required = false)
	private String driverName;

	/** 电话号码 */
	@ApiModelProperty(value = "电话号码")
	@Excel(name = "电话号码")
	private String telephone;

	/** 调度中心这边的司机手机号 */
	/** 联系电话，查询为空号码或指定的号码时传参，不用按此条件查询时不传参 */
	@Excel(name = "调度中心这边的司机手机号")
	@ApiModelProperty(value = "调度手机号", required = false)
	private String schedulerTelephone;

	/** 身份证号 */
	@Excel(name = "身份证号")
	@ApiModelProperty(value = "身份证号", required = false)
	private String identityCard;

	/** 驾龄(年) */
	@ApiModelProperty(value = "驾龄(年)")
	@Excel(name = "驾龄(年)")
	private Long drivingYears;

	/** 资料完整度(%) */
	@ApiModelProperty(value = "资料完整度(%)")
	@Excel(name = "资料完整度(%)")
	private Long profileCompleteness;

	/** 服务次数 */
	@ApiModelProperty(value = "服务次数")
	@Excel(name = "服务次数")
	private Long serviceCount;

	/** 总里程 */
	@ApiModelProperty(value = "总里程")
	@Excel(name = "总里程")
	private String totalCourse;

	/** 司机来源(1:PC 2:微信小程序 3:司机名片推送 4:数据迁移 0:未知) */
	@ApiModelProperty(value = "司机来源(1:PC 2:微信小程序 3:司机名片推送 4:数据迁移 0:未知)")
	@Excel(name = "司机来源(1:PC 2:微信小程序 3:司机名片推送 4:数据迁移 0:未知)")
	private Integer operation;

	/** 司机来源描述 */
	@ApiModelProperty(value = "司机来源描述")
	@Excel(name = "司机来源描述")
	private String operationDesc;

	/** 驾驶证是否有效 */
	@ApiModelProperty(value = "驾驶证是否有效")
	@Excel(name = "驾驶证是否有效")
	private Integer isTheDrivrLicenseValid;

	/** 驾驶证类型 */
	@ApiModelProperty(value = "驾驶证类型")
	@Excel(name = "驾驶证类型")
	private String vehicleClass;

	/** 驾驶证有效期 */
	@ApiModelProperty(value = "驾驶证有效期")
	@Excel(name = "驾驶证有效期")
	private String validPeriodTo;

	/** 从业资格证 */
	@ApiModelProperty(value = "从业资格证")
	@Excel(name = "从业资格证")
	private String qualificationCertificate;

	/** 从业资格证是否有效 */
	@ApiModelProperty(value = "从业资格证是否有效")
	@Excel(name = "从业资格证是否有效")
	private Integer isTheQualificationValid;

	/** 实际承运截止日期 */
	@ApiModelProperty(value = "实际承运截止日期")
	@Excel(name = "实际承运截止日期")
	private String actualCarrierEnd;

	/** 最近服务时间 */
	@ApiModelProperty(value = "最近服务时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@Excel(name = "最近服务时间", width = 30, dateFormat = "yyyy-MM-dd")
	private Date lastServiceTime;

	/** 状态 */
	@Excel(name = "状态")
	@ApiModelProperty(value = "状态(0:正常 1:黑名单)", required = false)
	private Integer state;

	/** 地址 */
	@ApiModelProperty(value = "地址")
	@Excel(name = "地址")
	private String address;

	/** 车辆数据JSON */
	@ApiModelProperty(value = "车辆数据JSON")
	@Excel(name = "车辆数据JSON")
	private String vehicles;

	/** 常跑路线JSON */
	@ApiModelProperty(value = "常跑路线JSON")
	@Excel(name = "常跑路线JSON")
	private String longDistanceRoutes;

	/** 合作状态(0:否 1:是) */
	@Excel(name = "合作状态(0:否 1:是)")
	@ApiModelProperty(value = "合作状态(0:否 1:是)", required = false)
	private Integer cooperativeStatus;

	/** 黑名单状态(0:否 1:是) */
	@ApiModelProperty(value = "黑名单状态(0:否 1:是)")
	@Excel(name = "黑名单状态(0:否 1:是)")
	private Integer blacklistStatus;

	/** 综合评价 */
	@Excel(name = "综合评价")
	@ApiModelProperty(value = "综合评价", required = false)
	private String overallEvaluate;

	/** 调度中心这边的车辆结构 */
	@Excel(name = "调度中心这边的车辆结构")
	@ApiModelProperty(value = "车型", required = false)
	private String schedulerVehicleStructure;

	/** 4.0系统那边的车辆结构 */
	@ApiModelProperty(value = "4.0系统那边的车辆结构")
	@Excel(name = "4.0系统那边的车辆结构")
	private String wlhyVehicleStructure;

	/** 需要排除的车辆结构 */
	@ClientLog
	@ApiModelProperty(value = "需要排除的车辆结构")
	@Excel(name = "需要排除的车辆结构")
	private String excludedVehicleStructure;

	/** 调度中心这边的车辆长度 */
	@Excel(name = "调度中心这边的车辆长度")
	@ApiModelProperty(value = "车辆长度", required = false)
	private String schedulerVehicleLength;

	/** 4.0系统那边的车辆长度 */
	@ApiModelProperty(value = "4.0系统那边的车辆长度")
	@Excel(name = "4.0系统那边的车辆长度")
	private String wlhyVehicleLength;

	/** 需要排除的车辆长度 */
	@ClientLog
	@ApiModelProperty(value = "需要排除的车辆长度")
	@Excel(name = "需要排除的车辆长度")
	private String excludedVehicleLength;

	/** 调度中心这边的常运货物 */
	@Excel(name = "调度中心这边的常运货物")
	@ApiModelProperty(value = "货物名称", required = false)
	private String schedulerGoods;

	/** 4.0系统那边的常运货物 */
	@ApiModelProperty(value = "4.0系统那边的常运货物")
	@Excel(name = "4.0系统那边的常运货物")
	private String wlhyGoods;

	/** 需要排除的常运货物 */
	@ClientLog
	@ApiModelProperty(value = "需要排除的常运货物")
	@Excel(name = "需要排除的常运货物")
	private String excludedGoods;

	/** 调度中心这边的常跑线路 */
	@Excel(name = "调度中心这边的常跑线路")
	@ApiModelProperty(value = "调度中心这边的常跑线路")
	private String schedulerHaulway;

	/** 4.0系统那边的常跑线路 */
	@ApiModelProperty(value = "4.0系统那边的常跑线路")
	@Excel(name = "4.0系统那边的常跑线路")
	private String wlhyHaulway;

	/** 需要排除的常跑线路 */
	@ClientLog
	@ApiModelProperty(value = "需要排除的常跑线路")
	@Excel(name = "需要排除的常跑线路")
	private String excludedHaulway;

	/** 加黑原因 */
	@ApiModelProperty(value = "加黑原因")
	@Excel(name = "加黑原因")
	private String blackenReason;

	/** 更换手机号原因 */
	@ApiModelProperty(value = "更换手机号原因")
	@Excel(name = "更换手机号原因")
	private String changeTelReason;

	/** 司机评价 */
	@ApiModelProperty(value = "司机评价")
	@Excel(name = "司机评价")
	private String driverRating;

	/** 司机标记 */
	@ApiModelProperty(value = "司机标记")
	@Excel(name = "司机标记")
	private String driverTag;

	public void setId(Long id) {
		this.id = id;
	}

	public Long getId() {
		return id;
	}

	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}

	public String getDriverId() {
		return driverId;
	}

	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}

	public String getDriverName() {
		return driverName;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setSchedulerTelephone(String schedulerTelephone) {
		this.schedulerTelephone = schedulerTelephone;
	}

	public String getSchedulerTelephone() {
		return schedulerTelephone;
	}

	public void setIdentityCard(String identityCard) {
		this.identityCard = identityCard;
	}

	public String getIdentityCard() {
		return identityCard;
	}

	public void setDrivingYears(Long drivingYears) {
		this.drivingYears = drivingYears;
	}

	public Long getDrivingYears() {
		return drivingYears;
	}

	public void setProfileCompleteness(Long profileCompleteness) {
		this.profileCompleteness = profileCompleteness;
	}

	public Long getProfileCompleteness() {
		return profileCompleteness;
	}

	public void setServiceCount(Long serviceCount) {
		this.serviceCount = serviceCount;
	}

	public Long getServiceCount() {
		return serviceCount;
	}

	public void setTotalCourse(String totalCourse) {
		this.totalCourse = totalCourse;
	}

	public String getTotalCourse() {
		return totalCourse;
	}

	public void setOperation(Integer operation) {
		this.operation = operation;
	}

	public Integer getOperation() {
		return operation;
	}

	public void setOperationDesc(String operationDesc) {
		this.operationDesc = operationDesc;
	}

	public String getOperationDesc() {
		return operationDesc;
	}

	public void setIsTheDrivrLicenseValid(Integer isTheDrivrLicenseValid) {
		this.isTheDrivrLicenseValid = isTheDrivrLicenseValid;
	}

	public Integer getIsTheDrivrLicenseValid() {
		return isTheDrivrLicenseValid;
	}

	public void setVehicleClass(String vehicleClass) {
		this.vehicleClass = vehicleClass;
	}

	public String getVehicleClass() {
		return vehicleClass;
	}

	public void setValidPeriodTo(String validPeriodTo) {
		this.validPeriodTo = validPeriodTo;
	}

	public String getValidPeriodTo() {
		return validPeriodTo;
	}

	public void setQualificationCertificate(String qualificationCertificate) {
		this.qualificationCertificate = qualificationCertificate;
	}

	public String getQualificationCertificate() {
		return qualificationCertificate;
	}

	public void setIsTheQualificationValid(Integer isTheQualificationValid) {
		this.isTheQualificationValid = isTheQualificationValid;
	}

	public Integer getIsTheQualificationValid() {
		return isTheQualificationValid;
	}

	public void setActualCarrierEnd(String actualCarrierEnd) {
		this.actualCarrierEnd = actualCarrierEnd;
	}

	public String getActualCarrierEnd() {
		return actualCarrierEnd;
	}

	public void setLastServiceTime(Date lastServiceTime) {
		this.lastServiceTime = lastServiceTime;
	}

	public Date getLastServiceTime() {
		return lastServiceTime;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return state;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getAddress() {
		return address;
	}

	public void setVehicles(String vehicles) {
		this.vehicles = vehicles;
	}

	public String getVehicles() {
		return vehicles;
	}

	public void setLongDistanceRoutes(String longDistanceRoutes) {
		this.longDistanceRoutes = longDistanceRoutes;
	}

	public String getLongDistanceRoutes() {
		return longDistanceRoutes;
	}

	public void setCooperativeStatus(Integer cooperativeStatus) {
		this.cooperativeStatus = cooperativeStatus;
	}

	public Integer getCooperativeStatus() {
		return cooperativeStatus;
	}

	public void setBlacklistStatus(Integer blacklistStatus) {
		this.blacklistStatus = blacklistStatus;
	}

	public Integer getBlacklistStatus() {
		return blacklistStatus;
	}

	public void setOverallEvaluate(String overallEvaluate) {
		this.overallEvaluate = overallEvaluate;
	}

	public String getOverallEvaluate() {
		return overallEvaluate;
	}

	public void setSchedulerVehicleStructure(String schedulerVehicleStructure) {
		this.schedulerVehicleStructure = schedulerVehicleStructure;
	}

	public String getSchedulerVehicleStructure() {
		return schedulerVehicleStructure;
	}

	public void setWlhyVehicleStructure(String wlhyVehicleStructure) {
		this.wlhyVehicleStructure = wlhyVehicleStructure;
	}

	public String getWlhyVehicleStructure() {
		return wlhyVehicleStructure;
	}

	public void setExcludedVehicleStructure(String excludedVehicleStructure) {
		this.excludedVehicleStructure = excludedVehicleStructure;
	}

	public String getExcludedVehicleStructure() {
		return excludedVehicleStructure;
	}

	public void setSchedulerVehicleLength(String schedulerVehicleLength) {
		this.schedulerVehicleLength = schedulerVehicleLength;
	}

	public String getSchedulerVehicleLength() {
		return schedulerVehicleLength;
	}

	public void setWlhyVehicleLength(String wlhyVehicleLength) {
		this.wlhyVehicleLength = wlhyVehicleLength;
	}

	public String getWlhyVehicleLength() {
		return wlhyVehicleLength;
	}

	public void setExcludedVehicleLength(String excludedVehicleLength) {
		this.excludedVehicleLength = excludedVehicleLength;
	}

	public String getExcludedVehicleLength() {
		return excludedVehicleLength;
	}

	public void setSchedulerGoods(String schedulerGoods) {
		this.schedulerGoods = schedulerGoods;
	}

	public String getSchedulerGoods() {
		return schedulerGoods;
	}

	public void setWlhyGoods(String wlhyGoods) {
		this.wlhyGoods = wlhyGoods;
	}

	public String getWlhyGoods() {
		return wlhyGoods;
	}

	public void setExcludedGoods(String excludedGoods) {
		this.excludedGoods = excludedGoods;
	}

	public String getExcludedGoods() {
		return excludedGoods;
	}

	public void setSchedulerHaulway(String schedulerHaulway) {
		this.schedulerHaulway = schedulerHaulway;
	}

	public String getSchedulerHaulway() {
		return schedulerHaulway;
	}

	public void setWlhyHaulway(String wlhyHaulway) {
		this.wlhyHaulway = wlhyHaulway;
	}

	public String getWlhyHaulway() {
		return wlhyHaulway;
	}

	public void setExcludedHaulway(String excludedHaulway) {
		this.excludedHaulway = excludedHaulway;
	}

	public String getExcludedHaulway() {
		return excludedHaulway;
	}

	public void setBlackenReason(String blackenReason) {
		this.blackenReason = blackenReason;
	}

	public String getBlackenReason() {
		return blackenReason;
	}

	public void setChangeTelReason(String changeTelReason) {
		this.changeTelReason = changeTelReason;
	}

	public String getChangeTelReason() {
		return changeTelReason;
	}

	public void setDriverRating(String driverRating) {
		this.driverRating = driverRating;
	}

	public String getDriverRating() {
		return driverRating;
	}

	public void setDriverTag(String driverTag) {
		this.driverTag = driverTag;
	}

	public String getDriverTag() {
		return driverTag;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("driverId", getDriverId()).append("driverName", getDriverName())
				.append("telephone", getTelephone()).append("schedulerTelephone", getSchedulerTelephone()).append("identityCard", getIdentityCard()).append("drivingYears", getDrivingYears())
				.append("profileCompleteness", getProfileCompleteness()).append("serviceCount", getServiceCount()).append("totalCourse", getTotalCourse()).append("operation", getOperation())
				.append("operationDesc", getOperationDesc()).append("isTheDrivrLicenseValid", getIsTheDrivrLicenseValid()).append("vehicleClass", getVehicleClass())
				.append("validPeriodTo", getValidPeriodTo()).append("qualificationCertificate", getQualificationCertificate()).append("isTheQualificationValid", getIsTheQualificationValid())
				.append("actualCarrierEnd", getActualCarrierEnd()).append("lastServiceTime", getLastServiceTime()).append("state", getState()).append("address", getAddress())
				.append("vehicles", getVehicles()).append("longDistanceRoutes", getLongDistanceRoutes()).append("cooperativeStatus", getCooperativeStatus())
				.append("blacklistStatus", getBlacklistStatus()).append("overallEvaluate", getOverallEvaluate()).append("schedulerVehicleStructure", getSchedulerVehicleStructure())
				.append("wlhyVehicleStructure", getWlhyVehicleStructure()).append("excludedVehicleStructure", getExcludedVehicleStructure())
				.append("schedulerVehicleLength", getSchedulerVehicleLength()).append("wlhyVehicleLength", getWlhyVehicleLength()).append("excludedVehicleLength", getExcludedVehicleLength())
				.append("schedulerGoods", getSchedulerGoods()).append("wlhyGoods", getWlhyGoods()).append("excludedGoods", getExcludedGoods()).append("schedulerHaulway", getSchedulerHaulway())
				.append("wlhyHaulway", getWlhyHaulway()).append("excludedHaulway", getExcludedHaulway()).append("blackenReason", getBlackenReason()).append("changeTelReason", getChangeTelReason())
				.append("remark", getRemark()).append("driverRating", getDriverRating()).append("driverTag", getDriverTag()).append("createBy", getCreateBy()).append("createTime", getCreateTime())
				.append("updateBy", getUpdateBy()).append("updateTime", getUpdateTime()).append("deleted", getDeleted()).toString();
	}
}
