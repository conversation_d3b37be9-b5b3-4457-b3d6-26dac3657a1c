package com.zly.project.driver.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.driver.DriverPortraitExcelConverter;
import com.zly.common.utils.driver.DriverPortraitMarkdownConverter;
import com.zly.framework.knowledge.KnowledgeBaseService;
import com.zly.framework.knowledge.UploadResult;
import com.zly.project.driver.controller.vo.DriverProfilePageReqVO;
import com.zly.project.driver.controller.vo.DriverProfilePageRespVO;
import com.zly.project.driver.controller.vo.DriverProfileRespVO;
import com.zly.project.driver.domain.DriverProfileDO;
import com.zly.project.driver.mapper.DriverProfileMapper;
import com.zly.project.driver.service.DriverProfileService;
import com.zly.project.system.service.ISysConfigService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import java.io.ByteArrayOutputStream;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.ALL_DRIVER_PROFILES_ERROR;
import static com.zly.enums.ErrorCodeConstants.EXPORT_DRIVER_PROFILES_ERROR;

/**
 * 司机画像 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DriverProfileServiceImpl implements DriverProfileService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private DriverProfileMapper driverProfileMapper;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource(name = "fileUploadThreadPool")
    private ExecutorService threadPool;

    @Value("${custom.scheduler.driverProfile.apiUrl}")
    private String driverProfileApiUrl;

    @Value("${scheduler.knowledge.export-path:/tmp/driver_profiles}")
    private String exportPath;

    //数据库默认目录
    @Value("${custom.bailian.agentKey:default}")
    public String DEFAULT_AGENT_KEY;

    //百炼平台 空间ID
    @Value("${custom.bailian.modelId:llm-ynhw38ot2ttdm2ox}")
    public String DEFAULT_MODEL_ID;

    //百炼平台 知识库ID
    @Value("${custom.bailian.indexId:eu5u5ue600}")
    public String DEFAULT_INDEX_ID;
    private static final String DRIVER_PROFILE_FILE_CONFIG_KEY = "driver.profile.knowledge.file";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DriverProfilePageRespVO getDriverProfilePage(DriverProfilePageReqVO pageReqVO) {
        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("pageSize", pageReqVO.getPageSize());
        params.put("pageNum", pageReqVO.getPageNum());
        params.put("addFlag", pageReqVO.getAddFlag());
        // 发起远程调用
        ResponseEntity<DriverProfilePageRespVO> responseEntity = restTemplate.getForEntity(
                driverProfileApiUrl + "?pageSize={pageSize}&pageNum={pageNum}&addFlag={addFlag}",
                DriverProfilePageRespVO.class,
                params
        );
        DriverProfilePageRespVO body = responseEntity.getBody();
        // 批量保存司机画像数据
        if (body != null && body.getRows() != null && !body.getRows().isEmpty()) {
            batchSaveDriverProfiles(body.getRows());
        }
        return body;
    }
    
    /**
     * 分批查询并同步所有司机画像数据
     * 首次查询1000条，获取总数后分页查询所有数据
     * @return 同步结果，包含总数和成功同步数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncAllDriverProfiles() {
        // 结果统计
        Map<String, Object> result = new HashMap<>();
        try {
            // 查询司机画像表中是否存在数据
            DriverProfilePageReqVO initialReq = new DriverProfilePageReqVO();
            initialReq.setPageNum(1);
            initialReq.setPageSize(1000);
            int existingCount = driverProfileMapper.countDriverProfiles();
            if (existingCount > 0) {
                initialReq.setAddFlag(1);
                // 存在数据，执行增量同步
                log.info("本地存在 {} 条司机画像数据，开始执行增量同步...", existingCount);
            } else {
                // 不存在数据，执行全量同步
                log.info("本地无司机画像数据，开始执行全量同步...");
            }
            int totalCount;
            int syncCount = 0;
            // 发起首次请求
            DriverProfilePageRespVO initialResp = getDriverProfilePage(initialReq);
            if (initialResp == null || initialResp.getTotal() == null || initialResp.getTotal() <= 0) {
                result.put("totalCount", 0);
                result.put("syncCount", 0);
                result.put("message", "未获取到数据或总数为0");
                return result;
            }
            // 获取总数和首批数据
            totalCount = initialResp.getTotal();
            syncCount += initialResp.getRows() != null ? initialResp.getRows().size() : 0;
            // 计算总页数
            int pageSize = 1000;
            int totalPages = (totalCount + pageSize - 1) / pageSize;
            // 从第2页开始查询剩余数据
            for (int pageNo = 2; pageNo <= totalPages; pageNo++) {
                DriverProfilePageReqVO pageReq = new DriverProfilePageReqVO();
                pageReq.setPageNum(pageNo);
                pageReq.setPageSize(pageSize);
                if (existingCount > 0) {
                    pageReq.setAddFlag(1);
                    log.info("本地存在 {} 条司机画像数据，开始执行增量同步...", existingCount);
                }
                try {
                    DriverProfilePageRespVO pageResp = getDriverProfilePage(pageReq);
                    if (pageResp != null && pageResp.getRows() != null) {
                        syncCount += pageResp.getRows().size();
                    }
                } catch (Exception e) {
                    // 记录异常但继续执行
                    result.put("error_page_" + pageNo, e.getMessage());
                }
            }
            // 返回结果
            result.put("addFlag", initialReq.getAddFlag());
            result.put("totalCount", totalCount);
            result.put("syncCount", syncCount);
            result.put("message", "同步完成，共同步" + syncCount + "条数据，总数" + totalCount + "条");
        }catch (Exception e){
            log.error("同步司机画像数据异常e:{}",e);
            throw exception(ALL_DRIVER_PROFILES_ERROR);
        }
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncAllDriverProfilesNoAddFlag() {
        // 结果统计
        Map<String, Object> result = new HashMap<>();
        try {
            // 查询司机画像表中是否存在数据
            DriverProfilePageReqVO initialReq = new DriverProfilePageReqVO();
            initialReq.setPageNum(1);
            initialReq.setPageSize(1000);
            initialReq.setAddFlag(0);
            int totalCount;
            int syncCount = 0;
            // 发起首次请求
            DriverProfilePageRespVO initialResp = getDriverProfilePage(initialReq);
            if (initialResp == null || initialResp.getTotal() == null || initialResp.getTotal() <= 0) {
                result.put("totalCount", 0);
                result.put("syncCount", 0);
                result.put("message", "未获取到数据或总数为0");
                return result;
            }
            // 获取总数和首批数据
            totalCount = initialResp.getTotal();
            syncCount += initialResp.getRows() != null ? initialResp.getRows().size() : 0;
            // 计算总页数
            int pageSize = 1000;
            int totalPages = (totalCount + pageSize - 1) / pageSize;
            // 从第2页开始查询剩余数据
            for (int pageNo = 2; pageNo <= totalPages; pageNo++) {
                DriverProfilePageReqVO pageReq = new DriverProfilePageReqVO();
                pageReq.setPageNum(pageNo);
                pageReq.setPageSize(pageSize);
                pageReq.setAddFlag(0);
                try {
                    DriverProfilePageRespVO pageResp = getDriverProfilePage(pageReq);
                    if (pageResp != null && pageResp.getRows() != null) {
                        syncCount += pageResp.getRows().size();
                    }
                } catch (Exception e) {
                    // 记录异常但继续执行
                    result.put("error_page_" + pageNo, e.getMessage());
                }
            }
            // 返回结果
            result.put("addFlag", initialReq.getAddFlag());
            result.put("totalCount", totalCount);
            result.put("syncCount", syncCount);
            result.put("message", "同步完成，共同步" + syncCount + "条数据，总数" + totalCount + "条");
        }catch (Exception e){
            log.error("同步司机画像数据异常e:{}",e);
            throw exception(ALL_DRIVER_PROFILES_ERROR);
        }
        return result;
    }

    /**
     * 批量保存司机画像数据
     *
     * @param driverProfiles 司机画像列表
     */
    private void batchSaveDriverProfiles(List<DriverProfileRespVO> driverProfiles) {
        if (CollectionUtil.isEmpty(driverProfiles)) {
            return;
        }
        // 1. 提取所有司机ID
        List<String> driverIds = new ArrayList<>(driverProfiles.size());
        Map<String, DriverProfileRespVO> profileMap = new HashMap<>(driverProfiles.size());
        for (DriverProfileRespVO profile : driverProfiles) {
            driverIds.add(profile.getDriverId());
            profileMap.put(profile.getDriverId(), profile);
        }
        // 2. 批量查询已存在的司机画像
        List<DriverProfileDO> existingProfiles = driverProfileMapper.selectListByDriverIds(driverIds);
        Map<String, DriverProfileDO> existingProfileMap = new HashMap<>(existingProfiles.size());
        for (DriverProfileDO profile : existingProfiles) {
            existingProfileMap.put(profile.getDriverId(), profile);
        }
        // 3. 分别处理需要更新和插入的记录
        List<DriverProfileDO> toUpdateList = new ArrayList<>();
        List<DriverProfileDO> toInsertList = new ArrayList<>();
        for (String driverId : driverIds) {
            DriverProfileRespVO profileVO = profileMap.get(driverId);
            DriverProfileDO profileDO = new DriverProfileDO();
            BeanUtils.copyProperties(profileVO, profileDO);
            if (existingProfileMap.containsKey(driverId)) {
                // 需要更新
                DriverProfileDO existingProfile = existingProfileMap.get(driverId);
                handleMerged(profileVO,existingProfile, profileDO);
                profileDO.setId(existingProfile.getId());
                profileDO.setUpdateTime(new Date());
                toUpdateList.add(profileDO);
            } else {
                //只有第一次更新时同步手机号
                profileDO.setSchedulerTelephone(profileVO.getTelephone());
                handleMerged(profileVO,null, profileDO);
                profileDO.setCreateTime(new Date());
                profileDO.setUpdateTime(new Date());
                // 需要插入
                toInsertList.add(profileDO);
            }
        }
        // 4. 批量更新
        if (!toUpdateList.isEmpty()) {
            driverProfileMapper.updateDriverProfileBatch(toUpdateList);
            log.info("批量更新司机画像数据");
        }
        // 5. 批量插入
        if (!toInsertList.isEmpty()) {
            driverProfileMapper.insertBatch(toInsertList);
            log.info("批量插入司机画像数据");
        }
    }

    /**
     * 处理合并逻辑
     * @param profileVO
     * @param existingProfile
     * @param profileDO
     */
    private static void handleMerged(DriverProfileRespVO profileVO,DriverProfileDO existingProfile, DriverProfileDO profileDO) {
        if (existingProfile == null){
            String mergedHaulway = DriverPortraitMarkdownConverter.mergeHaulway(profileVO.getWlhyHaulway(), null, null);
            mergedHaulway = StringUtil.isEmpty(mergedHaulway) ? null : new JSONArray(java.util.Arrays.asList(mergedHaulway.split("、"))).toJSONString();
            profileDO.setSchedulerHaulway(mergedHaulway);
            String mergedGoode = DriverPortraitMarkdownConverter.mergeAndProcessGoods(profileVO.getWlhyGoods(), null, null);
            mergedGoode = StringUtil.isEmpty(mergedGoode) ? null : new JSONArray(java.util.Arrays.asList(mergedGoode.split("、"))).toJSONString();
            profileDO.setSchedulerGoods(mergedGoode);
            String mergeAndProcessVehicleStructure = DriverPortraitMarkdownConverter.mergeAndProcessVehicleStructure(profileVO.getWlhyVehicleStructure(), null, null);
            mergeAndProcessVehicleStructure = StringUtil.isEmpty(mergeAndProcessVehicleStructure) ? null : new JSONArray(java.util.Arrays.asList(mergeAndProcessVehicleStructure.split("、"))).toJSONString();
            profileDO.setSchedulerVehicleStructure(mergeAndProcessVehicleStructure);
            String mergeAndProcessVehicleLengths = DriverPortraitMarkdownConverter.mergeAndProcessVehicleLengthNoChange(profileVO.getWlhyVehicleLength(), null, null);
            mergeAndProcessVehicleLengths = StringUtil.isEmpty(mergeAndProcessVehicleLengths) ? null : new JSONArray(java.util.Arrays.asList(mergeAndProcessVehicleLengths.split("、"))).toJSONString();
            profileDO.setSchedulerVehicleLength(mergeAndProcessVehicleLengths);
        }else {
            String mergedHaulway = DriverPortraitMarkdownConverter.mergeHaulway(profileVO.getWlhyHaulway(), existingProfile.getSchedulerHaulway(), existingProfile.getExcludedHaulway());
            mergedHaulway = StringUtil.isEmpty(mergedHaulway) ? null : new JSONArray(java.util.Arrays.asList(mergedHaulway.split("、"))).toJSONString();
            profileDO.setSchedulerHaulway(mergedHaulway);
            String mergedGoode = DriverPortraitMarkdownConverter.mergeAndProcessGoods(profileVO.getWlhyGoods(), existingProfile.getSchedulerGoods(), existingProfile.getExcludedGoods());
            mergedGoode = StringUtil.isEmpty(mergedGoode) ? null : new JSONArray(java.util.Arrays.asList(mergedGoode.split("、"))).toJSONString();
            profileDO.setSchedulerGoods(mergedGoode);
            String mergeAndProcessVehicleStructure = DriverPortraitMarkdownConverter.mergeAndProcessVehicleStructure(profileVO.getWlhyVehicleStructure(), existingProfile.getSchedulerVehicleStructure(), existingProfile.getExcludedVehicleStructure());
            mergeAndProcessVehicleStructure = StringUtil.isEmpty(mergeAndProcessVehicleStructure) ? null : new JSONArray(java.util.Arrays.asList(mergeAndProcessVehicleStructure.split("、"))).toJSONString();
            profileDO.setSchedulerVehicleStructure(mergeAndProcessVehicleStructure);
            String mergeAndProcessVehicleLengths = DriverPortraitMarkdownConverter.mergeAndProcessVehicleLengthNoChange(profileVO.getWlhyVehicleLength(), existingProfile.getSchedulerVehicleLength(), existingProfile.getExcludedVehicleLength());
            mergeAndProcessVehicleLengths = StringUtil.isEmpty(mergeAndProcessVehicleLengths) ? null : new JSONArray(java.util.Arrays.asList(mergeAndProcessVehicleLengths.split("、"))).toJSONString();
            profileDO.setSchedulerVehicleLength(mergeAndProcessVehicleLengths);
        }

    }

    @Nullable
    private static String getMergeAndProcessVehicleLengths(String mergeAndProcessVehicleLengths) {
        if (StringUtils.isNotBlank(mergeAndProcessVehicleLengths)) {
            String[] strings = mergeAndProcessVehicleLengths.split("、");
            // ["6米", "13米"]  -> ["6000", "13000"]
            List<String> convertedLengths = new ArrayList<>();
            for (String length : strings) {
                if (length.endsWith("米")) {
                    try {
                        // 提取数字部分
                        String numPart = length.substring(0, length.length() - 1);
                        // 转换为浮点数并乘以1000转为毫米
                        double meters = Double.parseDouble(numPart);
                        int millimeters = (int) (meters * 1000);
                        convertedLengths.add(String.valueOf(millimeters));
                    } catch (NumberFormatException e) {
                        // 如果转换失败，保留原值
                        log.warn("无法转换车辆长度值: {}", length);
                        convertedLengths.add(length);
                    }
                } else {
                    // 如果不是以"米"结尾，保留原值
                    convertedLengths.add(length);
                }
            }
            mergeAndProcessVehicleLengths = StringUtil.isEmpty(mergeAndProcessVehicleLengths) ? null : new JSONArray(convertedLengths).toJSONString();
        }
        return mergeAndProcessVehicleLengths;
    }

    @Override
    public DriverProfileDO getDriverProfile(String driverId) {
        return driverProfileMapper.selectByDriverId(driverId);
    }
    
    @Override
    public byte[] exportDriverProfilesMarkdown() {
        try {
            // 查询所有司机画像数据
            List<DriverProfileRespVO> driverProfileVOs = getDriverProfileRespVOS();
            // 使用DriverPortraitMarkdownConverter生成Markdown内容
            //List<DriverProfileRespVO> first100 = driverProfileVOs.subList(0, Math.min(1000, driverProfileVOs.size()));
            String markdownContent = DriverPortraitMarkdownConverter.convertToMarkdown(driverProfileVOs);
            // 将Markdown内容转换为字节数组
            return markdownContent.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("[exportDriverProfilesMarkdown] 导出司机画像数据异常", e);
            throw exception(EXPORT_DRIVER_PROFILES_ERROR);
        }
    }

    @Override
    public List<String> retrieveFromKnowledgeBase(String query, String indexId, Integer topK) {
        return KnowledgeBaseService.retrieveFromKnowledgeBase(query,DEFAULT_INDEX_ID,topK,DEFAULT_INDEX_ID,DEFAULT_MODEL_ID);
    }

    @Override
    public Map<String, String> retrieveFromKnowledgeBaseMap(String query, String indexId, Integer topK) {
        return KnowledgeBaseService.retrieveFromKnowledgeBaseMap(query,DEFAULT_INDEX_ID,topK,DEFAULT_INDEX_ID,DEFAULT_MODEL_ID);
    }

    @Override
    public int updateDriverProfile(DriverProfileDO driverProfileDO) {
        return driverProfileMapper.updateDriverProfile(driverProfileDO);
    }

    @Override
    public Map<String, Object> uploadDriverProfilesToKnowledgeBase(String DEFAULT_AGENT_KEY,String DEFAULT_MODEL_ID,String DEFAULT_INDEX_ID) {
        Map<String, Object> result = new HashMap<>();
        List<String> tempFiles = new ArrayList<>();
        try {
            // 1. 生成Markdown文件
            List<String> fileList = generateMarkdownFileList();
            tempFiles.addAll(fileList);
            log.info("生成司机画像Markdown文件: {}", fileList);
            result.put("filePaths", fileList);
            if (CollectionUtil.isEmpty(fileList)) {
                result.put("success", false);
                result.put("message", "未生成任何文件");
                return result;
            }
            // 2. 使用线程池并行处理文件上传
            int fileCount = fileList.size();
            List<String> fileIds = Collections.synchronizedList(new ArrayList<>());
            List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());
            log.info("使用线程池处理文件上传，核心线程数: 5，文件数量: {}", fileCount);
            // 使用CountDownLatch等待所有线程完成
            CountDownLatch latch = new CountDownLatch(fileCount);
            // 为每个文件创建一个上传任务并提交到线程池
            for (String filePath : fileList) {
                threadPool.submit(() -> {
                    try {
                        log.info("线程[{}]开始上传文件: {}", Thread.currentThread().getName(), filePath);
                        // 上传文件到数据管理库
                        UploadResult uploadResult = KnowledgeBaseService.updateKnowledgeBase(filePath,DEFAULT_AGENT_KEY,DEFAULT_MODEL_ID,DEFAULT_INDEX_ID);
                        if (!uploadResult.getIsSuccess()) {
                            log.error("线程[{}]上传文件到数据管理库失败: {}", Thread.currentThread().getName(), uploadResult.getMessage());
                            exceptions.add(new RuntimeException("上传文件到数据管理库失败: " + uploadResult.getMessage()));
                            return;
                        }
                        // 获取文件ID并添加到知识库
                        String fileId = uploadResult.getFileId();
                        if (StringUtils.isBlank(fileId)) {
                            log.error("线程[{}]未获取到文件ID", Thread.currentThread().getName());
                            exceptions.add(new RuntimeException("未获取到文件ID"));
                            return;
                        }
//                        log.info("线程[{}]文件上传成功，文件ID: {}, 开始添加到知识库...", Thread.currentThread().getName(), fileId);
//                        UploadResult addResult = KnowledgeBaseService.addFileToKnowledgeBase(fileId, DEFAULT_INDEX_ID);
//                        if (!addResult.getIsSuccess()) {
//                            log.error("线程[{}]添加文件到知识库失败: {}", Thread.currentThread().getName(), addResult.getMessage());
//                            exceptions.add(new RuntimeException("添加文件到知识库失败: " + addResult.getMessage()));
//                            return;
//                        }
                        // 添加成功的文件ID
                        fileIds.add(fileId);
                        log.info("线程[{}]文件处理完成，文件ID: {}", Thread.currentThread().getName(), fileId);
                    } catch (Exception e) {
                        log.error("线程[{}]处理文件异常: {}", Thread.currentThread().getName(), e.getMessage(), e);
                        exceptions.add(e);
                    } finally {
                        latch.countDown();
                    }
                });
            }
            // 等待所有线程完成
            log.info("等待所有上传线程完成...");
            boolean allCompleted = latch.await(180, TimeUnit.MINUTES);
            if (!allCompleted || !exceptions.isEmpty()){
                //失败或者超时了删除上传的文件
                for (String fileId : fileIds) {
                    UploadResult delUploadResult = KnowledgeBaseService.deleteFileFromDataCenter(fileId,DEFAULT_MODEL_ID);
                    Boolean isSuccess = delUploadResult.getIsSuccess();
                    if (isSuccess){
                        log.info("成功删除文件: {}", fileId);
                    }else {
                        log.warn("删除旧文件失败: {}",fileId);
                    }
    //                    UploadResult delIndexUploadResult = KnowledgeBaseService.deleteFileFromKnowledgeBase(fileId, DEFAULT_INDEX_ID);
//                    Boolean isSuccessIndex = delIndexUploadResult.getIsSuccess();
//                    if (isSuccessIndex){
//                        log.info("成功删除知识库文件: {}", fileId);
//                    }else {
//                        log.warn("删除知识库文件失败: {}",fileId);
//                    }
                }
            }
            if (!allCompleted) {
                log.error("部分上传线程未在规定时间内完成");
                result.put("success", false);
                result.put("message", "上传超时，部分文件可能未处理完成");
                return result;
            }
            // 检查是否有异常发生
            if (!exceptions.isEmpty()) {
                log.error("上传过程中发生{}个异常", exceptions.size());
                result.put("success", false);
                result.put("message", "上传过程中发生异常: " + exceptions.get(0).getMessage());
                result.put("exceptions", exceptions.size());
                return result;
            }
            // 3. 处理旧文件
            String configValue = sysConfigService.selectConfigByKeyNoCache(DRIVER_PROFILE_FILE_CONFIG_KEY);
            List<String> oldFileIds = new ArrayList<>();
            if (StringUtils.isNotBlank(configValue)) {
                try {
                    // 尝试解析配置值
                    if (configValue.startsWith("[")) {
                        // 新的JSON数组格式 - 使用fastjson2正确的API
                        List<JSONObject> fileInfoList = com.alibaba.fastjson2.JSON.parseArray(configValue, JSONObject.class);
                        for (JSONObject fileInfo : fileInfoList) {
                            String fileId = fileInfo.getString("fileId");
                            if (StringUtils.isNotBlank(fileId)) {
                                oldFileIds.add(fileId);
                            }
                        }
                    } else {
                        // 旧的对象格式
                        JSONObject configJson = JSONObject.parseObject(configValue);
                        String oldFileId = configJson.getString("fileId");
                        if (StringUtils.isNotBlank(oldFileId)) {
                            oldFileIds.add(oldFileId);
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析旧文件配置异常: {}", e.getMessage());
                }
            }
            // 4. 更新配置 - 使用JSON数组格式存储文件ID
            List<JSONObject> fileInfoList = new ArrayList<>();
            for (String fileId : fileIds) {
                JSONObject fileInfo = new JSONObject();
                fileInfo.put("fileId", fileId);
                fileInfo.put("createTime", System.currentTimeMillis());
                fileInfoList.add(fileInfo);
            }
            // 将文件信息列表转换为JSON字符串并保存
            String newConfigValue = JSONObject.toJSONString(fileInfoList);
            sysConfigService.updateConfigByKey(DRIVER_PROFILE_FILE_CONFIG_KEY, newConfigValue);
            log.info("更新配置成功，新配置值: {}", newConfigValue);
            // 5. 删除旧文件
            if (!oldFileIds.isEmpty()) {
                log.info("开始删除旧文件，共{}个文件", oldFileIds.size());
                List<String> deletedFileIds = new ArrayList<>();
                List<String> failedFileIds = new ArrayList<>();
                for (String oldFileId : oldFileIds) {
                    try {
                        UploadResult delUploadResult = KnowledgeBaseService.deleteFileFromDataCenter(oldFileId,DEFAULT_MODEL_ID);
//                        UploadResult delIndexUploadResult = KnowledgeBaseService.deleteFileFromKnowledgeBase(oldFileId, DEFAULT_INDEX_ID);

//                        if (delUploadResult.getIsSuccess() && delIndexUploadResult.getIsSuccess()) {
//                            deletedFileIds.add(oldFileId);
//                            log.info("成功删除旧文件: {}", oldFileId);
//                        } else {
//                            failedFileIds.add(oldFileId);
//                            log.warn("删除旧文件失败: {}, 数据中心删除结果: {}, 知识库删除结果: {}",
//                                    oldFileId,
//                                    delUploadResult.getIsSuccess() ? "成功" : "失败",
//                                    delIndexUploadResult.getIsSuccess() ? "成功" : "失败");
//                        }

                        if (delUploadResult.getIsSuccess()) {
                            deletedFileIds.add(oldFileId);
                            log.info("成功删除旧文件: {}", oldFileId);
                        } else {
                            failedFileIds.add(oldFileId);
                            log.warn("删除旧文件失败: {}, 数据中心删除结果: {}",
                                    oldFileId, "失败");
                        }
                    } catch (Exception e) {
                        failedFileIds.add(oldFileId);
                        log.warn("删除旧文件异常({}): {}", oldFileId, e.getMessage());
                    }
                }
                result.put("deletedFileIds", deletedFileIds);
                result.put("failedDeleteFileIds", failedFileIds);
            }
            // 6. 返回结果
            result.put("success", true);
            result.put("message", "司机画像知识库更新成功，共上传" + fileIds.size() + "个文件");
            result.put("fileIds", fileIds);
            result.put("oldFileIds", oldFileIds);
            return result;
        } catch (Exception e) {
            log.error("上传司机画像到知识库异常", e);
            result.put("success", false);
            result.put("message", "上传司机画像到知识库异常: " + e.getMessage());
            return result;
        } finally {
            // 删除创建的临时文件
            for (String tempFile : tempFiles) {
                try {
                    File file = new File(tempFile);
                    if (file.exists()) {
                        boolean deleted = file.delete();
                        if (deleted) {
                            log.info("成功删除临时文件: {}", tempFile);
                        } else {
                            log.warn("删除临时文件失败: {}", tempFile);
                        }
                    }
                } catch (Exception e) {
                    log.warn("删除临时文件异常: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 生成司机画像Markdown文件
     *
     * @return 文件路径
     * @throws Exception 生成异常
     */
    private String generateMarkdownFile() throws Exception {
        // 确保导出目录存在
        File exportDir = new File(exportPath);
        if (!exportDir.exists()) {
            exportDir.mkdirs();
        }
        // 生成文件名
        String fileName = "driver_profiles_" +
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".md";
        String filePath = exportPath + File.separator + fileName;
        // 获取司机画像数据并转换为Markdown
        List<DriverProfileRespVO> driverProfileVOs = getDriverProfileRespVOS();
        String markdownContent = DriverPortraitMarkdownConverter.convertToMarkdown(driverProfileVOs);
        // 写入文件
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(markdownContent.getBytes(StandardCharsets.UTF_8));
        }
        return filePath;
    }

    /**
     * 生成司机画像Markdown文件
     * 每2000条数据生成一个文件，文件名后添加序号标识
     * 只包含状态为生效且非黑名单的司机数据
     *
     * @return 文件路径列表
     * @throws Exception 生成异常
     */
    private List<String> generateMarkdownFileList() throws Exception {
        // 确保导出目录存在
        File exportDir = new File(exportPath);
        if (!exportDir.exists()) {
            boolean mkdirs = exportDir.mkdirs();
            if (!mkdirs) {
                log.warn("创建导出目录失败: {}", exportPath);
            }
        }
        List<String> filePaths = new ArrayList<>();
        String baseFileName = "driver_profiles_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        // 分页大小
        int pageSize = 3300;
        int pageNum = 1;
        boolean hasMoreData = true;
        int fileIndex = 1;
        while (hasMoreData) {
            int offset = (pageNum - 1) * pageSize;
            // 分页查询有效且非黑名单的司机数据
            List<DriverProfileDO> driverProfiles = driverProfileMapper.selectActiveNonBlacklistDrivers(offset, pageSize);
            if (CollectionUtil.isEmpty(driverProfiles)) {
                // 没有更多数据了
                hasMoreData = false;
                continue;
            }
            // 转换为VO对象
            List<DriverProfileRespVO> driverProfileVOs = new ArrayList<>(driverProfiles.size());
            for (DriverProfileDO profile : driverProfiles) {
                DriverProfileRespVO vo = new DriverProfileRespVO();
                BeanUtils.copyProperties(profile, vo);
                driverProfileVOs.add(vo);
            }
            // 生成带序号的文件名
            String fileName = baseFileName + "_" + fileIndex + ".md";
            String filePath = exportPath + File.separator + fileName;
            // 将当前分组数据转换为Markdown并写入文件
            String markdownContent = DriverPortraitMarkdownConverter.convertToMarkdown(driverProfileVOs);
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                fos.write(markdownContent.getBytes(StandardCharsets.UTF_8));
            }
            log.info("生成司机画像Markdown文件(第{}组): {}, 包含{}条数据", fileIndex, filePath, driverProfileVOs.size());
            filePaths.add(filePath);
            // 如果当前页的数据量小于页大小，说明没有更多数据了
            if (driverProfiles.size() < pageSize) {
                hasMoreData = false;
            } else {
                // 继续查询下一页
                pageNum++;
                fileIndex++;
            }
        }
        log.info("共生成{}个司机画像Markdown文件", filePaths.size());
        return filePaths;
    }

    @Override
    public byte[] exportDriverProfilesExcel() {
        try {
            // 获取所有司机画像数据
            List<DriverProfileRespVO> driverProfileVOs = getDriverProfileRespVOS();
            // 使用ByteArrayOutputStream来接收Excel数据
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 设置Excel样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(
                    headWriteCellStyle, contentWriteCellStyle);
            // 将数据转换为Excel格式并写入ByteArrayOutputStream
            List<DriverPortraitExcelConverter.DriverExcelData> excelDataList = 
                    DriverPortraitExcelConverter.convertToExcelData(driverProfileVOs);
            EasyExcel.write(outputStream, DriverPortraitExcelConverter.DriverExcelData.class)
                    .registerWriteHandler(styleStrategy)
                    .sheet("司机画像数据")
                    .doWrite(excelDataList);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("导出司机画像Excel失败", e);
            throw exception(EXPORT_DRIVER_PROFILES_ERROR);
        }
    }

    @NotNull
    private List<DriverProfileRespVO> getDriverProfileRespVOS() {
        List<DriverProfileDO> driverProfiles = driverProfileMapper.selectAllDriverList();
        if (CollUtil.isEmpty(driverProfiles)) {
            throw exception(EXPORT_DRIVER_PROFILES_ERROR);
        }
        // 转换为VO对象
        List<DriverProfileRespVO> driverProfileVOs = new ArrayList<>();
        for (DriverProfileDO profile : driverProfiles) {
            DriverProfileRespVO vo = new DriverProfileRespVO();
            BeanUtils.copyProperties(profile, vo);
            driverProfileVOs.add(vo);
        }
        return driverProfileVOs;
    }
}