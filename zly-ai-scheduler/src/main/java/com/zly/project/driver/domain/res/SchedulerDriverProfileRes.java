package com.zly.project.driver.domain.res;

import java.util.List;

import com.zly.project.driver.domain.SchedulerDriverEvaluateTag;
import com.zly.project.driver.domain.SchedulerDriverProfile;

/**
 * 司机画像对象 scheduler_driver_profile
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public class SchedulerDriverProfileRes {

	private SchedulerDriverProfile driver;

	private List<SchedulerDriverEvaluateTag> evaluateList;

	private SchedulerDriverEvaluateTag tag;

	public SchedulerDriverProfile getDriver() {
		return driver;
	}

	public void setDriver(SchedulerDriverProfile driver) {
		this.driver = driver;
	}

	public List<SchedulerDriverEvaluateTag> getEvaluateList() {
		return evaluateList;
	}

	public void setEvaluateList(List<SchedulerDriverEvaluateTag> evaluateList) {
		this.evaluateList = evaluateList;
	}

	public SchedulerDriverEvaluateTag getTag() {
		return tag;
	}

	public void setTag(SchedulerDriverEvaluateTag tag) {
		this.tag = tag;
	}
}
