package com.zly.project.driver.domain.req;

import com.zly.project.driver.domain.SchedulerDriverProfile;

import io.swagger.annotations.ApiModelProperty;

/**
 * 司机画像对象查询请求参数 scheduler_driver_profile
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public class SchedulerDriverProfileReq extends SchedulerDriverProfile {

	private static final long serialVersionUID = 1L;

	/** 电话状态(0:空号码 1:重复号码) */
	@ApiModelProperty(value = "电话状态(1:重复号码)", required = false)
	private String telephoneState;

	/** 调度中心这边的常跑线路出发地 */
	@ApiModelProperty(value = "线路(出发地)", required = false)
	private String schedulerHaulwayStart;

	/** 调度中心这边的常跑线路目的地 */
	@ApiModelProperty(value = "线路(目的地)", required = false)
	private String schedulerHaulwayEnd;

	public String getTelephoneState() {
		return telephoneState;
	}

	public void setTelephoneState(String telephoneState) {
		this.telephoneState = telephoneState;
	}

	public String getSchedulerHaulwayStart() {
		return schedulerHaulwayStart;
	}

	public void setSchedulerHaulwayStart(String schedulerHaulwayStart) {
		this.schedulerHaulwayStart = schedulerHaulwayStart;
	}

	public String getSchedulerHaulwayEnd() {
		return schedulerHaulwayEnd;
	}

	public void setSchedulerHaulwayEnd(String schedulerHaulwayEnd) {
		this.schedulerHaulwayEnd = schedulerHaulwayEnd;
	}
}
