package com.zly.project.match.service;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.match.controller.vo.DriverNotifyReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchCreateReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchPageReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchUpdateReqVO;
import com.zly.project.match.domain.TaskDriverMatchDO;
import com.zly.project.plan.domain.TaskPlanDO;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 调度任务匹配司机 Service 接口
 *
 * <AUTHOR>
 */
public interface TaskDriverMatchService {

    /**
     * 创建调度任务匹配司机
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTaskDriverMatch(TaskDriverMatchCreateReqVO createReqVO);

    /**
     * 批量创建调度任务匹配司机
     *
     * @param list 创建信息列表
     * @return 是否成功
     */
    boolean addTaskDriverMatchBatch(List<TaskDriverMatchDO> list);

    /**
     * 更新调度任务匹配司机
     *
     * @param updateReqVO 更新信息
     */
    void updateTaskDriverMatch(TaskDriverMatchUpdateReqVO updateReqVO);

    /**
     * 更新成交状态
     *
     * @param id 编号
     * @param dealStatus 成交状态
     * @param updateBy 更新者
     */
    void updateDealStatus(Long id, Integer dealStatus, String updateBy);

    /**
     * 删除调度任务匹配司机
     *
     * @param id 编号
     */
    void deleteTaskDriverMatch(Long id);

    /**
     * 批量删除调度任务匹配司机
     *
     * @param ids 编号集合
     */
    void deleteTaskDriverMatchBatch(Collection<Long> ids);

    /**
     * 获得调度任务匹配司机
     *
     * @param id 编号
     * @return 调度任务匹配司机
     */
    TaskDriverMatchDO getTaskDriverMatch(Long id);

    /**
     * 获得调度任务匹配司机列表
     *
     * @param ids 编号集合
     * @return 调度任务匹配司机列表
     */
    List<TaskDriverMatchDO> getTaskDriverMatchList(Collection<Long> ids);

    /**
     * 获得调度任务匹配司机分页
     *
     * @param pageReqVO 分页查询
     * @return 调度任务匹配司机分页
     */
    List<TaskDriverMatchDO> getTaskDriverMatchPage(TaskDriverMatchPageReqVO pageReqVO);

    /**
     * 根据调度任务ID获取匹配司机列表
     *
     * @param taskPlanId 调度任务ID
     * @return 匹配司机列表
     */
    List<TaskDriverMatchDO> getListByTaskPlanId(Long taskPlanId);

    /**
     * 根据成交状态获取匹配司机列表
     *
     * @param dealStatus 成交状态
     * @return 匹配司机列表
     */
    List<TaskDriverMatchDO> getListByDealStatus(Integer dealStatus);

    /**
     * 执行短信通知
     * @param reqVO
     * @return
     */
    void executeSmsNotify(DriverNotifyReqVO reqVO);

    /**
     * 构建短信内容
     * @param taskPlan
     * @return
     */
    String buildSmsContent(TaskPlanDO taskPlan);

    /**
     * 执行外呼操作
     * @param reqVO
     * @return
     */
    AjaxResult executeCallNotify(DriverNotifyReqVO reqVO);

    /**
     * 自动外呼任务处理
     */
    void autoCallTask();

    /**
     * 处理呼叫超时的司机
     */
    void handleTimeoutDrivers();

    /**
     * 处理任务
     * @param taskPlan
     */
    void processTaskPlan(TaskPlanDO taskPlan);

    /**
     * 处理重复电话号码
     * @param driverMatches
     * @param keepDriverIds
     * @param taskId
     */
    void handleRepPhoneDriver(List<TaskDriverMatchDO> driverMatches, Set<Long> keepDriverIds, Long taskId);
    
    /**
     * 暂停智能外呼
     * 
     * @param taskId 任务ID
     * @return 操作结果
     */
    AjaxResult pauseAutoCall(Long taskId);
    
    /**
     * 开启智能外呼任务
     * 
     * @param taskId 任务ID
     * @return 操作结果
     */
    AjaxResult startAutoCall(Long taskId);

    /**
     * 生成删除方法
     * @param ids
     * @return
     */
    boolean deleteTaskDriver(List<Long> ids);

    /**
     * 更新意向
     * @param id
     * @param hasIntention
     * @return
     */
    Boolean updateIntention(Long id, Integer hasIntention);

    /**
     * 确认成效 & 取消成交
     * @param id
     * @param dealStatus  成交状态
     * @param handleType 1 成交 2 取掉成交
     * @param dealPrice 成交费用
     * @return
     */
    Boolean confirmOrCancelDeal(Long id, Integer dealStatus, Integer handleType, BigDecimal dealPrice,Integer unitCount);
    
    /**
     * 确认成效 & 取消成交
     * @param id
     * @param dealStatus  成交状态(0:未成交，1:已成交，2:已取消)
     * @param handleType 1 成交 2 取掉成交
     * @return
     */
    Boolean confirmOrCancelDeal(Long id, Integer dealStatus, Integer handleType);

    /**
     * 调度作废
     *
     * @param taskId 任务ID
     * @param reason 作废原因
     * @return 操作结果
     */
    AjaxResult cancelSchedule(Long taskId, String reason);

    /**
     * 调度完成
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    AjaxResult completeSchedule(Long taskId);

    /**
     * 重新呼叫司机
     * 用于处理呼叫失败或未接通的情况
     *
     * @param matchId 匹配司机ID
     * @return 操作结果
     */
    AjaxResult recallDriver(Long matchId);

    /**
     * 确认短信发送
     * @param matchId
     * @return
     */
    AjaxResult sendConfirmSms(Long matchId);

    /**
     * 获取任务司机统计数据
     *
     * @param taskPlanId 任务计划ID
     * @return 统计数据，包含匹配司机总数、已呼叫司机数和有意向司机数
     */
    AjaxResult getDriverStatistics(Long taskPlanId);

    /**
     * 查询当天被呼手机号
     * @param driverPhones
     * @param todayStart
     * @param todayEnd
     * @return
     */
    List<TaskDriverMatchDO> selectCalledDriversByPhones(List<String> driverPhones, Date todayStart, Date todayEnd);

    /**
     * 主动呼叫司机
     *
     * @param matchId 匹配司机ID
     * @return 操作结果
     */
    AjaxResult activeCallDriver(Long matchId);
}