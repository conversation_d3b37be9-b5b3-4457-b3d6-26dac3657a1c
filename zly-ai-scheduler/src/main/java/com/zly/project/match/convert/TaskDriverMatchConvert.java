package com.zly.project.match.convert;

import com.zly.project.match.controller.vo.TaskDriverMatchCreateReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchRespVO;
import com.zly.project.match.controller.vo.TaskDriverMatchUpdateReqVO;
import com.zly.project.match.domain.TaskDriverMatchDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 调度任务匹配司机 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskDriverMatchConvert {

    TaskDriverMatchConvert INSTANCE = Mappers.getMapper(TaskDriverMatchConvert.class);

    TaskDriverMatchDO convert(TaskDriverMatchCreateReqVO bean);

    TaskDriverMatchDO convert(TaskDriverMatchUpdateReqVO bean);

    TaskDriverMatchRespVO convert(TaskDriverMatchDO bean);

    List<TaskDriverMatchRespVO> convertList(List<TaskDriverMatchDO> list);

}