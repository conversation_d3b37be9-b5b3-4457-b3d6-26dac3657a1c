package com.zly.project.match.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.web.domain.BaseEntity;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 调度任务匹配司机 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskDriverMatchDO extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 调度任务ID
     */
    private Long taskPlanId;

    /**
     * 外呼系统任务ID
     */
    private String outboundTaskId;

    /**
     * 司机画像ID
     */
    private Long driverProfileId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 身份证号
     */
    private String identityCard;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 匹配度(百分比)
     */
    private Integer matchScore;

    /**
     * 成交状态(0:未成交，1:已成交，2:已取消 3:已作废)
     */
    private Integer dealStatus;

    /**
     * 成交运费(元)
     */
    private BigDecimal freightCost;
    
    /**
     * AI分析原因
     */
    private String aiAnalysis;

    /**
     * 匹配司机是否外呼
     * 1.待呼叫、2.呼叫中、3.已呼叫、4.呼叫失败 5 重新呼叫中 6 号码重复 7 其他任务已呼叫 8 呼叫超时 9 当天已经呼叫
     */
    private Integer isOutbound;

    /**
     * 有无意向
     */
    //有无意向 0:无意向 1:有意向 2 可能有意向
    private Integer hasIntention;

    /**
     * 是否发送短信 0 未发 1 已发
     */
    private Integer isSendSms;

    /**
     * 短信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date smsSendTime;

    /**
     * 是否发送确认短信
     */
    private Integer isConfirmSms;

    /**
     * 确认短信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmSmsTime;

    /**
     * 单位 1:元/车 2元/吨 3元/方
     */
    private Integer unitCount;

}