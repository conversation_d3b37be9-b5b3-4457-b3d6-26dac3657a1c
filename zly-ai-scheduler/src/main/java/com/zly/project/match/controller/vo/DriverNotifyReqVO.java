package com.zly.project.match.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@ApiModel("司机通知请求 VO")
@Data
public class DriverNotifyReqVO {

    @ApiModelProperty(value = "任务ID", required = true)
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    @ApiModelProperty(value = "通知类型（1-短信，2外呼）", required = true)
    private List<Integer> notifyType;

}