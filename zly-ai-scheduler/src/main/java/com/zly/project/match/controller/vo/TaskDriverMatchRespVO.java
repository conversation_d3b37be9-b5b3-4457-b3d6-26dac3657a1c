package com.zly.project.match.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.project.match.domain.DispatchCallDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 调度任务匹配司机 Response VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskDriverMatchRespVO extends TaskDriverMatchBaseVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 最新的调度外呼记录
     */
    private DispatchCallDO dispatchCallDO;

}