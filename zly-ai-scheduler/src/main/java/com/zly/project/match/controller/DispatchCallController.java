package com.zly.project.match.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.match.domain.DispatchCallDO;
import com.zly.project.match.service.IDispatchCallService;

import javax.annotation.Resource;

/**
 * 调度呼叫记录Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/match/dispatch-call")
public class DispatchCallController extends BaseController {

    @Resource
    private IDispatchCallService dispatchCallService;

    /**
     * 查询调度呼叫记录列表
     */
    @PreAuthorize("@ss.hasPermi('match:dispatch-call:list')")
    @GetMapping("/list")
    public TableDataInfo list(DispatchCallDO dispatchCall) {
        startPage();
        List<DispatchCallDO> list = dispatchCallService.selectDispatchCallList(dispatchCall);
        return getDataTable(list);
    }

    /**
     * 导出调度呼叫记录列表
     */
    @PreAuthorize("@ss.hasPermi('match:dispatch-call:export')")
    @Log(title = "调度呼叫记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DispatchCallDO dispatchCall) {
        List<DispatchCallDO> list = dispatchCallService.selectDispatchCallList(dispatchCall);
        ExcelUtil<DispatchCallDO> util = new ExcelUtil<>(DispatchCallDO.class);
        return util.exportExcel(list, "调度呼叫记录数据");
    }

    /**
     * 获取调度呼叫记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('match:dispatch-call:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dispatchCallService.selectDispatchCallById(id));
    }

    /**
     * 新增调度呼叫记录
     */
    @PreAuthorize("@ss.hasPermi('match:dispatch-call:add')")
    @Log(title = "调度呼叫记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DispatchCallDO dispatchCall) {
        return toAjax(dispatchCallService.insertDispatchCall(dispatchCall));
    }

    /**
     * 修改调度呼叫记录
     */
    @PreAuthorize("@ss.hasPermi('match:dispatch-call:edit')")
    @Log(title = "调度呼叫记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DispatchCallDO dispatchCall) {
        return toAjax(dispatchCallService.updateDispatchCall(dispatchCall));
    }

    /**
     * 删除调度呼叫记录
     */
    @PreAuthorize("@ss.hasPermi('match:dispatch-call:remove')")
    @Log(title = "调度呼叫记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dispatchCallService.deleteDispatchCallByIds(ids));
    }
}