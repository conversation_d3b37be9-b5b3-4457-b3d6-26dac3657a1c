package com.zly.project.match.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.utils.MessageUtil;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.TextUtil;
import com.zly.enums.plan.TaskPlanStatusEnum;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.cargo.domain.CarrierDriver;
import com.zly.project.cargo.service.ICarrierDriverService;
import com.zly.project.cargo.service.IDailyCargoService;
import com.zly.project.driver.domain.DriverProfileDO;
import com.zly.project.driver.service.DriverProfileService;
import com.zly.project.match.controller.vo.DriverNotifyReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchCreateReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchPageReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchUpdateReqVO;
import com.zly.project.match.convert.TaskDriverMatchConvert;
import com.zly.project.match.domain.DispatchCallDO;
import com.zly.project.match.domain.TaskDriverMatchDO;
import com.zly.project.match.mapper.TaskDriverMatchMapper;
import com.zly.project.match.service.IDispatchCallService;
import com.zly.project.match.service.TaskDriverMatchService;
import com.zly.project.outbound.domain.*;
import com.zly.project.outbound.service.IBeeBirdService;
import com.zly.project.plan.controller.vo.TaskPlanPageReqVO;
import com.zly.project.plan.controller.vo.TaskPlanUpdateReqVO;
import com.zly.project.plan.convert.TaskPlanConvert;
import com.zly.project.plan.domain.TaskPlanDO;
import com.zly.project.plan.service.TaskPlanService;
import com.zly.project.system.domain.SysDictData;
import com.zly.project.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.BASE_SERVER_ERROR;
import static com.zly.enums.plan.TaskPlanStatusEnum.SCHEDULING;
import static com.zly.framework.web.domain.AjaxResult.*;

/**
 * 调度任务匹配司机 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TaskDriverMatchServiceImpl implements TaskDriverMatchService {

    @Resource
    private TaskDriverMatchMapper taskDriverMatchMapper;

    @Resource
    private IDispatchCallService dispatchCallService;

    @Resource
    private TaskPlanService taskPlanService;

    @Resource
    private IBeeBirdService iBeeBirdService;

    @Resource
    private MessageUtil messageUtil;

    @Resource
    private ISysDictTypeService dictTypeService;

    @Resource
    @Lazy
    private IDailyCargoService dailyCargoService;

    @Resource
    private ICarrierDriverService carrierDriverService;

     /**
     * 发送确认短信
     * 向已成交的司机发送行程确认短信
     *
     * @param matchId 匹配司机ID
     * @return 操作结果
     */
    @Override
    @Transactional
    public AjaxResult sendConfirmSms(Long matchId) {
        log.info("开始发送确认短信，匹配司机ID：{}", matchId);
        try {
            // 获取匹配司机信息
            TaskDriverMatchDO driverMatch = taskDriverMatchMapper.selectTaskDriverMatchById(matchId);
            if (driverMatch == null) {
                return error("匹配司机不存在");
            }
            // 检查司机状态，只有已成交的司机才能发送确认短信
            if (driverMatch.getDealStatus() == null || driverMatch.getDealStatus() != 1) {
                return error("只有已成交的司机才能发送确认短信");
            }
            // 检查是否已经发送过确认短信
            if (driverMatch.getIsConfirmSms() != null && driverMatch.getIsConfirmSms() == 1) {
                return error("已经发送过确认短信，请勿重复发送");
            }
            // 获取任务计划信息
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(driverMatch.getTaskPlanId());
            if (taskPlan == null) {
                return error("任务计划不存在");
            }
            // 构建确认短信内容
            String smsContent = buildConfirmSmsContent(taskPlan);
            log.info("确认短信内容：{}", smsContent);
            // 发送短信
            try {
                AjaxResult result = messageUtil.sendNormalMessage(driverMatch.getTelephone(), smsContent, String.valueOf(driverMatch.getId()));
                if (result.isSuccess()) {
                    // 更新司机确认短信发送状态
                    driverMatch.setIsConfirmSms(1); // 1表示已发送确认短信
                    driverMatch.setConfirmSmsTime(new Date()); // 记录发送时间
                    taskDriverMatchMapper.updateTaskDriverMatchById(driverMatch);
                    log.info("向司机[{}]发送确认短信成功，电话：{}", driverMatch.getDriverName(), driverMatch.getTelephone());
                    return success("确认短信发送成功");
                } else {
                    log.error("向司机[{}]发送确认短信失败，电话：{}", driverMatch.getDriverName(), driverMatch.getTelephone());
                    return error("确认短信发送失败");
                }
            } catch (Exception e) {
                log.error("向司机[{}]发送确认短信异常，电话：{}", driverMatch.getDriverName(), driverMatch.getTelephone(), e);
                return error("确认短信发送异常：" + e.getMessage());
            }
        } catch (Exception e) {
            log.error("发送确认短信操作异常", e);
            return error("发送确认短信失败：" + e.getMessage());
        }
    }

    /**
     * 构建确认短信内容
     * 
     * @param taskPlan 任务计划
     * @return 短信内容
     */
    private String buildConfirmSmsContent(TaskPlanDO taskPlan) {
        StringBuilder content = new StringBuilder("【善道智运】师傅您好，已与您确认了");
        // 添加起始地和目的地
        if (taskPlan.getStartPoint() != null && taskPlan.getEndPoint() != null) {
            content.append(taskPlan.getStartPoint()).append("-").append(taskPlan.getEndPoint());
        } else {
            content.append("本次");
        }
        content.append("的行程，请按时前往装货地。");
        return content.toString();
    }
    
    /**
     * 重新呼叫司机
     * 用于处理呼叫失败或未接通的情况
     *
     * @param matchId 匹配司机ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult recallDriver(Long matchId) {
        log.info("开始执行重新呼叫操作，匹配司机ID：{}", matchId);
        TaskDriverMatchDO driverMatch = taskDriverMatchMapper.selectTaskDriverMatchById(matchId);
        // 获取匹配司机信息
        if (driverMatch == null) {
            return error("匹配司机不存在");
        }
        try {
            // 获取任务计划信息
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(driverMatch.getTaskPlanId());
            if (taskPlan == null) {
                return error("任务计划不存在");
            }
            // 检查任务状态，只有调度中的任务才能重新呼叫
            if (!Objects.equals(taskPlan.getStatus(), SCHEDULING.getStatus())) {
                return error("只有调度中的任务才能执行重新呼叫");
            }
            // 检查司机状态，不能是呼叫中状态
            if (driverMatch.getIsOutbound() != null && (driverMatch.getIsOutbound() == 2 || driverMatch.getIsOutbound() == 6 )) {
                return error("司机正在呼叫中，无法重新呼叫");
            }
            // 检查司机成交状态，已成交或已取消的司机不能重新呼叫
            if (driverMatch.getDealStatus() != null && (driverMatch.getDealStatus() == 1 || driverMatch.getDealStatus() == 2)) {
                return error("已成交或已取消的司机不能重新呼叫");
            }
            // 检查当前时间是否在外呼时间范围内
            LocalDateTime now = LocalDateTime.now();
            boolean isAfter530 = isAfter530(now);
            boolean isBefore9 = now.getHour() < 9;
            if (isAfter530 || isBefore9) {
                return error("当前时间不在外呼时间范围内（9:00-17:30）");
            }
            String telephone = driverMatch.getTelephone();
            List<TaskDriverMatchDO> existingOutboundTasks = taskDriverMatchMapper.selectByTelephoneAndOutboundTaskIdNotNull(telephone);
            if (!existingOutboundTasks.isEmpty()) {
                List<TaskDriverMatchDO> taskDriverMatchDOS = existingOutboundTasks.stream().filter(t -> {
                    Integer isOutbound = t.getIsOutbound();
                    return 2 == isOutbound || 5 == isOutbound || 8 == isOutbound;
                }).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(taskDriverMatchDOS)){
                    return error("该司机存在已经呼叫中的任务，请稍后再试！");
                }
                // 该电话号码已经在其他任务中被外呼过
                TaskDriverMatchDO taskDriverMatchDO = existingOutboundTasks.get(0);
                log.info("司机[{}]的电话号码[{}]已在其他任务中被外呼过，标记为已处理", taskDriverMatchDO.getDriverName(), telephone);
            }
            DispatchCallDO oldDispatchCall = dispatchCallService.getLatestCallByMatchId(driverMatch.getId());
            if (oldDispatchCall == null){
                return error("旧通话记录不存在");
            }
            String extra = oldDispatchCall.getExtra();
            // 创建新的呼叫记录
            DispatchCallDO dispatchCall = new DispatchCallDO();
            dispatchCall.setTaskMatchId(matchId);
            dispatchCall.setCallStatus("待呼叫");
            dispatchCall.setHasIntention(0);
            dispatchCall.setCallTime(new Date());
            dispatchCall.setRemark("手动重新呼叫");
            dispatchCall.setExtra(extra);
            dispatchCallService.insertDispatchCall(dispatchCall);
            // 调用外部接口实现电话重置  重置号码需要保证extra 唯一标识是同一个
            TaskNumber taskNumber = new TaskNumber();
            taskNumber.setTask_id(taskPlan.getOutTaskId());
            ResetNumber resetNumber = new ResetNumber();
            resetNumber.setTask_id(taskPlan.getOutTaskId());
            List<String> telephones = new ArrayList<>(8);
            telephones.add(driverMatch.getTelephone());
            resetNumber.setData(telephones);
            handleTaskBeeBird(taskNumber,2,resetNumber);
            // 更新呼叫记录状态
            dispatchCall.setExternalTaskId(taskPlan.getOutTaskId());
            dispatchCall.setCallStatus("呼叫中");
            dispatchCallService.updateDispatchCall(dispatchCall);
            // 更新匹配司机状态为呼叫中
            driverMatch.setIsOutbound(5); // 5表示二次呼叫中
            driverMatch.setOutboundTaskId(taskPlan.getOutTaskId());
            taskDriverMatchMapper.updateTaskDriverMatchById(driverMatch);
            log.info("重新呼叫司机[{}]成功，电话：{}，外呼任务ID：{}", driverMatch.getDriverName(), driverMatch.getTelephone(), taskPlan.getOutTaskId());
            return success("重新呼叫成功");
        } catch (Exception e) {
            log.error("重新呼叫操作异常", e);
            throw exception(BASE_SERVER_ERROR, "重新呼叫失败");
        }
    }

    public static boolean isAfter530(LocalDateTime now) {
		return now.getHour() > 17 || (now.getHour() == 17 && now.getMinute() >= 30);
    }

    @Override
    public Long createTaskDriverMatch(TaskDriverMatchCreateReqVO createReqVO) {
        // 插入
        TaskDriverMatchDO taskDriverMatch = TaskDriverMatchConvert.INSTANCE.convert(createReqVO);
        // 设置默认值
        if (taskDriverMatch.getDealStatus() == null) {
            taskDriverMatch.setDealStatus(0); // 默认未成交
        }
        taskDriverMatch.setId(TextUtil.generateId());
        // 设置创建者
        taskDriverMatchMapper.insertTaskDriverMatch(taskDriverMatch);
        // 返回
        return taskDriverMatch.getId();
    }

    @Override
    public boolean addTaskDriverMatchBatch(List<TaskDriverMatchDO> list) {
        if (list == null || list.isEmpty()) {
            return false;
        }
        // 设置创建者
        for (TaskDriverMatchDO taskDriverMatch : list) {
            if (taskDriverMatch.getDealStatus() == null) {
                taskDriverMatch.setDealStatus(0); // 默认未成交
            }
            taskDriverMatch.setId(TextUtil.generateId());
        }
        // 批量插入
        return taskDriverMatchMapper.insertBatch(list) > 0;
    }

    @Override
    public void updateTaskDriverMatch(TaskDriverMatchUpdateReqVO updateReqVO) {
        // 校验存在
        validateTaskDriverMatchExists(updateReqVO.getId());
        // 更新
        TaskDriverMatchDO updateObj = TaskDriverMatchConvert.INSTANCE.convert(updateReqVO);
        taskDriverMatchMapper.updateTaskDriverMatchById(updateObj);
    }

    @Override
    public void updateDealStatus(Long id, Integer dealStatus, String updateBy) {
        // 校验存在
        validateTaskDriverMatchExists(id);
        // 更新状态
        taskDriverMatchMapper.updateDealStatus(id, dealStatus, updateBy != null ? updateBy : SecurityUtils.getUsername());
    }

    @Override
    public void deleteTaskDriverMatch(Long id) {
        // 校验存在
        validateTaskDriverMatchExists(id);
        // 删除
        taskDriverMatchMapper.deleteTaskDriverMatchById(id);
    }

    @Override
    public void deleteTaskDriverMatchBatch(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        // 批量删除
        taskDriverMatchMapper.deleteTaskDriverMatchByIds(ids);
    }

    private void validateTaskDriverMatchExists(Long id) {
        if (taskDriverMatchMapper.selectTaskDriverMatchById(id) == null) {
            throw new RuntimeException("调度任务匹配司机不存在");
        }
    }

    @Override
    public TaskDriverMatchDO getTaskDriverMatch(Long id) {
        return taskDriverMatchMapper.selectTaskDriverMatchById(id);
    }

    @Override
    public List<TaskDriverMatchDO> getTaskDriverMatchList(Collection<Long> ids) {
        return taskDriverMatchMapper.selectTaskDriverMatchBatchIds(ids);
    }

    @Override
    public List<TaskDriverMatchDO> getTaskDriverMatchPage(TaskDriverMatchPageReqVO pageReqVO) {
        // 查询分页数据
        return taskDriverMatchMapper.selectTaskDriverMatchList(pageReqVO);
    }

    @Override
    public List<TaskDriverMatchDO> getListByTaskPlanId(Long taskPlanId) {
        return taskDriverMatchMapper.selectListByTaskPlanId(taskPlanId);
    }

    @Override
    public List<TaskDriverMatchDO> getListByDealStatus(Integer dealStatus) {
        return taskDriverMatchMapper.selectListByDealStatus(dealStatus);
    }

    @Override
    @Async
    public void executeSmsNotify(DriverNotifyReqVO reqVO) {
        log.info("开始执行短信通知，任务ID：{}", reqVO.getTaskId());
        try {
            // 获取任务计划信息
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(reqVO.getTaskId());
            if (taskPlan == null) {
                throw exception(BASE_SERVER_ERROR, "任务计划不存在");
            }
            // 获取匹配的司机数据
            List<TaskDriverMatchDO> driverMatches = taskDriverMatchMapper.selectByTaskId(reqVO.getTaskId());
            if (driverMatches.isEmpty()) {
                throw exception(BASE_SERVER_ERROR, "未找到匹配的司机数据");
            }
            Set<Long> keepDriverIds = new HashSet<>();
            handleRepPhoneDriver(driverMatches, keepDriverIds, reqVO.getTaskId());
            // 构建短信内容
            String smsContent = buildSmsContent(taskPlan);
            log.info("短信内容：{}", smsContent);
            // 发送短信
            int successCount = 0;
            List<String> failedPhones = new ArrayList<>();
            driverMatches = driverMatches.stream()
                    .filter(driver -> driver != null && driver.getMatchScore() != null) // 确保driver和matchScore不为null
                    .filter(driver -> keepDriverIds.contains(driver.getId())) // 只保留每个电话号码中分数最高的司机
                    .collect(Collectors.toList());
            for (TaskDriverMatchDO driverMatch : driverMatches) {
                try {
                    Integer isSendSms = driverMatch.getIsSendSms();
                    if (isSendSms != null && isSendSms == 1){
                        log.info("司机[{}]短信已发送", driverMatch.getDriverName());
                        continue;
                    }
                    // 检查司机状态，已成交或已取消的司机不发送短信
                    if (driverMatch.getDealStatus() != null && (driverMatch.getDealStatus() == 1 || driverMatch.getDealStatus() == 2)) {
                        log.info("司机[{}]已成交或已取消，跳过短信发送", driverMatch.getDriverName());
                        continue;
                    }
                    // 发送短信
                    AjaxResult result = messageUtil.sendNormalMessage(driverMatch.getTelephone(), smsContent,"91320830MADCRXEJ3Q");
                    if (result.isSuccess()) {
                        successCount++;
                        // 更新司机短信发送状态
                        driverMatch.setDealStatus(null);
                        driverMatch.setIsOutbound(null);
                        driverMatch.setHasIntention(null);
                        driverMatch.setIsSendSms(1); // 1表示已发送短信
                        driverMatch.setSmsSendTime(new Date());
                        taskDriverMatchMapper.updateTaskDriverMatchById(driverMatch);
                        log.info("向司机[{}]发送短信成功，电话：{}", driverMatch.getDriverName(), driverMatch.getTelephone());
                    } else {
                        failedPhones.add(driverMatch.getTelephone());
                        log.error("向司机[{}]发送短信失败，电话：{}", driverMatch.getDriverName(), driverMatch.getTelephone());
                    }
                } catch (Exception e) {
                    failedPhones.add(driverMatch.getTelephone());
                    log.error("向司机[{}]发送短信异常，电话：{}", driverMatch.getDriverName(), driverMatch.getTelephone(), e);
                }
            }
            List<Integer> notifyType = reqVO.getNotifyType();
            if (notifyType.contains(1)){
                // 更新任务计划状态为调度中
                updateTaskPlanStatus(reqVO.getTaskId(),1,notifyType);
            }
            // 返回结果
            if (successCount == 0) {
                log.error("所有短信发送失败");
            } else if (failedPhones.isEmpty()) {
                log.info("成功发送" + successCount + "条短信");
            } else {
                log.info("成功发送" + successCount + "条短信，失败" + failedPhones.size() + "条");
            }
        } catch (Exception e) {
            log.error("执行短信通知异常", e);
        }
    }
    
    /**
     * 构建短信内容
     * 
     * @param taskPlan 任务计划
     * @return 短信内容
     */
    public String buildSmsContent(TaskPlanDO taskPlan) {
        StringBuilder content = new StringBuilder("师傅您好，");
        // 添加起始地和目的地
        if (taskPlan.getStartPoint() != null && taskPlan.getEndPoint() != null) {
            content.append("有一趟从").append(taskPlan.getStartPoint()).append("到").append(taskPlan.getEndPoint());
        } else {
            content.append("有一趟新的运输任务");
        }
        // 添加货物类型
        if (taskPlan.getCargoName() != null) {
            content.append("的").append(taskPlan.getCargoName());
        }
        // 添加车辆要求
        if (taskPlan.getVehicleType() != null && taskPlan.getVehicleLength() != null) {
            double length = taskPlan.getVehicleLength();
            String lengthStr = length % 1 == 0 ?
                    String.format("%.0f", length) :
                    String.format("%.1f", length);
            content.append("，需要 ").append(lengthStr).append(" 米长的").append(taskPlan.getVehicleType());
        } else if (taskPlan.getVehicleType() != null) {
            content.append("，需要").append(taskPlan.getVehicleType());
        } else if (taskPlan.getVehicleLength() != null) {
            double length = taskPlan.getVehicleLength();
            String lengthStr = length % 1 == 0 ?
                    String.format("%.0f", length) :
                    String.format("%.1f", length);
            content.append("，需要 ").append(lengthStr).append(" 米长的车辆");
        }
        // 添加装货时间
        if (taskPlan.getPlanStartTime() != null) {
            // 将 LocalDateTime 转换为 Date
            LocalDateTime planStartTime = taskPlan.getPlanStartTime();
            Date planStartDate = Date.from(planStartTime.atZone(ZoneId.systemDefault()).toInstant());
            // 格式化日期为"MM月dd日HH点"
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(planStartDate);
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);
            // 判断是否有具体时间（时分秒都为0表示只有日期）
            boolean hasSpecificTime = isHasStartSpecificTime(hour, minute, second);
            if (hasSpecificTime) {
                // 有具体时间，显示时间段
                content.append("，").append(month).append("月").append(day).append("日").append(hour).append("点左右装货");
            } else {
                // 只有日期，不显示具体时间
                content.append("，").append(month).append("月").append(day).append("日").append("装货");
            }
        }
        // 添加运价
        if (taskPlan.getEstimatedCost() != null) {
            content.append("，计划运价是 ").append(taskPlan.getEstimatedCost().intValue()).append(" 元");
        }
        String dispatcherPhone = taskPlan.getDispatcherPhone();
        // 添加固定话术
        content.append("，若有意向，请拨打调度电话：");
        content.append(dispatcherPhone);
        return content.toString();
    }

    @Override
    public AjaxResult executeCallNotify(DriverNotifyReqVO reqVO) {
        // 获取匹配的司机数据
        List<TaskDriverMatchDO> driverMatches = taskDriverMatchMapper.selectByTaskId(reqVO.getTaskId());
        if (driverMatches.isEmpty()) {
            return error("未找到匹配的司机数据");
        }
        List<Integer> notifyType = reqVO.getNotifyType();
        // 更新调度任务状态为开始
        updateTaskPlanStatus(reqVO.getTaskId(),2,notifyType);
        return success();
    }

    /**
     * 更新任务状态
     * @param taskId 任务id
     * @param communicationMethod  1:短信 2 短信+外呼
     */
    private void updateTaskPlanStatus(Long taskId,Integer communicationMethod,List<Integer> notifyType) {
        log.info("updateTaskPlanStatus,taskId:{}", taskId);
        // 获取任务计划
        TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskId);
        if (taskPlan != null) {
            if (communicationMethod!=null && communicationMethod == 1){
                //发送短信结束设置调度状态为完成
                if (!notifyType.contains(2)){
                    //短信发送,不存在外呼的情况才将 沟通方式设置为短信 否则还是设置为null
                    taskPlan.setCommunicationMethod(communicationMethod);
                }
                taskPlan.setStatus(SCHEDULING.getStatus());
                TaskPlanUpdateReqVO respVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
                taskPlanService.updateTaskPlan(respVO);
            }else if (communicationMethod!=null && communicationMethod == 2){
                // 修改自动暂停手动暂停为1表示开始
                taskPlan.setCommunicationMethod(communicationMethod);
                taskPlan.setIsAutoPause(1);
                taskPlan.setIsManualPause(1);
                taskPlan.setStatus(SCHEDULING.getStatus());
                TaskPlanUpdateReqVO respVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
                taskPlanService.updateTaskPlan(respVO);
            }
        } else {
            log.warn("任务计划不存在，任务ID：{}", taskId);
        }
    }

    /**
     * 自动外呼任务处理
     * 根据任务计划状态和条件执行自动外呼
     */
    @Override
    @Transactional
    public void autoCallTask() {
        log.info("开始执行自动外呼任务处理");
        try {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            TaskPlanPageReqVO allPageReqVO = new TaskPlanPageReqVO();
            allPageReqVO.setStatus(SCHEDULING.getStatus());
            // 检查任务是否即将超时或已超时
            List<TaskPlanDO> newAllTaskPlans = taskPlanService.getTaskPlansByCondition(allPageReqVO);
            handleTimeOutStatus(newAllTaskPlans, now);
            List<TaskPlanDO> allTaskPlans = taskPlanService.getTaskPlansByCondition(allPageReqVO);
            if (allTaskPlans.isEmpty()) {
                log.info("没有调度中的任务计划需要处理");
                return;
            }
            boolean isAfter530 = isAfter530(now);
            boolean isBefore9 = now.getHour() < 9;
            // 1.2 每天9点开启外呼功能
            if (now.getHour() >= 9 && !isAfter530) {
                // 查询自动暂停的任务，将其设置为自动开启
                for (TaskPlanDO task : allTaskPlans) {
                    task.setIsAutoPause(1); // 设置为自动开启
                    TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(task);
                    taskPlanService.updateTaskPlan(updateReqVO);
                    log.info("任务[{}]自动开启外呼功能", task.getId());
                }
            }
            // 如果当前时间在17:30之后或9:00之前，暂停所有任务
            if (isAfter530 || isBefore9) {
                for (TaskPlanDO task : allTaskPlans) {
                    if (task.getIsAutoPause() == 1) { // 只处理未自动暂停的任务
                        task.setIsAutoPause(0); // 设置为自动暂停
                        TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(task);
                        taskPlanService.updateTaskPlan(updateReqVO);
                        log.info("任务[{}]因时间限制自动暂停", task.getId());
                    }
                }
                log.info("当前时间{}不在外呼时间范围内，所有任务已暂停", now);
                return;
            }
            // 1 查询符合条件的任务计划列表：短信+外呼类型、调度中状态、手动开始状态
            // 通知类型：2-短信+外呼 1, // 状态：1-调度中 手动暂停状态：1-(开始状态)  任务未超时 或者即将超时
            TaskPlanPageReqVO pageReqVO = new TaskPlanPageReqVO();
            pageReqVO.setCommunicationMethod(2);
            pageReqVO.setStatus(SCHEDULING.getStatus());
            pageReqVO.setIsManualPause(1);
            List<TaskPlanDO> taskPlans = taskPlanService.getTaskPlansByCondition(pageReqVO);
            if (taskPlans.isEmpty()) {
                log.info("没有符合条件的任务计划需要处理");
                return;
            }
            log.info("找到{}个符合条件的任务计划", taskPlans.size());
            // 处理每个任务计划
            for (TaskPlanDO taskPlan : taskPlans) {
                getSelf().processTaskPlan(taskPlan);
            }
        } catch (Exception e) {
            log.error("自动外呼任务处理异常", e);
            throw exception(BASE_SERVER_ERROR, "自动处理外呼任务异常");
        }
    }


    @Override
    @Transactional
    public void handleTimeoutDrivers() {
        log.info("开始处理呼叫超时的司机数据");
        try {
            //处理呼叫超时的数据改成呼叫超时
            List<TaskDriverMatchDO> timeoutCallingDrivers = findTimeoutCallingDrivers();
            log.info("handleTimeoutDrivers,timeoutCallingDrivers:{}",timeoutCallingDrivers);
            // 查询所有呼叫超时的司机数据
            List<TaskDriverMatchDO> timeoutDrivers = taskDriverMatchMapper.selectListByOutboundStatus(8); // 8表示呼叫超时
            if (timeoutDrivers.isEmpty()) {
                log.info("没有呼叫超时的司机数据需要处理");
                return;
            }
            log.info("找到{}个呼叫超时的司机数据", timeoutDrivers.size());
            for (TaskDriverMatchDO driver : timeoutDrivers) {
                if (driver.getOutboundTaskId() == null || StringUtils.isBlank(driver.getTelephone())) {
                    log.warn("司机[{}]的外呼任务ID或电话号码为空，无法处理", driver.getDriverName());
                    continue;
                }
                try {
                    // 调用BeeBirdService获取电话详情
                    AjaxResult result = iBeeBirdService.getPhoneDetail(driver.getOutboundTaskId(), driver.getTelephone());
                    if (!result.isSuccess()) {
                        log.error("获取司机[{}]的电话详情失败：{}", driver.getDriverName(), result.get("msg"));
                        continue;
                    }
                    // 解析电话详情
                    JSONObject data = JSONObject.parseObject(result.get("data").toString());
                    String statusStr = data.getString("status_str");
                    Integer status = data.getInteger("status");
                    log.info("司机[{}]的电话状态：{}，状态码：{}", driver.getDriverName(), statusStr, status);
                    // 获取最新的通话记录
                    DispatchCallDO dispatchCall = dispatchCallService.getLatestCallByMatchId(driver.getId());
                    if (dispatchCall == null) {
                        log.warn("未找到司机[{}]的通话记录", driver.getDriverName());
                        continue;
                    }
                    // 根据状态更新记录
                    if (status == 0) {
                        log.info("司机[{}]的电话仍在等待呼叫，保持超时状态", driver.getDriverName());
                    } else if (status == 1) {
                        // 呼叫成功
                        log.info("司机[{}]的电话呼叫成功，更新状态", driver.getDriverName());
                        // 获取通话记录中的extra标识
                        String extra = dispatchCall.getExtra();
                        if (StringUtils.isNotBlank(extra) && StringUtils.isNotBlank(driver.getOutboundTaskId())) {
                            handleCallListQuery(driver, extra, dispatchCall);
                        } else {
                            // 缺少必要信息，只更新基本状态
                            dispatchCall.setCallStatus("已呼叫");
                            dispatchCallService.updateDispatchCall(dispatchCall);
                            driver.setIsOutbound(3); // 3表示已呼叫
                            taskDriverMatchMapper.updateTaskDriverMatchById(driver);
                            log.info("司机[{}]的通话记录缺少必要信息，已更新基本状态", driver.getDriverName());
                        }
                    } else {
                        // 呼叫失败（其他状态）
                        log.info("司机[{}]的电话呼叫失败，状态：{}，更新状态", driver.getDriverName(), statusStr);
                        dispatchCall.setCallStatus("呼叫失败");
                        dispatchCall.setRemark(statusStr); // 记录失败原因
                        dispatchCallService.updateDispatchCall(dispatchCall);
                        // 更新司机状态
                        driver.setIsOutbound(4); // 4表示呼叫失败
                        taskDriverMatchMapper.updateTaskDriverMatchById(driver);
                    }
                } catch (Exception e) {
                    log.error("处理司机[{}]的电话状态异常", driver.getDriverName(), e);
                    throw exception(BASE_SERVER_ERROR, "处理呼叫超时的司机数据异常");
                }
            }
            log.info("处理呼叫超时的司机数据完成");
        } catch (Exception e) {
            log.error("处理呼叫超时的司机数据异常", e);
            throw exception(BASE_SERVER_ERROR, "处理呼叫超时的司机数据异常");
        }
    }


    public List<TaskDriverMatchDO> findTimeoutCallingDrivers() {
        log.info("开始查询呼叫中且超时的司机数据");
        List<TaskDriverMatchDO> timeoutDrivers = new ArrayList<>();
        try {
            // 查询所有呼叫中的司机数据（状态为2-呼叫中或5-重新呼叫中）
            List<TaskDriverMatchDO> taskDriverMatchDOS = new ArrayList<>(8);
            List<TaskDriverMatchDO> callingDrivers = taskDriverMatchMapper.selectListByOutboundStatus(2);
            List<TaskDriverMatchDO> repCallingDrivers = taskDriverMatchMapper.selectListByOutboundStatus(5);
            taskDriverMatchDOS.addAll(callingDrivers);
            taskDriverMatchDOS.addAll(repCallingDrivers);
            if (taskDriverMatchDOS.isEmpty()) {
                log.info("没有呼叫中的司机数据");
                return timeoutDrivers;
            }
            log.info("找到{}个呼叫中的司机数据", taskDriverMatchDOS.size());
            // 获取所有司机ID
            List<Long> driverIds = taskDriverMatchDOS.stream()
                    .map(TaskDriverMatchDO::getId)
                    .collect(Collectors.toList());
            // 查询最近的呼叫记录
            Map<Long, DispatchCallDO> dispatchCallDOMap = dispatchCallService.getDispatchCallDOMap(driverIds);
            if (dispatchCallDOMap.isEmpty()) {
                log.info("未找到任何司机的呼叫记录");
                return timeoutDrivers;
            }
            // 当前时间
            LocalDateTime now = LocalDateTime.now();
            // 检查每个司机的呼叫时间是否超过10分钟
            for (TaskDriverMatchDO driver : taskDriverMatchDOS) {
                DispatchCallDO dispatchCall = dispatchCallDOMap.get(driver.getId());
                if (dispatchCall == null) {
                    log.warn("司机[{}]没有对应的呼叫记录", driver.getDriverName());
                    continue;
                }
                // 获取呼叫时间
                Date callTime = dispatchCall.getCallTime();
                if (callTime == null) {
                    log.warn("司机[{}]的呼叫记录没有呼叫时间", driver.getDriverName());
                    continue;
                }
                // 转换为LocalDateTime进行比较
                LocalDateTime callDateTime = callTime.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
                // 计算时间差（分钟）
                long minutesDiff = Duration.between(callDateTime, now).toMinutes();
                // 如果超过10分钟，添加到超时列表
                if (minutesDiff >= 10) {
                    log.info("司机[{}]的呼叫已超时{}分钟", driver.getDriverName(), minutesDiff);
                    timeoutDrivers.add(driver);
                    // 更新司机状态为呼叫超时
                    driver.setIsOutbound(8); // 8表示呼叫超时
                    taskDriverMatchMapper.updateTaskDriverMatchById(driver);
                    // 更新呼叫记录状态
                    dispatchCall.setCallStatus("呼叫超时");
                    dispatchCall.setRemark("呼叫时间超过10分钟，系统自动标记为超时");
                    dispatchCallService.updateDispatchCall(dispatchCall);
                }
            }
            log.info("共找到{}个呼叫超时的司机", timeoutDrivers.size());
            return timeoutDrivers;
        } catch (Exception e) {
            log.error("查询呼叫中且超时的司机数据异常", e);
            throw exception(BASE_SERVER_ERROR, "查询呼叫中且超时的司机数据异常");
        }
    }

    /**
     * 处理通话列表查询
     * @param driver
     * @param extra
     * @param dispatchCall
     */
    private void handleCallListQuery(TaskDriverMatchDO driver, String extra, DispatchCallDO dispatchCall) {
        boolean hasIntention = false;
        int page = 1;
        int perPage = 10;
        boolean hasMore;
        while (true) {
            AjaxResult orderListResult = iBeeBirdService.getOrderList(driver.getOutboundTaskId(), driver.getTelephone(), page, perPage);
            if (!orderListResult.isSuccess()) {
                log.error("获取司机[{}]的通话列表失败：{}", driver.getDriverName(), orderListResult.get("msg"));
                break;
            }
            // 解析通话列表数据
            JSONObject orderListData = JSONObject.parseObject(orderListResult.get("data").toString());
            JSONObject meta = orderListData.getJSONObject("meta");
            JSONArray list = orderListData.getJSONArray("list");
            // 检查是否有更多页
            int currentPage = meta.getIntValue("current_page");
            int lastPage = meta.getIntValue("last_page");
            hasMore = currentPage < lastPage;
            for (int i = 0; i < list.size(); i++) {
                JSONObject item = list.getJSONObject(i);
                String itemExtra = item.getString("extra");
                // 找到匹配的通话记录
                if (StringUtils.equals(extra, itemExtra)) {
                    String callId = item.getString("callid");
                    handleCallOrderAndCallOrderVoice(driver, dispatchCall, callId);
                    Integer intentionResults = item.getInteger("intention_results");
                    if (intentionResults != null && (intentionResults == 1 || intentionResults == 2)) {
                        hasIntention = true;
                        dispatchCall.setCallStatus("已呼叫");
                        dispatchCall.setHasIntention(1); // 有意向
                        if (intentionResults == 2){
                            dispatchCall.setHasIntention(2); // 2表示可能有意向
                        }
                        dispatchCallService.updateDispatchCall(dispatchCall);
                        // 更新司机状态
                        driver.setIsOutbound(3); // 3表示已呼叫
                        driver.setHasIntention(dispatchCall.getHasIntention()); // 1表示有意向
                        taskDriverMatchMapper.updateTaskDriverMatchById(driver);
                        log.info("司机[{}]的通话记录显示有意向，已更新状态", driver.getDriverName());
                        try {
                            int rows = taskDriverMatchMapper.updateInterestedDriverCount(driver.getTaskPlanId());
                            log.info("更新任务计划意向司机数量结果: {}", rows > 0 ? "成功" : "失败");
                        } catch (Exception e) {
                            log.error("更新任务计划意向司机数量异常", e);
                        }
                        break;
                    }
                }
            }
            // 如果找到了有意向的记录，或者没有更多页，退出循环
            if (hasIntention || !hasMore) {
                break;
            }
            page++;
        }
        // 如果没有找到有意向的记录，更新为普通的已呼叫状态
        if (!hasIntention) {
            dispatchCall.setCallStatus("已呼叫");
            dispatchCall.setHasIntention(0); // 无意向
            dispatchCallService.updateDispatchCall(dispatchCall);
            driver.setIsOutbound(3); // 3表示已呼叫
            driver.setHasIntention(0); // 0表示无意向
            taskDriverMatchMapper.updateTaskDriverMatchById(driver);
            log.info("司机[{}]的通话记录显示无意向，已更新状态", driver.getDriverName());
        }
    }

    /**
     * 处理通话内容和通话录音地址
     * @param driver
     * @param dispatchCall
     * @param callId
     */
    private void handleCallOrderAndCallOrderVoice(TaskDriverMatchDO driver, DispatchCallDO dispatchCall, String callId) {
        if (StringUtils.isNotBlank(callId)) {
            try {
                // 查询通话记录详情
                AjaxResult callOrderResult = iBeeBirdService.getCallOrderNew(driver.getOutboundTaskId(), callId);
                if (callOrderResult.isSuccess()) {
                    // 保存通话记录详情
                    dispatchCall.setCallContent(callOrderResult.get("data").toString());
                    log.info("获取司机[{}]的通话记录详情成功", driver.getDriverName());
                } else {
                    log.error("获取司机[{}]的通话记录详情失败：{}", driver.getDriverName(), callOrderResult.get("msg"));
                }
                // 查询录音地址
                AjaxResult voiceResult = iBeeBirdService.getCallOrderVoice(driver.getOutboundTaskId(), callId);
                if (voiceResult.isSuccess()) {
                    JSONObject voiceData = JSONObject.parseObject(voiceResult.get("data").toString());
                    String voiceUrl = voiceData.getString("voice_url");
                    if (StringUtils.isNotBlank(voiceUrl)) {
                        // 保存录音地址
                        dispatchCall.setVoiceUrl(voiceUrl);
                        log.info("获取司机[{}]的通话录音地址成功：{}", driver.getDriverName(), voiceUrl);
                        voiceUrl= iBeeBirdService.downloadVoiceUrl(dispatchCall, voiceUrl);
                        dispatchCall.setVoiceUrl(voiceUrl);
                    }

                } else {
                    log.error("获取司机[{}]的通话录音地址失败：{}", driver.getDriverName(), voiceResult.get("msg"));
                }
            } catch (Exception e) {
                log.error("获取司机[{}]的通话记录或录音地址异常", driver.getDriverName(), e);
            }
        }
    }

    /**
     * 处理超时逻辑
     * @param taskPlans 处理计划
     * @param now 当前时间
     */
    private void handleTimeOutStatus(List<TaskPlanDO> taskPlans, LocalDateTime now) {
        if (taskPlans.isEmpty()) {
            log.info("没有调度中的任务计划需要处理");
            return;
        }
        List<TaskPlanDO> updatedTaskPlans = new ArrayList<>();
        Iterator<TaskPlanDO> iterator = taskPlans.iterator();
        while (iterator.hasNext()) {
            TaskPlanDO taskPlan = iterator.next();
            LocalDateTime planStartTime = taskPlan.getPlanStartTime();
            if (planStartTime != null) {
                // 检查是否只有日期部分（时分秒都为0）
                if (planStartTime.getHour() == 0 && planStartTime.getMinute() == 0 && planStartTime.getSecond() == 0) {
                    // 将时间调整为当天的24:00:00（即第二天的00:00:00）
                    planStartTime = planStartTime.plusDays(1);
                    log.info("任务[{}]的计划开始时间只有日期部分，调整为当天24:00:00：{}", taskPlan.getId(), planStartTime);
                }
               // 计算当前时间与计划开始时间的差距（小时）
               long hoursBetween = Duration.between(now, planStartTime).toHours();
               long minutesBetween = Duration.between(now, planStartTime).toMinutes();
                // 如果距离计划开始时间只剩30分钟内，手动暂停呼叫任务
                if (minutesBetween > 0 && minutesBetween <= 30) {
                    log.info("任务[{}]距离计划开始时间只剩{}分钟，自动暂停呼叫", taskPlan.getId(), minutesBetween);
                    taskPlan.setIsManualPause(0);  // 手动暂停
                    updatedTaskPlans.add(taskPlan);
                    // 从处理列表中移除即将开始的任务
                    iterator.remove();
                }
               // 如果距离计划开始时间只剩4小时，标记为即将超时
               if (hoursBetween <= 4 && hoursBetween >= 0) {
                   log.info("任务[{}]距离计划开始时间只剩{}小时，标记为即将超时", taskPlan.getId(), hoursBetween);
                   taskPlan.setTimeoutStatus(1); // 1表示即将超时
                   updatedTaskPlans.add(taskPlan);
                }
                // 如果已经超过计划开始时间，标记为已超时并设置任务状态为超时
                else if (now.isAfter(planStartTime)) {
                    log.info("任务[{}]已超过计划开始时间，标记为已超时", taskPlan.getId());
                    taskPlan.setTimeoutStatus(2); // 2表示已超时
                    taskPlan.setIsManualPause(0);  // 手动暂停
                    updatedTaskPlans.add(taskPlan);
                    // 从处理列表中移除已超时的任务
                    iterator.remove();
                }
            }
        }
        // 批量更新任务状态
        for (TaskPlanDO taskPlan : updatedTaskPlans) {
            TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
            taskPlanService.updateTaskPlan(updateReqVO);
            log.info("更新任务[{}]的超时状态为：{}", taskPlan.getId(), taskPlan.getTimeoutStatus());
        }
    }

    /**
     * 处理单个任务计划
     * @param taskPlan 任务计划
     */
    public void processTaskPlan(TaskPlanDO taskPlan) {
        Long taskId = taskPlan.getId();
        log.info("开始处理任务计划[{}]：{}", taskId, taskPlan.getPlanName());
        try {
            // 判断是否存在呼叫中的司机数据，存在则等待更新呼叫状态，直接返回该任务
            List<TaskDriverMatchDO> callingDrivers = taskDriverMatchMapper.selectListByTaskPlanIdAndOutboundStatus(taskId, 2); // 2表示呼叫中
            if (!callingDrivers.isEmpty()) {
                log.info("任务计划[{}]存在{}个呼叫中的司机，等待呼叫完成后再处理", taskId, callingDrivers.size());
                // 处理呼叫超时的司机
                boolean allTimedOut = true; // 标记是否所有呼叫中的司机都已超时
                LocalDateTime now = LocalDateTime.now();
                for (TaskDriverMatchDO driver : callingDrivers) {
                    // 查询最新一条通话记录
                    DispatchCallDO latestCall = dispatchCallService.getLatestCallByMatchId(driver.getId());
                    if (latestCall != null && latestCall.getCallTime() != null) {
                        // 计算通话开始时间与当前时间的差距（分钟）
                        LocalDateTime callTime = latestCall.getCallTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();
                        long minutesPassed = Duration.between(callTime, now).toMinutes();
                        // 如果超过10分钟，则标记为呼叫超时
                        if (minutesPassed > 10) {
                            log.info("司机[{}]的呼叫已超过10分钟，标记为呼叫超时", driver.getDriverName());
                            // 更新通话记录状态
                            latestCall.setCallStatus("呼叫超时");
                            dispatchCallService.updateDispatchCall(latestCall);
                            // 更新司机外呼状态
                            driver.setIsOutbound(8); // 8表示呼叫超时
                            taskDriverMatchMapper.updateTaskDriverMatchById(driver);
                        } else {
                            // 有未超时的司机，继续等待
                            allTimedOut = false;
                        }
                    }
                }
                // 如果所有司机都已超时，则不返回，继续处理后续司机
                if (!allTimedOut) {
                    log.info("任务计划[{}]存在未超时的呼叫中司机，等待呼叫完成", taskId);
                    return;
                } else {
                    log.info("任务计划[{}]所有呼叫中司机已超时，继续处理后续司机", taskId);
                }
            }
            // 1.3 判断该计划中是否还存在需要调度的司机
            List<TaskDriverMatchDO> driverMatches = taskDriverMatchMapper.selectListByTaskPlanIdAndOutboundStatus(taskId,1);
            if (driverMatches == null || driverMatches.isEmpty()) {
                log.info("任务计划[{}]没有匹配的司机数据", taskId);
                return;
            }
            //添加处理逻辑判断driverMatches 中是否有重复的电话号码
            Set<Long> keepDriverIds = new HashSet<>();
            handleRepPhoneDriver(driverMatches, keepDriverIds, taskId);
            // 过滤出未成交的司机（需要调度的司机），并且只包含每个电话号码中分数最高的司机 isOutbound  == 1 表示未呼叫的司机
            List<TaskDriverMatchDO> undealtDrivers = driverMatches.stream()
                    .filter(driver -> driver != null && driver.getMatchScore() != null) // 确保driver和matchScore不为null
                    .filter(driver -> keepDriverIds.contains(driver.getId())) // 只保留每个电话号码中分数最高的司机
                    .sorted(Comparator.comparing(TaskDriverMatchDO::getMatchScore, Comparator.nullsLast(Comparator.naturalOrder())).reversed())
                    .filter(driver -> driver.getIsOutbound() != null && driver.getIsOutbound() == 1)
                    .collect(Collectors.toList());
            if (undealtDrivers.isEmpty()) {
                // 1.5 不存在需要调度的司机，将任务计划设置为已完成
                taskPlan.setIsManualPause(0); // 0-手动暂停
                TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
                taskPlanService.updateTaskPlan(updateReqVO);
                log.info("任务计划[{}]所有司机已调度完成，设置为已完成状态", taskId);
                return;
            }
            // 1.4 意向司机个数判断 // 统计已有意向的司机数量
            List<TaskDriverMatchDO> allDriverMatches = taskDriverMatchMapper.selectListByTaskPlanId(taskId);
            int interestedDriverCount = (int) allDriverMatches.stream().filter(driver ->  driver.getHasIntention() != null && driver.getHasIntention() == 1).count(); // 1-已成交（有意向）
            // 根据需要车辆数量计算所需意向司机数
            int requiredVehicles = taskPlan.getVehicleCount() != null ? taskPlan.getVehicleCount() : 1;
            int requiredInterestedDrivers = requiredVehicles + 4; // 每辆车需要1+4个意向司机
            if (interestedDriverCount >= requiredInterestedDrivers) {
                // 如果意向司机数量已达到要求，暂停任务
                taskPlan.setIsManualPause(0); // 设置为手动暂停
                TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
                taskPlanService.updateTaskPlan(updateReqVO);
                log.info("任务计划[{}]意向司机数量已达到要求({}个)，手动暂停", taskId, interestedDriverCount);
                return;
            }
            // 执行到这里说明需要继续外呼 获取未外呼过的司机
            List<Long> matchIds = undealtDrivers.stream().map(TaskDriverMatchDO::getId).collect(Collectors.toList());
            // 获取已有通话记录 理论上map是空
            Map<Long, DispatchCallDO> callRecordMap = dispatchCallService.getDispatchCallDOMap(matchIds);
            // 筛选出未外呼过的司机
            List<TaskDriverMatchDO> driversToCall = undealtDrivers.stream()
                    .filter(driver -> {
                        DispatchCallDO dispatchCallDO = callRecordMap.get(driver.getId());
                        return !callRecordMap.containsKey(driver.getId()) || StringUtils.equals(dispatchCallDO.getCallStatus(),"待呼叫");
                    }).limit(5) // 每次最多外呼5个司机
                    .collect(Collectors.toList());
            if (!driversToCall.isEmpty()) {
                log.info("任务计划[{}]开始外呼{}个司机", taskId, driversToCall.size());
                // 执行外呼操作
                TaskNumber taskNumber = new TaskNumber();
                for (TaskDriverMatchDO driver : driversToCall) {
                    try {
                        // 可能存在更新操作
                        DispatchCallDO dispatchCallDO = callRecordMap.get(driver.getId());
                        String uuid = UUID.randomUUID().toString();
                        if (dispatchCallDO == null){
                            // 创建呼叫记录
                            DispatchCallDO dispatchCall = new DispatchCallDO();
                            dispatchCall.setTaskMatchId(driver.getId());
                            dispatchCall.setCallStatus("待呼叫");
                            dispatchCall.setHasIntention(0);
                            dispatchCall.setCallTime(new Date());
                            dispatchCall.setExtra(uuid);
                            dispatchCallService.insertDispatchCall(dispatchCall);
                            dispatchCallDO = dispatchCall;
                        }
                        // 调用外部接口实现电话外呼
                        String externalTaskId = callExternalOutboundService(driver.getTelephone(), taskId.toString(),uuid,taskNumber);
                        // 同时修改状态 调度呼叫记录将状态设置为 呼叫中
                        dispatchCallDO.setExternalTaskId(externalTaskId);
                        dispatchCallDO.setCallStatus("呼叫中");
                        dispatchCallService.updateDispatchCall(dispatchCallDO);
                        // 同时修改调度任务匹配司机 将 isOutbound 设置 呼叫中
                        driver.setIsOutbound(2); // 1表示呼叫中
                        driver.setOutboundTaskId(externalTaskId);
                        taskDriverMatchMapper.updateTaskDriverMatchById(driver);
                        log.info("为司机[{}]创建外呼记录：{}，外呼任务ID：{}", driver.getDriverName(), driver.getTelephone(), externalTaskId);
                    } catch (Exception e) {
                        log.error("为司机[{}]创建外呼记录失败：{}", driver.getDriverName(), e.getMessage(), e);
                        throw exception(BASE_SERVER_ERROR, "处理外呼异常！");
                    }
                }
                // 调用外呼接口
                handleTaskBeeBird(taskNumber,1,null);
            } else {
                log.info("任务计划[{}]没有需要外呼的司机", taskId);
            }
        } catch (Exception e) {
            log.error("处理任务计划[{}]异常", taskId, e);
            throw exception(BASE_SERVER_ERROR, "处理任务异常");
        }
    }


    @Override
    public void handleRepPhoneDriver(List<TaskDriverMatchDO> driverMatches, Set<Long> keepDriverIds, Long taskId) {
        Map<String, List<TaskDriverMatchDO>> phoneMap = new HashMap<>();
        // 按电话号码分组
        for (TaskDriverMatchDO driver : driverMatches) {
            if (driver.getTelephone() != null) {
                phoneMap.computeIfAbsent(driver.getTelephone(), k -> new ArrayList<>()).add(driver);
            }
        }
        List<TaskDriverMatchDO> driversToUpdate = new ArrayList<>();
        // 保存所有需要保留的司机ID（每个电话号码中分数最高的）
        for (Entry<String, List<TaskDriverMatchDO>> entry : phoneMap.entrySet()) {
            List<TaskDriverMatchDO> samePhoneDrivers = entry.getValue();
            if (samePhoneDrivers.size() > 1) {
                // 按匹配分数排序，保留分数最高的
                samePhoneDrivers.sort((d1, d2) -> {
                    if (d1.getMatchScore() == null) return 1;
                    if (d2.getMatchScore() == null) return -1;
                    return d2.getMatchScore().compareTo(d1.getMatchScore());
                });
                // 保留第一个（分数最高的）的ID
                keepDriverIds.add(samePhoneDrivers.get(0).getId());
                // 其余标记为重复
                for (int i = 1; i < samePhoneDrivers.size(); i++) {
                    TaskDriverMatchDO duplicateDriver = samePhoneDrivers.get(i);
                    duplicateDriver.setIsOutbound(6); // 6表示号码重复
                    driversToUpdate.add(duplicateDriver);
                    log.info("任务计划[{}]中发现重复电话号码[{}]，将司机[{}]标记为重复", taskId, duplicateDriver.getTelephone(), duplicateDriver.getDriverName());
                }
            } else {
                // 如果只有一个司机使用这个电话号码，也将其ID添加到保留列表
                keepDriverIds.add(samePhoneDrivers.get(0).getId());
            }
        }
        // 批量更新重复的司机状态
        if (!driversToUpdate.isEmpty()) {
            for (TaskDriverMatchDO driver : driversToUpdate) {
                taskDriverMatchMapper.updateTaskDriverMatchById(driver);
            }
            log.info("任务计划[{}]中共处理{}个重复电话号码的司机", taskId, driversToUpdate.size());
        }
    }

    /**
     * 处理通话
     * @param taskNumber
     * @param handleType  1:正常添加逻辑 2：重置号码 3仅删除逻辑 4 删除号码暂停任务
     */
    private void handleTaskBeeBird(TaskNumber taskNumber,Integer handleType,ResetNumber resetNumber) {
        log.info("handleTaskBeeBird,taskNumber:{},handleType:{},resetNumber:{}",JSONObject.toJSONString(taskNumber),handleType,JSONObject.toJSONString(resetNumber));
        //处理数据为空导致的异常
        String taskId = taskNumber.getTask_id();
        if (StringUtils.isBlank(taskId)){
            return;
        }
        if (1 == handleType){
//            log.info("添加号码-暂停外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
//            AjaxResult stopResult1 = iBeeBirdService.taskStop(taskNumber.getTask_id());
//            if (!stopResult1.isSuccess()) {
//                log.error("调用外呼接口失败：{}", stopResult1.get(MSG_TAG));
//                throw exception(BASE_SERVER_ERROR, "暂停外呼接口失败：" + stopResult1.get(MSG_TAG));
//            }
            log.info("添加号码-开始调用外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
            AjaxResult ajaxResult = iBeeBirdService.taskNumberNew(taskNumber);
            if (!ajaxResult.isSuccess()) {
                log.error("调用外呼接口失败：{}", ajaxResult.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "添加号码外呼接口失败：" + ajaxResult.get(MSG_TAG));
            }
//            log.info("添加号码-调用启动外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
//            AjaxResult startResult = iBeeBirdService.taskStart(taskNumber.getTask_id());
//            if (!startResult.isSuccess()) {
//                log.error("调用外呼接口失败：{}", startResult.get(MSG_TAG));
//                throw exception(BASE_SERVER_ERROR, "启动外呼接口失败：" + startResult.get(MSG_TAG));
//            }
        }else if (2 == handleType){
            log.info("重置号码-暂停外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
            AjaxResult stopResult1 = iBeeBirdService.taskStop(taskNumber.getTask_id());
            if (!stopResult1.isSuccess()) {
                log.error("调用外呼接口失败：{}", stopResult1.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "重置号码-暂停外呼接口失败：" + stopResult1.get(MSG_TAG));
            }
            log.info("重置号码-开始调用外呼接口，请求参数：{}", JSONObject.toJSONString(resetNumber));
            AjaxResult ajaxResult = iBeeBirdService.resetNumber(resetNumber);
            if (!ajaxResult.isSuccess()) {
                log.error("调用外呼接口失败：{}", ajaxResult.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "重置号码外呼接口失败：" + ajaxResult.get(MSG_TAG));
            }
            log.info("重置号码-调用启动外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
            AjaxResult startResult = iBeeBirdService.taskStart(taskNumber.getTask_id());
            if (!startResult.isSuccess()) {
                log.error("调用外呼接口失败：{}", startResult.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "重置号码-启动外呼接口失败：" + startResult.get(MSG_TAG));
            }
        } if (3 == handleType){
            log.info("删除号码-暂停外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
            AjaxResult stopResult1 = iBeeBirdService.taskStop(taskNumber.getTask_id());
            if (!stopResult1.isSuccess()) {
                log.error("调用外呼接口失败：{}", stopResult1.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "暂停外呼接口失败：" + stopResult1.get(MSG_TAG));
            }
            log.info("删除号码-调用删除外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
            AjaxResult ajaxResult1 = iBeeBirdService.deleteNumber(resetNumber);
            if (!ajaxResult1.isSuccess()) {
                log.error("调用删除接口失败：{}", ajaxResult1.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "调用删除接口失败：" + ajaxResult1.get(MSG_TAG));
            }
            log.info("删除号码-调用启动外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
            AjaxResult startResult = iBeeBirdService.taskStart(taskNumber.getTask_id());
            if (!startResult.isSuccess()) {
                log.error("调用外呼接口失败：{}", startResult.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "启动外呼接口失败：" + startResult.get(MSG_TAG));
            }
        }else if (4 == handleType){
            log.info("仅暂停外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
            AjaxResult stopResult1 = iBeeBirdService.taskStop(taskNumber.getTask_id());
            if (!stopResult1.isSuccess()) {
                log.error("调用外呼接口失败：{}", stopResult1.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "仅暂停外呼接口失败：" + stopResult1.get(MSG_TAG));
            }
            log.info("仅删除号码-调用删除外呼接口，请求参数：{}", JSONObject.toJSONString(taskNumber));
            AjaxResult ajaxResult1 = iBeeBirdService.deleteNumber(resetNumber);
            if (!ajaxResult1.isSuccess()) {
                log.error("调用仅删除接口失败：{}", ajaxResult1.get(MSG_TAG));
                throw exception(BASE_SERVER_ERROR, "调用仅删除接口失败：" + ajaxResult1.get(MSG_TAG));
            }
        }
    }

    /**
     * 调用外部外呼服务
     * @param telephone 电话号码
     * @param taskId 任务ID
     * @return 外部任务ID
     */
    private String callExternalOutboundService(String telephone, String taskId,String uuid,TaskNumber taskNumber) {
        // 这里实现调用外部外呼服务的逻辑
        log.info("调用外部外呼服务，电话：{}，任务ID：{}", telephone, taskId);
        int count = 0;
        try {
            // 获取任务计划信息，用于填充变量
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(Long.valueOf(taskId));
            if (taskPlan == null) {
                log.error("任务计划不存在，任务ID：{}", taskId);
                throw exception(BASE_SERVER_ERROR, "任务计划不存在");
            }
            //外呼任务ID
            Integer sessionType = taskPlan.getSessionType();
            String outTaskId = taskPlan.getOutTaskId();
            Integer taskState = taskPlan.getTaskState(); //id 为空 状态为null 表示第一次需要添加
            if (StringUtils.isBlank(outTaskId) && (null == taskState)){
                // 查询任务列表 暂停中的
                List<Task> stopTaskList = iBeeBirdService.getTaskList(1);
                if (stopTaskList.isEmpty()) {
                    log.error("外呼任务列表为空，无法进行外呼");
                    outTaskId = createOutTaskAndUpdateTaskPlanState(sessionType, taskPlan);
                }else {
                    boolean isUserFlag = false;
                    List<SysDictData> zlyAiTaskGroup = dictTypeService.selectDictDataByType("zly_ai_task_group");
                    List<Task> filterSessionTypeTaskList = stopTaskList.stream().filter(t -> {
                        Integer id = t.getGroup().getId();
                        if (CollectionUtil.isNotEmpty(zlyAiTaskGroup)) {
                            for (SysDictData sysDictData : zlyAiTaskGroup) {
                                String dictValue = sysDictData.getDictValue();
                                if (StringUtils.isNotBlank(dictValue) && Objects.equals(sessionType, Integer.valueOf(dictValue))) {
                                    String sessionTaskId = sysDictData.getRemark();
                                    if (Objects.equals(id, Integer.valueOf(sessionTaskId))) {
                                        return true;
                                    }
                                }
                            }
                        }
						return false;
					}).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(filterSessionTypeTaskList)){
                        for (Task task : filterSessionTypeTaskList) {
                            // 查询是否存在正在使用该uuidStr的任务计划
                            String uuidStr = task.getUuid();
                            List<TaskPlanDO> existingPlanList = taskPlanService.getTaskPlanByOutTaskIdAndStatus(uuidStr, SCHEDULING.getStatus());
                            if (CollectionUtil.isNotEmpty(existingPlanList)) {
                                for (TaskPlanDO taskPlanDO : existingPlanList) {
                                    if (1 == taskPlanDO.getTaskState()) {
                                        isUserFlag = true;
                                        break;
                                    }
                                }
                            }
                            if (isUserFlag){
                                //存在占用的数据则跳过此循环
                                continue;
                            }
                            // 未占用，更新当前任务计划
                            taskPlan.setSessionTaskId(task.getGroup().getId());
                            taskPlan.setOutTaskId(uuidStr);
                            taskPlan.setTaskState(1); // 1表示占用
                            TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
                            taskPlanService.updateTaskPlan(updateReqVO);
                            outTaskId = task.getUuid();
                            break;
                        }
                        if (StringUtils.isBlank(taskPlan.getOutTaskId())){
                            outTaskId = createOutTaskAndUpdateTaskPlanState(sessionType, taskPlan);
                        }
                    }else {
                        outTaskId = createOutTaskAndUpdateTaskPlanState(sessionType,taskPlan);
                    }
                }
            }
            log.info("获取到外呼任务：{}，任务ID：{}", taskPlan.getPlanName(),outTaskId);
            // 获取变量字段列表
            List<VariableField> fieldList = iBeeBirdService.getFieldList();
            if (fieldList == null || fieldList.isEmpty()) {
                log.error("外呼变量字段列表为空");
                throw exception(BASE_SERVER_ERROR, "外呼变量字段列表为空");
            }
            // 构建外呼任务请求
            taskNumber.setTask_id(outTaskId);
            // 创建电话呼叫内容列表
            List<TaskNumber.TaskDetail> dataList = taskNumber.getData();
            if (CollectionUtil.isEmpty(dataList)){
                dataList = new ArrayList<>();
                taskNumber.setData(dataList);
            }
            TaskNumber.TaskDetail taskDetail = new TaskNumber.TaskDetail();
            // 设置电话呼叫内容
            taskDetail.setPhone(telephone);
            taskDetail.setSort(++count);
            // 生成唯一标识UUID，用于匹配回调接口
            taskDetail.setExtra(uuid);
            SysDictData sysDictData = null;
            List<SysDictData> zlyAiTaskGroup = dictTypeService.selectDictDataByType("zly_ai_task_component");
            if (CollectionUtil.isNotEmpty(zlyAiTaskGroup)){
                sysDictData = zlyAiTaskGroup.stream()
                        .filter(dictData -> StringUtils.equals(dictData.getIsDefault(),"Y"))
                        .findFirst().orElse(null);
            }
            // 创建语音内容组件列表
            List<TaskNumber.TaskComponents> componentsList = getTaskComponents(fieldList, taskPlan,sysDictData);
            // 设置语音内容组件到电话呼叫内容
            taskDetail.setComponents(componentsList);
            // 添加电话呼叫内容到列表
            dataList.add(taskDetail);
            return outTaskId;
        } catch (Exception e) {
            log.error("调用外部外呼服务异常", e);
            throw exception(BASE_SERVER_ERROR, "调用外部外呼服务异常：" + e.getMessage());
        }
    }

    /**
     * 创建外呼计划并更新状态
     * @param sessionType
     * @param taskPlan
     * @return
     */
    private String createOutTaskAndUpdateTaskPlanState(Integer sessionType, TaskPlanDO taskPlan) {
        String outTaskId;
        List<SysDictData> zlyAiTaskGroup = dictTypeService.selectDictDataByType("zly_ai_task_group");
        SysDictData sysDictData = zlyAiTaskGroup.stream()
                .filter(dictData -> Objects.equals(Integer.valueOf(dictData.getDictValue()), sessionType))
                .findFirst().orElse(null);
        if (sysDictData == null){
            throw exception(BASE_SERVER_ERROR, "字典数据[话术组]不存在");
        }
        Integer dialTimeId = null;
        String lineId = null;
        AjaxResult taskInitInfo = iBeeBirdService.getTaskInitInfo();
        if (taskInitInfo.isSuccess()) {
            TaskInitInfo initInfo = (TaskInitInfo) taskInitInfo.get("data");
            if (initInfo != null) {
                if (initInfo.getDialTimeId() != null) {
                    dialTimeId = initInfo.getDialTimeId();
                    log.info("从API获取到拨号时间ID: {}", dialTimeId);
                }
                if (StringUtils.isNotBlank(initInfo.getLineId())) {
                    lineId = initInfo.getLineId();
                    log.info("从API获取到线路ID: {}", lineId);
                }
            }
        } else {
            log.warn("获取任务初始化信息失败: {}", taskInitInfo.get(MSG_TAG));
            throw exception(BASE_SERVER_ERROR, "获取任务初始化信息失败"+ taskInitInfo.get(MSG_TAG));
        }
        String dictValue = sysDictData.getRemark();
        CreateTask createTask = new CreateTask();
        createTask.setName(sysDictData.getDictLabel() + "-" + System.currentTimeMillis()+"-" + taskPlan.getId());
        createTask.setDestination_extension(Integer.valueOf(dictValue)); // 默认分机号
        createTask.setMaximumcall(5); // 默认最大呼叫次数
        //呼叫时间ID 实时去查询
        createTask.setDial_time_id(dialTimeId); // 默认拨号时间ID
        // 设置任务额外参数
        CreateTask.TaskExtras extras = new CreateTask.TaskExtras();
        extras.setStart_time(LocalDateTime.now().toString());
        extras.setStop_time(LocalDateTime.now().plusYears(1).toString());
        extras.setLine(lineId);
        createTask.setTask_extras(extras);
        AjaxResult task = iBeeBirdService.createTask(createTask);
        if (task.isSuccess()){
            Map<String,String> data = (Map<String, String>) task.get("data");
            String taskUuid = data.get("id");
            // 未占用，更新当前任务计划
            taskPlan.setSessionTaskId(createTask.getDestination_extension());
            taskPlan.setOutTaskId(taskUuid);
            taskPlan.setTaskState(1); // 1表示占用
            TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
            taskPlanService.updateTaskPlan(updateReqVO);
            outTaskId = taskUuid;
        }else {
            throw exception(BASE_SERVER_ERROR, "创建外呼任务失败"+ task.get(MSG_TAG));
        }
        return outTaskId;
    }

    @NotNull
    private static List<TaskNumber.TaskComponents> getTaskComponents(List<VariableField> fieldList, TaskPlanDO taskPlan,SysDictData sysDictData) {
        List<TaskNumber.TaskComponents> componentsList = new ArrayList<>();
        // 遍历字段列表，根据字段ID和名称设置对应的值
        for (VariableField field : fieldList) {
            TaskNumber.TaskComponents component = new TaskNumber.TaskComponents();
            component.setId(field.getId());
            switch (field.getName()) {
            case "运输线路":
                // 原话术先保留
//                String goodsName = StringUtils.isNotBlank(taskPlan.getCargoName()) ? taskPlan.getCargoName() : "货物";
//                component.setValue("有一趟从" + taskPlan.getStartPoint() + "到" + taskPlan.getEndPoint() + "的" + goodsName);
                String goodsName = StringUtils.isNotBlank(taskPlan.getCargoName()) ? taskPlan.getCargoName() : "货物";
                String transportDate = "";
                if (sysDictData == null){
                    if (taskPlan.getPlanStartTime() != null) {
                        LocalDateTime planStartTime = taskPlan.getPlanStartTime();
                        transportDate = planStartTime.getDayOfMonth() + "号";
                    }
                    component.setValue("有趟从" + taskPlan.getStartPoint() + "到" + taskPlan.getEndPoint() + "的" + goodsName + "需要" + transportDate + "装货");
                }else {
                    String dictLabel = sysDictData.getDictLabel();
                    if (StringUtils.equals(dictLabel,"运输路线一")){
                        if (taskPlan.getPlanStartTime() != null) {
                            LocalDateTime planStartTime = taskPlan.getPlanStartTime();
                            transportDate = planStartTime.getDayOfMonth() + "号";
                        }
                        String dictValue = sysDictData.getDictValue();
                        //"有趟从%s到%s的%s需要%s装货"
                        String routeInfo = String.format(dictValue, taskPlan.getStartPoint(),taskPlan.getEndPoint(),goodsName,transportDate);
                        component.setValue(routeInfo);
                    }
                }
                break;
            case "车辆信息":
                String vehicleInfo = getVehicleInfo(taskPlan);
                component.setValue(vehicleInfo);
                break;
            case "运输时间":
//                if (taskPlan.getPlanStartTime() != null) {
//                    LocalDateTime planStartTime = taskPlan.getPlanStartTime();
//                    int month = planStartTime.getMonthValue();
//                    int day = planStartTime.getDayOfMonth();
//                    int hour = planStartTime.getHour();
//                    int minute = planStartTime.getMinute();
//                    // 判断是否是明天
//                    LocalDateTime tomorrow = LocalDateTime.now().plusDays(1);
//                    boolean isTomorrow = planStartTime.toLocalDate().equals(tomorrow.toLocalDate());
//                    String timePrefix = isTomorrow ? "明天" : (month + "月" + day + "日");
//                    // 判断是否有具体时间（时分秒都为0表示只有日期）
//                    boolean hasSpecificTime = !(hour == 0 && minute == 0 && planStartTime.getSecond() == 0);
//                    if (hasSpecificTime) {
//                        // 有具体时间，显示时间段
//                        String timePeriod;
//                        if (hour >= 5 && hour < 12) {
//                            timePeriod = "早上";
//                        } else if (hour >= 12 && hour < 18) {
//                            timePeriod = "下午";
//                        } else {
//                            timePeriod = "晚上";
//                        }
//                        component.setValue(timePrefix + timePeriod + " " + hour + "点左右装货");
//                    } else {
//                        // 只有日期，不显示具体时间
//                        component.setValue(timePrefix + "装货");
//                    }
//                }
                String value = formatTransportTime(taskPlan);
                component.setValue("需要"+value);
                break;
            case "价格":
            case "确认价格":
//                if (taskPlan.getEstimatedCost() != null) {
//                    component.setValue("计划运价是" + taskPlan.getEstimatedCost() + "元");
//                }
                if (taskPlan.getEstimatedCost() != null) {
                    BigDecimal cost = taskPlan.getEstimatedCost();
                    // 判断是否为整数（小数部分为0）
                    String costStr = cost.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0 ?
                            String.valueOf(cost.intValue()) : cost.toString();
                    component.setValue(costStr + "元");
                }
                break;
            case "地址":
                component.setValue(taskPlan.getStartPoint());
                break;
            default:
                component.setValue("");
                break;
            }
            componentsList.add(component);
        }
        return componentsList;
    }


    /**
     * 格式化运输时间
     * 处理各种情况的运输时间格式化
     * @param taskPlan 任务计划
     * @return 格式化后的运输时间字符串
     */
    private static String formatTransportTime(TaskPlanDO taskPlan) {
        if (taskPlan.getPlanStartTime() == null) {
            return "";
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = taskPlan.getPlanStartTime();
        LocalDateTime endTime = taskPlan.getPlanEndTime();
        // 判断是否是明天
        LocalDateTime tomorrow = now.plusDays(1);
        boolean isStartTomorrow = isStartTomorrow(startTime, tomorrow);
        // 获取开始时间的基本信息
        int startDay = startTime.getDayOfMonth();
        int startHour = startTime.getHour();
        int startMinute = startTime.getMinute();
        // 判断开始时间是否有具体时间点（时分秒都为0表示只有日期）
        boolean hasStartSpecificTime = isHasStartSpecificTime(startHour, startMinute, startTime.getSecond());
        // 获取开始时间的时间段描述
        String startTimePeriod = getTimePeriod(startHour);
        // 开始时间前缀（明天或具体日期）
        String startTimePrefix = isStartTomorrow ? "明天" : startDay + "号";
        // 如果结束时间为空，只处理开始时间
        if (endTime == null) {
            if (hasStartSpecificTime) {
                // 有具体时间，显示时间段
                return startTimePrefix + startTimePeriod + startHour + "点装货";
            } else {
                // 只有日期，不显示具体时间
                return startTimePrefix + "装货";
            }
        }
        // 结束时间不为空，处理各种组合情况
        boolean isEndTomorrow = isStartTomorrow(endTime, tomorrow);
        int endDay = endTime.getDayOfMonth();
        int endHour = endTime.getHour();
        int endMinute = endTime.getMinute();
        // 判断结束时间是否有具体时间点
        boolean hasEndSpecificTime = isHasStartSpecificTime(endHour, endMinute, endTime.getSecond());
        // 结束时间前缀
        String endTimePrefix = isEndTomorrow ? "明天" : endDay + "号";
        // 开始和结束是否是同一天
        boolean isSameDay = isStartTomorrow(startTime, endTime);
        StringBuilder result = new StringBuilder();
        // 开始日期和结束日期都是明天
        if (isStartTomorrow && isEndTomorrow) {
            if (hasStartSpecificTime) {
                // 开始时间填写时间 明天早上9点到16点装货
                result.append("明天").append(startTimePeriod).append(startHour).append("点到");
            } else {
                // 开始时间未填写时间 明天晚上0点到16点装货
                result.append("明天");
            }
            if (hasEndSpecificTime) {
                // 结束时间填写时间则显示时间
                result.append(endHour).append("点装货");
            }else {
                result.append("装货");
            }
        }
        // 开始日期是明天和结束日期不是明天
        else if (isStartTomorrow) {
            if (hasStartSpecificTime) {
                // 开始时间填写时间 明天早上9点到16号16点装货
                result.append("明天").append(startTimePeriod).append(startHour).append("点到");
            } else {
                // 开始时间未填写时间 明天晚上0点到16号16点装货
                result.append("明天到");
            }
            if (hasEndSpecificTime) {
                // 结束时间填写时间则显示时间
                result.append(endTimePrefix).append(endHour).append("点装货");
            } else {
                // 不填则显示16号24点
                result.append(endTimePrefix).append("装货");
            }
        }
        // 开始日期不是明天和结束日期不是明天
        else {
            if (hasStartSpecificTime) {
                // 开始时间填写时间 16号早上9点到16号16点装货
                result.append(startTimePrefix).append(startTimePeriod).append(startHour).append("点到");
            } else {
                // 开始时间未填写时间 16号晚上0点到16号16点装货
                result.append(startTimePrefix);
            }
            if (isSameDay) {
                // 如果是同一天，不重复日期
                if (hasEndSpecificTime) {
                    result.append(endHour).append("点装货");
                } else {
                    result.append("装货");
                }
            } else {
                // 不同天，需要显示结束日期
                if (hasEndSpecificTime) {
                    result.append(endTimePrefix).append(endHour).append("点装货");
                } else {
                    result.append(endTimePrefix).append("装货");
                }
            }
        }
        return result.toString();
    }

    private static boolean isHasStartSpecificTime(int hour, int minute, int time) {
		return !(hour == 0 && minute == 0 && time == 0);
    }

    public static boolean isStartTomorrow(LocalDateTime startTime, LocalDateTime tomorrow) {
		return startTime.toLocalDate().equals(tomorrow.toLocalDate());
    }

    /**
     * 根据小时获取时间段描述（早上、下午、晚上）
     * @param hour 小时
     * @return 时间段描述
     */
    private static String getTimePeriod(int hour) {
        if (hour >= 5 && hour < 12) {
            return "早上";
        } else if (hour >= 12 && hour < 18) {
            return "下午";
        } else {
            return "晚上";
        }
    }



    @NotNull
    private static String getVehicleInfo(TaskPlanDO taskPlan) {
        //String vehicleInfo = "需要";
        String vehicleInfo = "";
        if (taskPlan.getVehicleLength() != null) {
            Double length = taskPlan.getVehicleLength();
            String lengthStr = length % 1 == 0 ?
                    String.format("%.0f", length) :
                    String.format("%.1f", length);
            vehicleInfo += lengthStr + "米的";
        }
        if (taskPlan.getVehicleType() != null) {
            vehicleInfo += taskPlan.getVehicleType();
        } else {
            vehicleInfo += "平板车";
        }
        return vehicleInfo;
    }

    /**
     * 暂停智能外呼
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @Override
    public AjaxResult pauseAutoCall(Long taskId) {
        log.info("暂停智能外呼，任务ID：{}", taskId);
        try {
            // 1. 判断调度任务是否调度中，不是调度中的无法暂停
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskId);
            if (taskPlan == null) {
                return error("当前任务不存在");
            }
            // 判断任务是否在调度中状态
            if (!SCHEDULING.getStatus().equals(taskPlan.getStatus())) {
                return error("只有调度中的任务才能暂停");
            }
            // 判断状态isManualPause是否已经是暂停状态
            if (taskPlan.getIsManualPause() == 0) {
                return error("任务已经是暂停状态");
            }
            // 是调度中的则修改状态，将状态isManualPause设置为手动暂停
            taskPlan.setIsManualPause(0); // 0表示手动暂停
            TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
            taskPlanService.updateTaskPlan(updateReqVO);
            log.info("任务[{}]已手动暂停智能外呼", taskId);
            return success("暂停成功");
        } catch (Exception e) {
            log.error("暂停智能外呼异常，任务ID：{}", taskId, e);
            return error("暂停失败：" + e.getMessage());
        }
    }

    /**
     * 开启智能外呼任务
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @Override
    public AjaxResult startAutoCall(Long taskId) {
        log.info("开启智能外呼任务，任务ID：{}", taskId);
        try {
            // 1. 判断是否是调度中，不是调度中无法开启
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskId);
            if (taskPlan == null) {
                return error("当前任务不存在");
            }
            // 判断任务是否在调度中状态
            if (!SCHEDULING.getStatus().equals(taskPlan.getStatus())) {
                return error("只有调度中的任务才能开启");
            }
            // 判断状态isManualPause是否已经是开启状态
            if (taskPlan.getIsManualPause() == 1) {
                return error("任务已经是开启状态");
            }
            // 判断当前时间是否是9点到5:30这个区间
            LocalDateTime now = LocalDateTime.now();
            boolean isAfter530 = isAfter530(now);
            boolean isBefore9 = now.getHour() < 9;
            if (isAfter530 || isBefore9) {
                return error("当前时间不在外呼时间范围内（9:00-17:30）");
            }
            long minutesBetween = Duration.between(now, taskPlan.getPlanStartTime()).toMinutes();
            // 如果距离计划开始时间只剩30分钟内，手动暂停呼叫任务
            if (minutesBetween > 0 && minutesBetween <= 30) {
                log.info("任务距离计划开始时间只剩{}分钟无法手动开启任务", minutesBetween);
                // 从处理列表中移除即将开始的任务
                return error("任务距离计划开始时间已不足半小时无法手动开启任务");
            }
            // 判断是否还存在需要外呼的司机，不存在则返回报错
            List<TaskDriverMatchDO> driverMatches = taskDriverMatchMapper.selectListByTaskPlanId(taskId);
            List<TaskDriverMatchDO> undealtDrivers = driverMatches.stream().sorted(Comparator.comparing(TaskDriverMatchDO::getMatchScore).reversed()).filter(driver -> driver.getIsOutbound() == 1)
                    .collect(Collectors.toList());
            if (undealtDrivers.isEmpty()) {
                return error("没有需要外呼的司机");
            }
            // 判断意向司机是否已经达到，达到则返回报错
            int interestedDriverCount = (int) driverMatches.stream().filter(driver -> driver.getHasIntention() == 1).count();
            int requiredVehicles = taskPlan.getVehicleCount() != null ? taskPlan.getVehicleCount() : 1;
            int requiredInterestedDrivers = requiredVehicles + 4;
            if (interestedDriverCount >= requiredInterestedDrivers) {
                return error("意向司机数量已达到要求，无需继续外呼");
            }
            // 都满足条件则更新isManualPause状态为开启
            taskPlan.setCommunicationMethod(2);
            taskPlan.setIsManualPause(1); // 1表示开启
            TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
            taskPlanService.updateTaskPlan(updateReqVO);
            log.info("任务[{}]已手动开启智能外呼", taskId);
            return success("开启成功");
        } catch (Exception e) {
            log.error("开启智能外呼任务异常，任务ID：{}", taskId, e);
            return error("开启失败：" + e.getMessage());
        }
    }

    @Override
    public boolean deleteTaskDriver(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw exception(BASE_SERVER_ERROR, "请选择要删除的记录");
        }
        // 检查是否有呼叫中的司机
        for (Long id : ids) {
            TaskDriverMatchDO driverMatch = getSelf().getTaskDriverMatch(id);
            // 获取当前匹配司机信息
            if (driverMatch == null) {
                throw exception(BASE_SERVER_ERROR, "司机不存在");
            }
            if (driverMatch.getIsOutbound() != null && driverMatch.getIsOutbound() == 2) {
                throw exception(BASE_SERVER_ERROR, "选中的司机中有正在呼叫中的司机，无法删除");
            }
        }
        // 执行批量删除
        getSelf().deleteTaskDriverMatchBatch(ids);
        return true;
    }

    @Override
    public Boolean updateIntention(Long id, Integer hasIntention) {
        log.info("updateIntention,id:{},hasIntention:{}",id,hasIntention);
        // 获取当前匹配司机信息
        TaskDriverMatchDO driverMatch = getSelf().getTaskDriverMatch(id);
        if (driverMatch == null) {
            throw exception(BASE_SERVER_ERROR, "匹配司机不存在");
        }
        // 判断是否在呼叫中
        if (driverMatch.getIsOutbound() != null && driverMatch.getIsOutbound() == 2) {
            throw exception(BASE_SERVER_ERROR, "司机正在呼叫中，无法变更意向状态");
        }
        // 更新意向状态
        driverMatch.setHasIntention(hasIntention);
        TaskDriverMatchUpdateReqVO updateReqVO = new TaskDriverMatchUpdateReqVO();
        updateReqVO.setId(id);
        updateReqVO.setHasIntention(hasIntention);
        getSelf().updateTaskDriverMatch(updateReqVO);
        return null;
    }

    @Override
    public Boolean confirmOrCancelDeal(Long id, Integer dealStatus, Integer handleType, BigDecimal dealPrice,Integer unitCount) {
        log.info("confirmOrCancelDeal,taskId:{},dealStatus:{},handleType:{},dealPrice:{},unitCount:{}",id,dealStatus,handleType,dealPrice,unitCount);
        // 获取当前匹配司机信息
        TaskDriverMatchDO driverMatch = getTaskDriverMatch(id);
        if (driverMatch == null) {
            throw exception(BASE_SERVER_ERROR, "匹配司机不存在");
        }
        // 判断是否在呼叫中
        if (driverMatch.getIsOutbound() != null && driverMatch.getIsOutbound() == 2) {
            throw exception(BASE_SERVER_ERROR, "司机正在呼叫中，无法操作");
        }
        if (handleType == 1){
            if (driverMatch.getDealStatus() == 1){
                throw exception(BASE_SERVER_ERROR, "当前状态已经是已成交");
            }
        }else if (handleType == 2){
            if (driverMatch.getDealStatus() == 2){
                throw exception(BASE_SERVER_ERROR, "当前状态已经是已取消");
            }
        }
        // 更新成交状态
        TaskDriverMatchUpdateReqVO updateReqVO = new TaskDriverMatchUpdateReqVO();
        updateReqVO.setId(id);
        updateReqVO.setDealStatus(dealStatus);
        // 如果是确认成交且提供了成交费用，则更新成交费用
        if (dealStatus == 1 && dealPrice != null) {
            // 设置成交费用，保留两位小数
            updateReqVO.setFreightCost(dealPrice.setScale(2, RoundingMode.HALF_UP));
            updateReqVO.setUnitCount(unitCount);
        }
        updateTaskDriverMatch(updateReqVO);
        if (driverMatch.getDriverProfileId() != null) {
            try {
                // 获取司机画像服务
                DriverProfileService driverProfileService = SpringUtil.getBean(DriverProfileService.class);
                DriverProfileDO driverProfileDO = new DriverProfileDO();
                driverProfileDO.setId(driverMatch.getDriverProfileId());
                if (1 == handleType){
                    driverProfileDO.setCooperativeStatus(1);
                }else if (2 == handleType){
                    driverProfileDO.setCooperativeStatus(0);
                }
                driverProfileDO.setUpdateBy(SecurityUtils.getUsername());
                driverProfileDO.setUpdateTime(new Date());
                // 更新司机画像
                int i = driverProfileService.updateDriverProfile(driverProfileDO);
                log.info("已将司机[{}]的合作状态更新为合作状态，状态：{}", driverMatch.getDriverName(),i);
            } catch (Exception e) {
                log.error("更新司机[{}]的合作状态失败", driverMatch.getDriverName(), e);
            }
        }
        //处理承运司机数据上传
        if (1 == handleType){
            handleUploadCarrierDriver(dealStatus, driverMatch);
        }else if (2 == handleType){
            List<CarrierDriver> carrierDrivers = carrierDriverService.selectDriversByTaskDriverMatchId(driverMatch.getId());
            if (CollectionUtil.isNotEmpty(carrierDrivers)){
                dailyCargoService.cancelCarrierDriver(id,0);
            }
        }
        return true;
    }

    /**
     * 承运司机数据上传
     * @param dealStatus 成交状态
     * @param driverMatch 匹配司机
     */
    private void handleUploadCarrierDriver(Integer dealStatus, TaskDriverMatchDO driverMatch) {
        Long taskPlanId = driverMatch.getTaskPlanId();
        if (taskPlanId != null && dealStatus == 1) {
            try {
                // 获取任务计划
                TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskPlanId);
                if (taskPlan != null && taskPlan.getDailyCargoId() != null) {
                    // 创建承运司机对象
                    CarrierDriver carrierDriver = getCarrierDriver(taskPlan, taskPlanId, driverMatch);
                    // 上传承运司机信息
                    int result = dailyCargoService.uploadCarrierDriver(carrierDriver);
                    if (result > 0) {
                        // 更新运力配置状态
                        log.info("已将司机[{}]的承运信息同步到每日货源[{}]", driverMatch.getDriverName(), taskPlan.getDailyCargoId());
                    } else {
                        log.error("同步司机[{}]的承运信息到每日货源[{}]失败", driverMatch.getDriverName(), taskPlan.getDailyCargoId());
                    }
                }
            } catch (Exception e) {
                log.error("同步司机承运信息到每日货源失败", e);
            }
        }
    }

    /**
     * 组装司机承运信息
     * @param taskPlan
     * @param taskPlanId
     * @param driverMatch
     * @return
     */
    @NotNull
    private static CarrierDriver getCarrierDriver(TaskPlanDO taskPlan, Long taskPlanId, TaskDriverMatchDO driverMatch) {
        CarrierDriver carrierDriver = new CarrierDriver();
        carrierDriver.setCargoId(taskPlan.getDailyCargoId());
        carrierDriver.setTaskId(taskPlanId);
        carrierDriver.setTaskDriverMatchId(driverMatch.getId());
        carrierDriver.setDriverName(driverMatch.getDriverName());
        carrierDriver.setIdCard(driverMatch.getIdentityCard());
        carrierDriver.setPhoneNumber(driverMatch.getTelephone());
        // 默认车牌信息
        carrierDriver.setPlateNumber(null);
        carrierDriver.setPlateColor(null);
        carrierDriver.setAcceptTime(new Date());
        carrierDriver.setCarrierStatus("0"); // 待接单
        carrierDriver.setStatus("0"); // 正常
        carrierDriver.setCreateBy(SecurityUtils.getUsername());
        carrierDriver.setCreateTime(new Date());
        return carrierDriver;
    }

    @Override
    public Boolean confirmOrCancelDeal(Long id, Integer dealStatus, Integer handleType) {
        // 调用带成交费用的方法，传入null表示不设置成交费用
        return confirmOrCancelDeal(id, dealStatus, handleType, null,null);
    }

    @Override
    @Transactional
    public AjaxResult cancelSchedule(Long taskId, String reason) {
        log.info("开始执行调度作废操作，任务ID：{}，作废原因：{}", taskId, reason);
        try {
            // 获取任务计划
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskId);
            if (taskPlan == null) {
                return error("任务计划不存在");
            }
            // 检查任务状态，只有调度中的任务才能作废
            if (!Objects.equals(taskPlan.getStatus(), SCHEDULING.getStatus())) {
                return error("只有调度中的任务才能作废");
            }
            List<TaskDriverMatchDO> existingOutboundTasks = taskDriverMatchMapper.selectByTaskId(taskId);
            if (CollectionUtil.isNotEmpty(existingOutboundTasks)){
                boolean hasCallingTask = existingOutboundTasks.stream()
                        .anyMatch(task -> task.getIsOutbound() != null && (task.getIsOutbound() == 2 || task.getIsOutbound() == 5));
                if (hasCallingTask) {
                    // 存在呼叫中的任务，需要被拦截，设置为任务中被外呼
                    return error("存在呼叫中或重新呼叫的司机，请稍后执行作废操作");
                }
            }
            // 更新任务状态为已作废
            taskPlan.setStatus(TaskPlanStatusEnum.CANCELLED.getStatus());
            taskPlan.setIsManualPause(0); // 设置为暂停状态
            taskPlan.setIsAutoPause(0);   // 设置为暂停状态
            taskPlan.setUpdateBy(SecurityUtils.getUsername());
            taskPlan.setCancelTime(new Date());
            taskPlan.setUpdateTime(new Date());
            taskPlan.setRemark(reason);
            TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
            taskPlanService.updateTaskPlan(updateReqVO);
            //每日货源ID不为空完成调度计划时需要更新每日调度货源的状态
            Long dailyCargoId = taskPlan.getDailyCargoId();
            if (dailyCargoId != null){
                dailyCargoService.updateAiScheduleStatus(dailyCargoId, "2", taskPlan.getId());
            }
            // 更新所有未成交的匹配司机状态
            List<TaskDriverMatchDO> driverMatches = taskDriverMatchMapper.selectListByTaskPlanId(taskId);
            for (TaskDriverMatchDO driverMatch : driverMatches) {
                // 只更新未成交的司机状态
                if (driverMatch.getDealStatus() == 0) {
                    driverMatch.setDealStatus(3); // 3-已作废
                    taskDriverMatchMapper.updateTaskDriverMatchById(driverMatch);
                }
            }
            log.info("调度作废操作完成，任务ID：{}", taskId);
            // 外呼暂停删除号码操作
            handleTaskBeeBirdStopAndDelNumberOpt(taskPlan.getId(), driverMatches);
            return success("调度作废成功");
        } catch (Exception e) {
            log.error("调度作废操作异常", e);
            throw exception(BASE_SERVER_ERROR, "调度作废操作异常");
        }
    }

    /**
     * 外呼暂停删除号码操作
     * @param taskId 计划ID
     * @param driverMatches 司机集合
     */
    private void handleTaskBeeBirdStopAndDelNumberOpt(Long taskId, List<TaskDriverMatchDO> driverMatches) {
        TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskId);
        if (taskPlan == null) {
            throw exception(BASE_SERVER_ERROR, "调度任务不存在");
        }
        TaskPlanUpdateReqVO updateReqVO;
        TaskNumber taskNumber = new TaskNumber();
        taskNumber.setTask_id(taskPlan.getOutTaskId());
        ResetNumber resetNumber = new ResetNumber();
        List<String> phoneList = driverMatches.stream().map(TaskDriverMatchDO::getTelephone).collect(Collectors.toList());
        resetNumber.setData(phoneList);
        resetNumber.setTask_id(taskPlan.getOutTaskId());
        handleTaskBeeBird(taskNumber,4,resetNumber);
        //修改状态为 释放
        taskPlan.setTaskState(2);
        updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
        taskPlanService.updateTaskPlan(updateReqVO);
    }

    @Override
    @Transactional
    public AjaxResult completeSchedule(Long taskId) {
        log.info("开始执行调度完成操作，任务ID：{}", taskId);
        try {
            // 获取任务计划
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskId);
            if (taskPlan == null) {
                return error("任务计划不存在");
            }
            // 检查任务状态，只有调度中的任务才能完成
            if (!Objects.equals(taskPlan.getStatus(), SCHEDULING.getStatus())) {
                return error("只有调度中的任务才能标记为完成");
            }
            // 检查是否有成交的司机
            List<TaskDriverMatchDO> driverMatches = taskDriverMatchMapper.selectListByTaskPlanId(taskId);
            if (CollectionUtil.isNotEmpty(driverMatches)){
                boolean hasCallingTask = driverMatches.stream()
                        .anyMatch(task -> task.getIsOutbound() != null && (task.getIsOutbound() == 2 || task.getIsOutbound() == 5));
                if (hasCallingTask) {
                    // 存在呼叫中的任务，需要被拦截，设置为任务中被外呼
                    return error("存在呼叫中或重新呼叫的司机，请稍后执行完成操作");
                }
            }
            long dealCount = driverMatches.stream().filter(driver -> driver.getDealStatus() == 1).count();
            // 更新任务状态为已完成
            taskPlan.setStatus(TaskPlanStatusEnum.COMPLETED.getStatus());
            taskPlan.setIsManualPause(0); // 设置为暂停状态
            taskPlan.setIsAutoPause(0);   // 设置为暂停状态
            taskPlan.setUpdateBy(SecurityUtils.getUsername());
            taskPlan.setCompleteTime(new Date());
            taskPlan.setUpdateTime(new Date());
            TaskPlanUpdateReqVO updateReqVO = TaskPlanConvert.INSTANCE.convertUpdateReqVO(taskPlan);
            taskPlanService.updateTaskPlan(updateReqVO);
            //每日货源ID不为空完成调度计划时需要更新每日调度货源的状态
            Long dailyCargoId = taskPlan.getDailyCargoId();
            if (dailyCargoId != null){
                dailyCargoService.updateAiScheduleStatus(dailyCargoId, "2", taskPlan.getId());
            }
            // 更新所有未成交的匹配司机状态
            for (TaskDriverMatchDO driverMatch : driverMatches) {
                // 只更新未成交的司机状态
                if (driverMatch.getDealStatus() == 0) {
                    driverMatch.setDealStatus(2); // 2-已取消
                    taskDriverMatchMapper.updateTaskDriverMatchById(driverMatch);
                }
            }
            // 计算成交费用总额
            BigDecimal totalDealPrice = driverMatches.stream()
                    .filter(driver -> driver.getDealStatus() == 1 && driver.getFreightCost() != null)
                    .map(TaskDriverMatchDO::getFreightCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("调度完成操作完成，任务ID：{}，成交司机数：{}，成交总额：{}", taskId, dealCount, totalDealPrice);
            Map<String, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("dealCount", dealCount);
            objectObjectHashMap.put("totalDealPrice", totalDealPrice.setScale(2, RoundingMode.HALF_UP));

            handleTaskBeeBirdStopAndDelNumberOpt(taskPlan.getId(), driverMatches);
            return success(objectObjectHashMap);
        } catch (Exception e) {
            log.error("调度完成操作异常", e);
            throw exception(BASE_SERVER_ERROR, "调度完成操作异常");
        }
    }
    
    /**
     * 获取任务司机统计数据
     *
     * @param taskPlanId 任务计划ID
     * @return 统计数据，包含匹配司机总数、已呼叫司机数和有意向司机数
     */
    @Override
    public AjaxResult getDriverStatistics(Long taskPlanId) {
        log.info("获取任务司机统计数据，任务ID：{}", taskPlanId);
        try {
            // 获取任务计划
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskPlanId);
            if (taskPlan == null) {
                return error("任务计划不存在");
            }
            // 获取该任务下所有匹配的司机
            List<TaskDriverMatchDO> driverMatches = taskDriverMatchMapper.selectListByTaskPlanId(taskPlanId);
            if (CollectionUtil.isEmpty(driverMatches)) {
                // 如果没有匹配司机，返回全部为0的统计数据
                Map<String, Object> emptyStats = new HashMap<>();
                emptyStats.put("totalDrivers", 0);
                emptyStats.put("calledDrivers", 0);
                emptyStats.put("interestedDrivers", 0);
                emptyStats.put("dealDrivers", 0);
                emptyStats.put("communicationMethod", 0);
                return success(emptyStats);
            }
            // 统计数据
            int totalDrivers = driverMatches.size(); // 匹配司机总数
            // 已呼叫司机数（isOutbound为3-已呼叫、4-呼叫失败、5-重新呼叫）
            long calledDrivers = driverMatches.stream()
                    .filter(driver -> driver.getIsOutbound() != null && 
                            (driver.getIsOutbound() == 3 || 
                             driver.getIsOutbound() == 4 || 
                             driver.getIsOutbound() == 5))
                    .count();
            // 有意向司机数（hasIntention为1的司机）
            long interestedDrivers = driverMatches.stream()
                    .filter(driver -> driver.getHasIntention() != null && driver.getHasIntention() == 1)
                    .count();
            // 已成交司机数（dealStatus为1的司机）
            long dealDrivers = driverMatches.stream()
                    .filter(driver -> driver.getDealStatus() != null && driver.getDealStatus() == 1)
                    .count();
            // 封装结果
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalDrivers", totalDrivers);
            statistics.put("calledDrivers", calledDrivers);
            statistics.put("interestedDrivers", interestedDrivers);
            statistics.put("dealDrivers", dealDrivers);
            Integer communicationMethod = taskPlan.getCommunicationMethod();
            if (communicationMethod == null){
                statistics.put("communicationMethod", 0);
            }else {
                statistics.put("communicationMethod", communicationMethod);
            }
            String buildSmsContent = getSelf().buildSmsContent(taskPlan);
            statistics.put("buildSmsContent", buildSmsContent);
            log.info("任务[{}]统计数据：共匹配到{}位司机，已呼叫{}位，{}位有意向，{}位已成交",
                    taskPlanId, totalDrivers, calledDrivers, interestedDrivers, dealDrivers);
            return success(statistics);
        } catch (Exception e) {
            log.error("获取任务司机统计数据异常", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    @Override
    public List<TaskDriverMatchDO> selectCalledDriversByPhones(List<String> driverPhones, Date todayStart, Date todayEnd) {
        return taskDriverMatchMapper.selectCalledDriversByPhones(driverPhones,todayStart,todayEnd);
    }

    @Override
    public AjaxResult activeCallDriver(Long matchId) {
        try {
            // 1. 获取匹配司机信息
            TaskDriverMatchDO driverMatch = taskDriverMatchMapper.selectTaskDriverMatchById(matchId);
            if (driverMatch == null) {
                return error("未找到匹配司机信息");
            }
            // 2. 获取任务计划信息
            Long taskPlanId = driverMatch.getTaskPlanId();
            TaskPlanDO taskPlan = taskPlanService.getTaskPlan(taskPlanId);
            if (taskPlan == null) {
                return error("未找到任务计划信息");
            }
            // 3. 判断任务状态是否为进行中
            if (taskPlan.getStatus() == null || !TaskPlanStatusEnum.SCHEDULING.getStatus().equals(taskPlan.getStatus())) {
                return error("只有进行中的任务才能执行主动呼叫");
            }
            // 4. 判断是否为当天已呼叫状态(is_outbound=9)
            if (driverMatch.getIsOutbound() == null || driverMatch.getIsOutbound() != 9) {
                return error("只有当前已呼叫的司机才能执行主动呼叫");
            }
            // 5. 设置为待呼叫状态
            driverMatch.setIsOutbound(1); // 1表示待呼叫
            driverMatch.setUpdateTime(new Date());
            driverMatch.setUpdateBy(SecurityUtils.getUsername());
            taskDriverMatchMapper.updateTaskDriverMatchById(driverMatch);
            log.info("司机[{}]已设置为待呼叫状态，电话：{}", driverMatch.getDriverName(), driverMatch.getTelephone());
            return success("已将司机设置为待呼叫状态");
        } catch (Exception e) {
            log.error("主动呼叫操作异常", e);
            return error("主动呼叫失败：" + e.getMessage());
        }
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private TaskDriverMatchServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
