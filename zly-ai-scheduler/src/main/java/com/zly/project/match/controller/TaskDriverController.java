package com.zly.project.match.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.utils.DateUtils;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.match.controller.vo.DriverNotifyReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchPageReqVO;
import com.zly.project.match.controller.vo.TaskDriverMatchRespVO;
import com.zly.project.match.controller.vo.TaskDriverMatchUpdateReqVO;
import com.zly.project.match.convert.TaskDriverMatchConvert;
import com.zly.project.match.domain.DispatchCallDO;
import com.zly.project.match.domain.TaskDriverMatchDO;
import com.zly.project.match.service.IDispatchCallService;
import com.zly.project.match.service.TaskDriverMatchService;
import com.zly.project.model.controller.vo.ModelDataRepVo;
import com.zly.project.model.controller.vo.ModelDriverRepVo;
import com.zly.project.model.controller.vo.ModelDriverReqVo;
import com.zly.project.model.controller.vo.ModelParseReqVO;
import com.zly.project.model.service.ModelParseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *  Controller
 * <AUTHOR>
 */
@Api(tags ="管理后台 - 调度任务司机")
@RestController
@RequestMapping("/scheduler/task/driver")
@Validated
@Slf4j
public class TaskDriverController extends BaseController {

    private static final String AUTO_CALL_TASK_LOCK = "AUTO_CALL_TASK_LOCK";

    private static final long LOCK_WAIT_TIME = 0;

    // 将租约时间设置为-1，启用看门狗机制，自动续期
    private static final long LOCK_LEASE_TIME = -1;

    @Resource
    private TaskDriverMatchService taskDriverMatchService;

    @Resource
    private IDispatchCallService dispatchCallService;

    @Resource
    private RedissonClient redissonClient;

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:list')")
    @GetMapping("/list")
    @ApiOperation("查询匹配司机列表")
    public TableDataInfo<TaskDriverMatchRespVO> list(TaskDriverMatchPageReqVO pageReqVO) {
        startPage();
        List<TaskDriverMatchDO> list = taskDriverMatchService.getTaskDriverMatchPage(pageReqVO);
        List<TaskDriverMatchRespVO> taskDriverMatchRespVOS = TaskDriverMatchConvert.INSTANCE.convertList(list);
        if (CollectionUtil.isNotEmpty(list)){
            List<Long> longList = taskDriverMatchRespVOS.stream().map(TaskDriverMatchRespVO::getId).collect(Collectors.toList());
            Map<Long,DispatchCallDO> dispatchCallDOMap = dispatchCallService.getDispatchCallDOMap(longList);
            if (CollectionUtil.isNotEmpty(taskDriverMatchRespVOS)){
                for (TaskDriverMatchRespVO taskDriverMatchRespVO : taskDriverMatchRespVOS) {
                    Long id = taskDriverMatchRespVO.getId();
                    DispatchCallDO dispatchCallDO = dispatchCallDOMap.get(id);
                    taskDriverMatchRespVO.setDispatchCallDO(dispatchCallDO);
                }
            }
        }
        return getDataTable(taskDriverMatchRespVOS);
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:executeNotify')")
    @PostMapping("/notify")
    @ApiOperation("执行司机通知")
    public AjaxResult executeNotify(@RequestBody @Valid DriverNotifyReqVO reqVO) {
        List<Integer> notifyType = reqVO.getNotifyType();
        if (CollectionUtil.isEmpty(notifyType)){
            return AjaxResult.error("通知类型不能为空!");
        }
        try {
            for (Integer type : notifyType) {
                // 根据处理类型执行不同的通知方式
                if (1 == type){ // 短信通知
                    taskDriverMatchService.executeSmsNotify(reqVO); // 异步执行短信通知
                }else if (2 == type){ // 外呼
                    AjaxResult ajaxResult = taskDriverMatchService.executeCallNotify(reqVO);
                    log.info("executeNotify,ajaxResult:{}",ajaxResult);
                }
            }
            return AjaxResult.success("已执行通知");
        } catch (Exception e) {
            log.error("执行司机通知失败", e);
            return error("执行通知失败：" + e.getMessage());
        }
	}

    @PostMapping("/auto-call")
    @ApiOperation("自动处理任务")
    public AjaxResult autoCallTask() {
        // 创建分布式锁
        RLock lock = redissonClient.getLock(AUTO_CALL_TASK_LOCK);
        boolean locked = false;
        try {
            //尝试获取锁，不等待，启用看门狗机制自动续期
            locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (locked) {
                log.info("{}获取到分布式锁，开始自动外呼任务处理", DateUtils.getTime());
                // 执行自动外呼任务
                taskDriverMatchService.autoCallTask();
                log.info("{}自动外呼任务处理成功", DateUtils.getTime());
                return AjaxResult.success("自动外呼任务处理成功");
            } else {
                log.info("{}未获取到分布式锁，跳过本次自动外呼任务", DateUtils.getTime());
                return AjaxResult.error("系统繁忙，请稍后再试");
            }
        } catch (Exception e) {
            log.error("自动外呼任务处理失败：{}", e.getMessage(), e);
            return AjaxResult.error("自动外呼任务处理失败：" + e.getMessage());
        } finally {
            // 确保锁被释放
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("{}释放分布式锁", DateUtils.getTime());
            }
        }
    }

    @PostMapping("/handleTimeoutDrivers")
    @ApiOperation("处理呼叫超时的司机")
    public AjaxResult handleTimeoutDrivers() {
        try {
            taskDriverMatchService.handleTimeoutDrivers();
            return AjaxResult.success("处理呼叫超时司机成功");
        } catch (Exception e) {
            return AjaxResult.error("处理呼叫超时司机失败：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:pause-call')")
    @PostMapping("/pause-call/{taskId}")
    @ApiOperation("暂停智能外呼")
    public AjaxResult pauseAutoCall(@PathVariable("taskId") Long taskId) {
        return taskDriverMatchService.pauseAutoCall(taskId);
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:start-call')")
    @PostMapping("/start-call/{taskId}")
    @ApiOperation("手动开启智能外呼任务")
    public AjaxResult startAutoCall(@PathVariable("taskId") Long taskId) {
        return taskDriverMatchService.startAutoCall(taskId);
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:update-intention')")
    @PutMapping("/intention/{id}/{hasIntention}")
    @ApiOperation("变更司机意向状态")
    public AjaxResult updateIntention(@PathVariable("id") Long id, @PathVariable("hasIntention") Integer hasIntention) {
        try {
            Boolean isSuccess = taskDriverMatchService.updateIntention(id, hasIntention);
            return AjaxResult.success(isSuccess);
        } catch (Exception e) {
            return AjaxResult.error("变更意向状态失败：" + e.getMessage());
        }
    }

    /**
     * 确认成交
     */
    @PreAuthorize("@ss.hasPermi('match:task-driver-match:confirm-deal')")
    @ApiOperation("确认成交")
    @PutMapping("/confirm-deal/{id}")
    public AjaxResult confirmDeal(@PathVariable("id") Long id,
            @RequestParam(value = "dealPrice", required = false) BigDecimal dealPrice,
            @RequestParam(value = "unitCount", required = false) Integer unitCount) {
        try {
            Boolean isSuccess;
            if (dealPrice != null) {
                // 如果提供了成交费用，则调用带成交费用的方法
                isSuccess = taskDriverMatchService.confirmOrCancelDeal(id, 1, 1, dealPrice,unitCount);
            } else {
                // 否则调用原有方法
                isSuccess = taskDriverMatchService.confirmOrCancelDeal(id, 1, 1);
            }
            return AjaxResult.success(isSuccess);
        } catch (Exception e) {
            return AjaxResult.error("确认成交失败：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:cancel-deal')")
    @ApiOperation("取消成交")
    @PutMapping("/cancel-deal/{id}")
    public AjaxResult cancelDeal(@PathVariable("id") Long id) {
        try {
            Boolean isSuccess = taskDriverMatchService.confirmOrCancelDeal(id, 2, 2);
            return AjaxResult.success(isSuccess);
        } catch (Exception e) {
            return AjaxResult.error("取消成交失败：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:delete')")
    @ApiOperation("删除匹配司机")
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {
        try {
            ArrayList<Long> ids
                    = new ArrayList<>(8);
            ids.add(id);
            boolean isSuccess = taskDriverMatchService.deleteTaskDriver(ids);
            return AjaxResult.success(isSuccess);
        } catch (Exception e) {
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:delete')")
    @ApiOperation("批量删除匹配司机")
    @DeleteMapping("/batch")
    public AjaxResult batchDelete(@RequestBody List<Long> ids) {
        try {
            boolean isSuccess = taskDriverMatchService.deleteTaskDriver(ids);
            return AjaxResult.success(isSuccess);
        } catch (Exception e) {
            return AjaxResult.error("批量删除失败：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:cancelSchedule')")
    @ApiOperation("调度作废")
    @PutMapping("/cancel-schedule/{taskId}")
    public AjaxResult cancelSchedule(@PathVariable("taskId") Long taskId,@RequestParam(value = "reason", required = false) String reason) {
        try {
            return taskDriverMatchService.cancelSchedule(taskId, reason);
        } catch (Exception e) {
            return AjaxResult.error("调度作废失败：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:completeSchedule')")
    @ApiOperation("调度完成")
    @PutMapping("/complete-schedule/{taskId}")
    public AjaxResult completeSchedule(@PathVariable("taskId") Long taskId) {
        try {
            return taskDriverMatchService.completeSchedule(taskId);
        } catch (Exception e) {
            return AjaxResult.error("调度完成失败：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:recallDriver')")
    @PostMapping("/recall/{id}")
    @ApiOperation("重新呼叫司机")
    public AjaxResult recallDriver(@PathVariable("id") Long id) {
        try {
            return taskDriverMatchService.recallDriver(id);
        } catch (Exception e) {
            log.error("重新呼叫司机失败", e);
            return error("重新呼叫失败：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:sendConfirmSms')")
    @ApiOperation("发送确认短信")
    @PutMapping("/sendConfirmSms/{matchId}")
    public AjaxResult sendConfirmSms(@PathVariable("matchId") Long matchId) {
        try {
            return taskDriverMatchService.sendConfirmSms(matchId);
        } catch (Exception e) {
            return AjaxResult.error("发送确认短信：" + e.getMessage());
        }
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:getDriverStatistics')")
    @GetMapping("/statistics/{taskPlanId}")
    @ApiOperation("获取任务司机统计数据")
    public AjaxResult getDriverStatistics(@PathVariable("taskPlanId") Long taskPlanId) {
        return taskDriverMatchService.getDriverStatistics(taskPlanId);
    }

    @PreAuthorize("@ss.hasPermi('match:task-driver-match:activeCallDriver')")
    @ApiOperation("主动呼叫司机")
    @PostMapping("/activeCall/{matchId}")
    public AjaxResult activeCallDriver(@PathVariable("matchId") Long matchId) {
        return taskDriverMatchService.activeCallDriver(matchId);
    }

}