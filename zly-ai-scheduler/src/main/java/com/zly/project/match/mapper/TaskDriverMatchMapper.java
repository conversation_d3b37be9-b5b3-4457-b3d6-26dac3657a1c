package com.zly.project.match.mapper;

import com.zly.project.match.controller.vo.TaskDriverMatchPageReqVO;
import com.zly.project.match.domain.TaskDriverMatchDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 调度任务匹配司机 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskDriverMatchMapper {

    /**
     * 查询调度任务匹配司机列表
     *
     * @param reqVO 查询条件
     * @return 调度任务匹配司机列表
     */
    List<TaskDriverMatchDO> selectTaskDriverMatchList(TaskDriverMatchPageReqVO reqVO);

    /**
     * 根据ID查询调度任务匹配司机
     *
     * @param id 主键ID
     * @return 调度任务匹配司机
     */
    TaskDriverMatchDO selectTaskDriverMatchById(Long id);

    /**
     * 根据调度任务ID查询匹配司机列表
     *
     * @param taskPlanId 调度任务ID
     * @return 匹配司机列表
     */
    List<TaskDriverMatchDO> selectListByTaskPlanId(Long taskPlanId);

    /**
     * 根据成交状态查询匹配司机列表
     *
     * @param dealStatus 成交状态
     * @return 匹配司机列表
     */
    List<TaskDriverMatchDO> selectListByDealStatus(Integer dealStatus);

    /**
     * 根据任务计划ID和外呼状态查询匹配司机列表
     *
     * @param taskPlanId 任务计划ID
     * @param outboundStatus 外呼状态
     * @return 匹配司机列表
     */
    List<TaskDriverMatchDO> selectListByTaskPlanIdAndOutboundStatus(Long taskPlanId, Integer outboundStatus);

    /**
     * 批量查询调度任务匹配司机
     *
     * @param ids ID集合
     * @return 调度任务匹配司机列表
     */
    List<TaskDriverMatchDO> selectTaskDriverMatchBatchIds(Collection<Long> ids);

    /**
     * 插入调度任务匹配司机
     *
     * @param taskDriverMatch 调度任务匹配司机
     * @return 影响行数
     */
    int insertTaskDriverMatch(TaskDriverMatchDO taskDriverMatch);

    /**
     * 批量插入调度任务匹配司机
     *
     * @param list 调度任务匹配司机列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<TaskDriverMatchDO> list);

    /**
     * 更新调度任务匹配司机
     *
     * @param taskDriverMatch 调度任务匹配司机
     * @return 影响行数
     */
    int updateTaskDriverMatchById(TaskDriverMatchDO taskDriverMatch);

    /**
     * 更新成交状态
     *
     * @param id 主键ID
     * @param dealStatus 成交状态
     * @param updateBy 更新者
     * @return 影响行数
     */
    int updateDealStatus(@Param("id") Long id, @Param("dealStatus") Integer dealStatus, @Param("updateBy") String updateBy);

    /**
     * 删除调度任务匹配司机
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteTaskDriverMatchById(Long id);

    /**
     * 批量删除调度任务匹配司机
     *
     * @param ids ID集合
     * @return 影响行数
     */
    int deleteTaskDriverMatchByIds(Collection<Long> ids);

    /**
     * 根据任务id查询 匹配司机
     * @param taskId  任务id
     * @return
     */
    List<TaskDriverMatchDO> selectByTaskId(Long taskId);

    /**
     * 根据电话号码和外呼任务ID不为空查询司机匹配记录
     *
     * @param telephone 电话号码
     * @return 司机匹配记录列表
     */
    List<TaskDriverMatchDO> selectByTelephoneAndOutboundTaskIdNotNull(String telephone);

    /**
     * 更新任务计划意向司机数量
     *
     * @param taskPlanId 任务计划ID
     * @return 影响的行数
     */
    @Update("UPDATE scheduler_task_plan SET interested_driver_count = interested_driver_count + 1 WHERE id = #{taskPlanId}")
    int updateInterestedDriverCount(@Param("taskPlanId") Long taskPlanId);

    /**
     * 根据状态查询 匹配司机
     * @param outboundStatus
     * @return
     */
    List<TaskDriverMatchDO> selectListByOutboundStatus(Integer outboundStatus);

    /**
     * 根据电话号码列表和时间范围查询已呼叫的司机
     *
     * @param phones 电话号码列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 已呼叫的司机列表
     */
    List<TaskDriverMatchDO> selectCalledDriversByPhones(@Param("phones") List<String> phones,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);
}