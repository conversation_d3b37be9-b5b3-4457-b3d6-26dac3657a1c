package com.zly.project.match.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 调度呼叫记录对象 scheduler_dispatch_call
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DispatchCallDO extends BaseEntity {

    /** 呼叫记录ID */
    private Long id;

    /** 调度任务匹配ID */
    @Excel(name = "调度任务匹配ID")
    private Long taskMatchId;

    /** 外呼系统任务ID */
    @Excel(name = "外呼系统任务ID")
    private String externalTaskId;

    /** 呼叫状态（1.待呼叫、2.呼叫中 3.已呼叫、4.呼叫失败、5.号码重复 呼叫超时） */
    @Excel(name = "呼叫状态")
    private String callStatus;

    /** 有无意向（0无 1有 2可能有意向） */
    @Excel(name = "有无意向")
    private Integer hasIntention;

    /** 通话记录文本内容 */
    private String callContent;

    /** 通话记录语音地址 */
    private String voiceUrl;

    /** 呼叫时间 */
    @Excel(name = "呼叫时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;

    /** 唯一标识 */
    private String extra;

}