package com.zly.project.match.mapper;

import java.util.List;
import java.util.Map;

import com.zly.project.match.domain.DispatchCallDO;

/**
 * 调度呼叫记录Mapper接口
 * 
 * <AUTHOR>
 */
public interface DispatchCallMapper {
    /**
     * 查询调度呼叫记录
     * 
     * @param id 调度呼叫记录主键
     * @return 调度呼叫记录
     */
     DispatchCallDO selectDispatchCallById(Long id);

    /**
     * 查询调度呼叫记录列表
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 调度呼叫记录集合
     */
     List<DispatchCallDO> selectDispatchCallList(DispatchCallDO dispatchCall);

    /**
     * 新增调度呼叫记录
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 结果
     */
     int insertDispatchCall(DispatchCallDO dispatchCall);

    /**
     * 修改调度呼叫记录
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 结果
     */
     int updateDispatchCall(DispatchCallDO dispatchCall);

    /**
     * 删除调度呼叫记录
     * 
     * @param id 调度呼叫记录主键
     * @return 结果
     */
     int deleteDispatchCallById(Long id);

    /**
     * 批量删除调度呼叫记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteDispatchCallByIds(Long[] ids);

    /**
     * 根据匹配司机ID查询呼叫记录集合
     * @param driverMatchIds
     * @return
     */
    List<DispatchCallDO> selectByDriverMatchIds(List<Long> driverMatchIds);

    /**
     * 根据唯一字段查询 外呼数据
     * @param extra
     * @return
     */
    DispatchCallDO getDispatchCallByExtra(String extra);

    /**
     * 根据matchId查询最新的通话记录
     * @param matchId
     * @return
     */
    DispatchCallDO getLatestCallByMatchId(Long matchId);
}