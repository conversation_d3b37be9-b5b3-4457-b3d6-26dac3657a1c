package com.zly.project.match.controller.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 调度任务匹配司机更新 Request VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskDriverMatchUpdateReqVO extends TaskDriverMatchBaseVO {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Long id;
}