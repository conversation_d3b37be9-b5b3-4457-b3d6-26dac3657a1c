package com.zly.project.match.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.zly.project.match.mapper.DispatchCallMapper;
import com.zly.project.match.domain.DispatchCallDO;
import com.zly.project.match.service.IDispatchCallService;

import javax.annotation.Resource;

/**
 * 调度呼叫记录Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class DispatchCallServiceImpl implements IDispatchCallService {
    
    @Resource
    private DispatchCallMapper dispatchCallMapper;

    /**
     * 查询调度呼叫记录
     * 
     * @param id 调度呼叫记录主键
     * @return 调度呼叫记录
     */
    @Override
    public DispatchCallDO selectDispatchCallById(Long id) {
        return dispatchCallMapper.selectDispatchCallById(id);
    }

    /**
     * 查询调度呼叫记录列表
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 调度呼叫记录
     */
    @Override
    public List<DispatchCallDO> selectDispatchCallList(DispatchCallDO dispatchCall) {
        return dispatchCallMapper.selectDispatchCallList(dispatchCall);
    }

    /**
     * 新增调度呼叫记录
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 结果
     */
    @Override
    public int insertDispatchCall(DispatchCallDO dispatchCall) {
        return dispatchCallMapper.insertDispatchCall(dispatchCall);
    }

    /**
     * 修改调度呼叫记录
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 结果
     */
    @Override
    public int updateDispatchCall(DispatchCallDO dispatchCall) {
        return dispatchCallMapper.updateDispatchCall(dispatchCall);
    }

    /**
     * 批量删除调度呼叫记录
     * 
     * @param ids 需要删除的调度呼叫记录主键
     * @return 结果
     */
    @Override
    public int deleteDispatchCallByIds(Long[] ids) {
        return dispatchCallMapper.deleteDispatchCallByIds(ids);
    }

    /**
     * 删除调度呼叫记录信息
     * 
     * @param id 调度呼叫记录主键
     * @return 结果
     */
    @Override
    public int deleteDispatchCallById(Long id) {
        return dispatchCallMapper.deleteDispatchCallById(id);
    }

    @Override
    public Map<Long, DispatchCallDO> getDispatchCallDOMap(List<Long> driverMatchIds) {
        // 获取所有通话记录
        List<DispatchCallDO> callRecords = dispatchCallMapper.selectByDriverMatchIds(driverMatchIds);
        // 使用Map来存储最新的通话记录
        Map<Long, DispatchCallDO> latestCallRecordMap = new HashMap<>();
        for (DispatchCallDO callRecord : callRecords) {
            Long driverMatchId = callRecord.getTaskMatchId();
            DispatchCallDO existingRecord = latestCallRecordMap.get(driverMatchId);
            // 如果当前记录的呼叫时间比已有记录的呼叫时间更新，则替换
            if (existingRecord == null || callRecord.getCallTime().after(existingRecord.getCallTime())) {
                latestCallRecordMap.put(driverMatchId, callRecord);
            }
        }
        log.info("getDispatchCallDOMap,latestCallRecordMap:{}",latestCallRecordMap);
        return latestCallRecordMap;
    }

    @Override
    public DispatchCallDO getDispatchCallByExtra(String extra) {
        log.info("getDispatchCallByExtra,extra:{}",extra);
        return dispatchCallMapper.getDispatchCallByExtra(extra);
    }

    @Override
    public DispatchCallDO getLatestCallByMatchId(Long matchId) {
        log.info("getLatestCallByMatchId,matchId:{}",matchId);
        return dispatchCallMapper.getLatestCallByMatchId(matchId);
    }
}