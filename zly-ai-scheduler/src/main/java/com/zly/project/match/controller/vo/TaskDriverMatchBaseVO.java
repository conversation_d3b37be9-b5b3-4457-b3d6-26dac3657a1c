package com.zly.project.match.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 调度任务匹配司机 Base VO，提供给添加、修改、详情查询等接口使用
 *
 * <AUTHOR>
 */
@Data
public class TaskDriverMatchBaseVO {

    /**
     * 调度任务ID
     */
    @NotNull(message = "调度任务ID不能为空")
    private Long taskPlanId;

    /**
     * 外呼系统任务ID
     */
    private String outboundTaskId;

    /**
     * 司机画像ID
     */
    @NotNull(message = "司机画像ID不能为空")
    private Long driverProfileId;

    /**
     * 司机姓名
     */
    @Length(max = 50, message = "司机姓名长度不能超过50个字符")
    private String driverName;

    /**
     * 身份证号
     */
    @Length(max = 30, message = "身份证号长度不能超过30个字符")
    private String identityCard;

    /**
     * 联系电话
     */
    @Length(max = 20, message = "联系电话长度不能超过20个字符")
    private String telephone;

    /**
     * 匹配度(百分比)
     */
    private Integer matchScore;

    /**
     * 成交状态(0:未成交，1:已成交，2:已取消)
     */
    private Integer dealStatus;

    /**
     * 成交运费(元)
     */
    private BigDecimal freightCost;
    
    /**
     * AI分析原因
     */
    @Length(max = 1024, message = "AI分析原因长度不能超过1024个字符")
    private String aiAnalysis;

    /**
     * 备注
     */
    @Length(max = 512, message = "备注长度不能超过512个字符")
    private String remark;

    /**
     * 匹配司机是否外呼
     * 1.待呼叫、2.呼叫中、3.已呼叫、4.未接通 5 停机 6二次呼叫
     */
    private Integer isOutbound;

    /**
     * 有无意向
     */
    //有无意向 0:无意向 1:有意向
    private Integer hasIntention;

    /**
     * 是否发送短信 0 未发 1 已发
     */
    private Integer isSendSms;

    /**
     * 短信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date smsSendTime;

    /**
     * 是否发送确认短信
     */
    private Integer isConfirmSms;

    /**
     * 确认短信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmSmsTime;

    /**
     * 成交单位
     */
    private Integer unitCount;
}