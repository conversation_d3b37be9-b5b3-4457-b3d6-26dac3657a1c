package com.zly.project.match.service;

import java.util.List;
import java.util.Map;

import com.zly.project.match.domain.DispatchCallDO;

/**
 * 调度呼叫记录Service接口
 * 
 * <AUTHOR>
 */
public interface IDispatchCallService {
    /**
     * 查询调度呼叫记录
     * 
     * @param id 调度呼叫记录主键
     * @return 调度呼叫记录
     */
     DispatchCallDO selectDispatchCallById(Long id);

    /**
     * 查询调度呼叫记录列表
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 调度呼叫记录集合
     */
     List<DispatchCallDO> selectDispatchCallList(DispatchCallDO dispatchCall);

    /**
     * 新增调度呼叫记录
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 结果
     */
     int insertDispatchCall(DispatchCallDO dispatchCall);

    /**
     * 修改调度呼叫记录
     * 
     * @param dispatchCall 调度呼叫记录
     * @return 结果
     */
     int updateDispatchCall(DispatchCallDO dispatchCall);

    /**
     * 批量删除调度呼叫记录
     * 
     * @param ids 需要删除的调度呼叫记录主键集合
     * @return 结果
     */
     int deleteDispatchCallByIds(Long[] ids);

    /**
     * 删除调度呼叫记录信息
     * 
     * @param id 调度呼叫记录主键
     * @return 结果
     */
     int deleteDispatchCallById(Long id);

    /**
     * 根据匹配司机id 查询最近的呼叫记录
     * @param longList
     * @return
     */
    Map<Long, DispatchCallDO> getDispatchCallDOMap(List<Long> longList);

    /**
     * 根据唯一标识查询 呼叫记录
     * @param extra
     * @return
     */
    DispatchCallDO getDispatchCallByExtra(String extra);

    /**
     * 查询最新一条的通话记录
     * @param id
     * @return
     */
    DispatchCallDO getLatestCallByMatchId(Long id);
}