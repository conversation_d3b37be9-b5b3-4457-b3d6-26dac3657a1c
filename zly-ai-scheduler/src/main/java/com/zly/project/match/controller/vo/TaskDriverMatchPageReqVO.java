package com.zly.project.match.controller.vo;

import com.zly.framework.web.page.PageDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 调度任务匹配司机分页查询 Request VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TaskDriverMatchPageReqVO extends PageDomain {

    /**
     * 调度任务ID
     */
    private Long taskPlanId;

    /**
     * 司机姓名
     */
    @Length(max = 50, message = "司机姓名长度不能超过50个字符")
    private String driverName;

    /**
     * 联系电话
     */
    @Length(max = 20, message = "联系电话长度不能超过20个字符")
    private String telephone;

    /**
     * 有无意向
     */
    //有无意向 0:无意向 1:有意向
    private Integer hasIntention;

    /**
     * 成交状态(0:未成交，1:已成交，2:已取消)
     */
    private Integer dealStatus;

    /**
     * 状态
     */
    private Integer isOutbound;
}