package com.zly.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车牌颜色枚举
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Getter
@AllArgsConstructor
public enum PlateColorEnum {
    
    /**
     * 蓝色
     */
    BLUE("1", "蓝色"),
    
    /**
     * 黄色
     */
    YELLOW("2", "黄色"),
    
    /**
     * 黑色
     */
    BLACK("3", "黑色"),
    
    /**
     * 白色
     */
    WHITE("4", "白色"),
    
    /**
     * 绿色
     */
    GREEN("5", "绿色"),
    
    /**
     * 其他
     */
    OTHER("91", "其他"),
    
    /**
     * 农绿色
     */
    FARM_GREEN("92", "农绿色"),
    
    /**
     * 黄绿色
     */
    YELLOW_GREEN("93", "黄绿色"),
    
    /**
     * 渐变绿
     */
    GRADIENT_GREEN("94", "渐变绿");
    
    /**
     * 编码
     */
    private final String code;
    
    /**
     * 描述
     */
    private final String desc;
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static PlateColorEnum getByCode(String code) {
        for (PlateColorEnum plateColor : values()) {
            if (plateColor.getCode().equals(code)) {
                return plateColor;
            }
        }
        return null;
    }
    
    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        PlateColorEnum plateColor = getByCode(code);
        return plateColor == null ? "" : plateColor.getDesc();
    }
    
    /**
     * 判断编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
} 