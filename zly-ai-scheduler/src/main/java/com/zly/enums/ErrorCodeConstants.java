package com.zly.enums;

import com.zly.common.exception.ErrorCode;

/**
 * Scheduler 错误码枚举类
 *
 * scheduler 系统，使用 1-009-000-000 段
 */
public interface ErrorCodeConstants {
    ErrorCode BASE_SERVER_ERROR = new ErrorCode(1_008_002_000, "业务异常");

    // ========== 任务调度计划相关 1-009-001-000 ==========
    ErrorCode TASK_PLAN_NOT_EXISTS = new ErrorCode(1_009_001_000, "任务调度计划不存在");
    ErrorCode TASK_PLAN_STATUS_INVALID = new ErrorCode(1_009_001_001, "任务调度计划状态无效");
    ErrorCode TASK_PLAN_ALREADY_ASSIGNED = new ErrorCode(1_009_001_002, "任务调度计划已分配司机");

    // ========== 物流文本解析相关 1-009-002-000 ==========
    ErrorCode LOGISTICS_PARSE_ERROR = new ErrorCode(1_009_002_000, "物流文本解析失败");

    // ========== 司机画像解析相关 1-009-002-000 ==========
    ErrorCode ALL_DRIVER_PROFILES_ERROR = new ErrorCode(1_009_003_000, "同步司机画像数据异常");

    ErrorCode EXPORT_DRIVER_PROFILES_ERROR = new ErrorCode(1002006004, "导出司机画像数据失败");
}