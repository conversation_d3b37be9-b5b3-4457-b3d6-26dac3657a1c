package com.zly.enums.plan;

import com.zly.common.core.text.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务调度计划状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskPlanStatusEnum implements ArrayValuable<Integer> {

    //UNSCHEDULED(1, "未调度"),
    SCHEDULING(2, "调度中"),
    COMPLETED(3, "已完成"),
    //STOPPED(4, "已停止"),
    CANCELLED(5, "已作废");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(TaskPlanStatusEnum::getStatus).toArray(Integer[]::new);

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}