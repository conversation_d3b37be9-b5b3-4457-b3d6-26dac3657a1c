package com.zly.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 承运状态枚举
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Getter
@AllArgsConstructor
public enum CarrierStatusEnum {
    
    /**
     * 待接单
     */
    PENDING_ACCEPT("0", "待接单"),
    
    /**
     * 待装车
     */
    PENDING_LOADING("1", "待装车"),
    
    /**
     * 运输中
     */
    IN_TRANSIT("2", "运输中"),
    
    /**
     * 已送达
     */
    DELIVERED("3", "已送达");
    
    /**
     * 编码
     */
    private final String code;
    
    /**
     * 描述
     */
    private final String desc;
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static CarrierStatusEnum getByCode(String code) {
        for (CarrierStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        CarrierStatusEnum status = getByCode(code);
        return status == null ? "" : status.getDesc();
    }
    
    /**
     * 判断编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}