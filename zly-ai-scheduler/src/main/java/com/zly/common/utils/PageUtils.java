package com.zly.common.utils;

import com.github.pagehelper.PageHelper;
import com.zly.common.utils.sql.SqlUtil;
import com.zly.framework.web.domain.BaseEntity;
import com.zly.framework.web.page.PageDomain;
import com.zly.framework.web.page.TableSupport;

import java.util.HashMap;

/**
 * 分页工具类
 * 
 * <AUTHOR>
 */
public class PageUtils extends PageHelper {

    /**
     * 设置请求分页数据
     */
    public static void startPage(String orderBy) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        Boolean reasonable = pageDomain.getReasonable();
        PageHelper.startPage(pageNum, pageSize, SqlUtil.escapeOrderBySql(orderBy)).setReasonable(reasonable);
    }

    /**
     * 设置请求分页数据
     */
    public static void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        PageHelper.startPage(pageNum, pageSize);
    }

    public static void startPageByPageDomain(PageDomain pageDomain) {
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        PageHelper.startPage(pageNum, pageSize);
    }

    /**
     * 设置请求分页数据
     */
    public static void startPage(boolean count) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        PageHelper.startPage(pageNum, pageSize, count);
    }

    /**
     * 设置请求分页数据，不需要count
     */
    public static void startPageAndNotCount() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        PageHelper.startPage(pageNum, pageSize, false);
    }

    /**
     * 设置请求分页数据
     *
     * @return
     */
    public static PageDomain buildPageRequest() {
        return TableSupport.buildPageRequest();
    }

    public static void getPageParam(BaseEntity base) {
        HashMap<String, Object> pages = new HashMap<>();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        pages.put("offSet",(pageDomain.getPageNum()-1) * pageDomain.getPageSize());
        pages.put("pageSize",pageDomain.getPageSize());
        base.setParams(pages);
    }

    public static Integer getPageOffSet() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        return (pageDomain.getPageNum() - 1) * pageDomain.getPageSize();
    }

    public static Integer getPageSize() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        return pageDomain.getPageSize();
    }

    /**
     * 清理分页的线程变量
     */
    public static void clearPage()
    {
        PageHelper.clearPage();
    }
}
