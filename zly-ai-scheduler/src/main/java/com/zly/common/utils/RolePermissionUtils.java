package com.zly.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zly.project.system.domain.SysRole;
import com.zly.project.system.domain.vo.SysRoleReqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色权限工具类
 * 
 * <AUTHOR>
 */
public class RolePermissionUtils
{
    private static final Logger logger = LoggerFactory.getLogger(RolePermissionUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();


    /**
     * 将权限集合转换为 JSON 字符串
     *
     * @param permissions 权限集合
     * @return JSON 字符串
     */
    public static String permissionsToJson(List<String> permissions)
    {
        if (permissions == null || permissions.isEmpty())
        {
            return null;
        }
        try
        {
            return objectMapper.writeValueAsString(permissions);
        }
        catch (JsonProcessingException e)
        {
            logger.error("权限集合转换为JSON失败", e);
            return null;
        }
    }

    /**
     * 将 JSON 字符串转换为权限集合
     * 
     * @param permissionsJson JSON 字符串
     * @return 权限集合
     */
    public static List<String> jsonToPermissions(String permissionsJson)
    {
        if (StringUtils.isEmpty(permissionsJson))
        {
            return new ArrayList<>();
        }
        
        try
        {
            return objectMapper.readValue(permissionsJson, new TypeReference<List<String>>() {});
        }
        catch (JsonProcessingException e)
        {
            logger.error("JSON转换为权限集合失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 合并权限集合
     * 
     * @param permissions1 权限集合1
     * @param permissions2 权限集合2
     * @return 合并后的权限集合
     */
    public static List<String> mergePermissions(List<String> permissions1, List<String> permissions2)
    {
        List<String> merged = new ArrayList<>();
        
        if (permissions1 != null)
        {
            merged.addAll(permissions1);
        }
        if (permissions2 != null)
        {
            for (String permission : permissions2)
            {
                if (!merged.contains(permission))
                {
                    merged.add(permission);
                }
            }
        }
        
        return merged;
    }
}
