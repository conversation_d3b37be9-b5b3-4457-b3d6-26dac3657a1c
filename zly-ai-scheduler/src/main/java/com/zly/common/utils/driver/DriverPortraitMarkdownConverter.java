package com.zly.common.utils.driver;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zly.project.driver.controller.vo.DriverProfileRespVO;
import com.zly.project.driver.controller.vo.VehicleGpsInfoVO;
import com.zly.project.driver.controller.vo.VehicleInfoVO;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DriverPortraitMarkdownConverter {
	public static final String OUTPUT_FILE = "/Users/<USER>/Downloads/driver_profiles.md";

	/**
	 * 将司机画像数据转换为Markdown格式并保存到文件
	 */
	public static void convertToMarkdownFile(List<DriverProfileRespVO> portraits) {
		String content = convertToMarkdown(portraits);
		writeToFile(content);
	}

	/**
	 * 将内容写入文件
	 */
	private static void writeToFile(String content) {
		try {
			Path filePath = Paths.get(OUTPUT_FILE);
			Files.write(filePath, content.getBytes(StandardCharsets.UTF_8));
		} catch (IOException e) {
			throw new RuntimeException("写入Markdown文件失败: " + e.getMessage(), e);
		}
	}

	/**
	 * 将司机画像数据转换为Markdown格式
	 */
	public static String convertToMarkdown(List<DriverProfileRespVO> portraits) {
		if (portraits == null || portraits.isEmpty()) {
			return "暂无司机数据";
		}
		StringBuilder markdown = new StringBuilder();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		// 添加文档开头说明
		markdown.append("# 物流司机画像数据库\n\n");
		markdown.append("本文档包含物流系统中司机的详细画像数据，用于智能调度系统的匹配和推荐。\n\n");
		// 添加生成时间
		markdown.append("生成时间: ").append(sdf.format(new Date())).append("\n\n");
		markdown.append("!").append("\n\n"); // 分隔线
		// 添加司机数量
		// markdown.append("司机总数: ").append(portraits.size()).append("\n\n");
		// for (DriverProfileRespVO driver : portraits) {
		// markdown.append("## ").append(driver.getDriverName()).append(" - ").append(driver.getDriverId()).append("\n\n");
		// }
		for (DriverProfileRespVO driver : portraits) {
			markdown.append("### 司机数据开头").append("\n");
			// 司机基本信息
			markdown.append("基本信息\n");
			markdown.append("**画像ID**: ").append(driver.getId()).append("\n");
			markdown.append("**司机ID**: ").append(driver.getDriverId()).append("\n");
			markdown.append("**姓名**: ").append(driver.getDriverName()).append("\n");
			markdown.append("**手机号**: ").append(StringUtils.defaultString(driver.getSchedulerTelephone(), "未填写")).append("\n");
			markdown.append("**身份证**: ").append(StringUtils.defaultString(driver.getIdentityCard(), "未填写")).append("\n");
			markdown.append("**司机来源**: ").append(getOperationDesc(driver.getOperation())).append("\n");
			String wlhyGoods = driver.getWlhyGoods();
			String schedulerGoods = driver.getSchedulerGoods();
			String excludedGoods = driver.getExcludedGoods();
			String mergedGoods = mergeAndProcessGoods(wlhyGoods, schedulerGoods, excludedGoods);
			markdown.append("**常运货物**: ").append(StringUtils.defaultString(mergedGoods, "未填写")).append("\n");
			// markdown.append("**驾龄**: ").append(driver.getDrivingYears() > 0 ? driver.getDrivingYears() + "年" : "未知").append("\n");
			// markdown.append("**资料完善度**: ").append(driver.getProfileCompleteness()).append("%\n");
			// markdown.append("**服务次数**: ").append(driver.getServiceCount()).append("\n");
			// markdown.append("**总里程**: ").append(driver.getTotalCourse()).append("km\n");
			// markdown.append("**驾驶证状态**: ").append(getLicenseValidDesc(driver.getIsTheDrivrLicenseValid())).append("\n");
			// markdown.append("**资格证状态**: ").append(getLicenseValidDesc(driver.getIsTheQualificationValid())).append("\n");
			// markdown.append("**最近服务时间**: ").append(driver.getLastServiceTime() != null ? sdf.format(driver.getLastServiceTime()) : "暂无").append("\n");
			// markdown.append("**司机状态**: ").append(getDriverStateDesc(driver.getState())).append("\n");
			// 证件信息
			// markdown.append("### 证件信息\n\n");
			// markdown.append("- **驾驶证类型**: ").append(StringUtils.defaultString(driver.getVehicleClass(), "未填写")).append("\n");
			// markdown.append("- **驾驶证有效期**: ").append(StringUtils.defaultString(driver.getValidPeriodTo(), "未填写")).append("\n");
			// markdown.append("- **从业资格证**: ").append(StringUtils.defaultString(driver.getQualificationCertificate(), "未填写")).append("\n");
			// markdown.append("- **资格证有效期**: ").append(StringUtils.defaultString(driver.getActualCarrierEnd(), "未填写")).append("\n\n");
			// String vehicles = driver.getVehicles();
			// List<VehicleInfoVO> vehicleInfoVOS = convertToVehicleInfoList(vehicles);
			String wlhyVehicleLength = driver.getWlhyVehicleLength();
			String schedulerVehicleLength = driver.getSchedulerVehicleLength();
			String excludedVehicleLength = driver.getExcludedVehicleLength();
			String mergeAndProcessVehicleLengths = mergeAndProcessVehicleLengths(wlhyVehicleLength, schedulerVehicleLength, excludedVehicleLength);
			String wlhyVehicleStructure = driver.getWlhyVehicleStructure();
			String schedulerVehicleStructure = driver.getSchedulerVehicleStructure();
			String excludedVehicleStructure = driver.getExcludedVehicleStructure();
			String mergeAndProcessVehicleStructure = mergeAndProcessVehicleStructure(wlhyVehicleStructure, schedulerVehicleStructure, excludedVehicleStructure);
			if (StringUtils.isNotBlank(mergeAndProcessVehicleLengths) || StringUtils.isNotBlank(mergeAndProcessVehicleStructure)) {
				markdown.append("车辆信息\n");
				markdown.append("**车辆长度**: ").append(StringUtils.defaultString(mergeAndProcessVehicleLengths, "未填写")).append("\n");
				markdown.append("**车辆结构**: ").append(StringUtils.defaultString(mergeAndProcessVehicleStructure, "未填写")).append("\n");
			}
			// 车辆信息
			// if (!vehicleInfoVOS.isEmpty() ) {
			// for (VehicleInfoVO vehicle : vehicleInfoVOS) {
			// markdown.append("**车辆类型**: ").append(vehicle.getVehicleTypeName()).append("\n");
			// markdown.append("**车辆尺寸**: ").append(vehicle.getVehicleLength()).append("×")
			// .append(vehicle.getVehicleWidth()).append("×")
			// .append(vehicle.getVehicleHeight()).append(" mm\n");
			// markdown.append("**载重**: ").append(vehicle.getVehicleTonnage() != null ?
			// vehicle.getVehicleTonnage() + "吨" : "未知").append("\n");
			// markdown.append("**总质量**: ").append(vehicle.getGrossMass() != null ?
			// vehicle.getGrossMass() + "吨" : "未知").append("\n");
			// markdown.append("- **能源类型**: ").append(getEnergyTypeDesc(vehicle.getVehicleEnergyType()));
			// markdown.append("- **使用性质**: ").append(StringUtils.defaultString(vehicle.getUseCharacter(), "未知")).append("\n");
			// markdown.append("- **GPS设备**: ").append(getGpsStatusDesc(vehicle.getIsGps())).append("\n");
			// markdown.append("- **资料完善**: ").append(vehicle.getInfoIsComplete() != null && vehicle.getInfoIsComplete() == 0 ? "是" : "否").append("\n");
			// markdown.append("- **服务次数**: ").append(vehicle.getServiceCount()).append("\n");
			// markdown.append("- **总里程**: ").append(vehicle.getTotalCourse()).append(" km\n");
			// markdown.append("- **最近服务**: ").append(vehicle.getLastServiceTime() != null ? sdf.format(vehicle.getLastServiceTime()) : "暂无").append("\n");
			// markdown.append("- **车辆状态**: ").append(getVehicleStateDesc(vehicle.getState())).append("\n");
			// GPS信息
			// VehicleGpsInfoVO gpsInfo = vehicle.getGpsInfo();
			// if (gpsInfo != null) {
			// markdown.append("**当前位置**: ").append(gpsInfo.getAddress()).append("\n");
			// markdown.append("**经纬度**: ").append("[").append(gpsInfo.getLongitude()).append(", ")
			// .append(gpsInfo.getLatitude()).append("]").append("\n");
			// markdown.append("**行政区域**: ")
			// .append(StringUtils.defaultString(gpsInfo.getProvince(), ""))
			// .append(StringUtils.defaultString(gpsInfo.getCity(), ""))
			// .append(StringUtils.defaultString(gpsInfo.getCountry(), ""))
			// .append("\n");
			// markdown.append("**GPS更新时间**: ").append(sdf.format(gpsInfo.getUpdateTime())).append("\n");
			// } else {
			// markdown.append("**GPS信息**: 暂无\n");
			// }
			// // 根据司机车辆数据生成车辆适应性评价
			// String vehicleAdaptability = generateVehicleAdaptability(vehicle);
			// markdown.append("**车辆适应性**: ").append(vehicleAdaptability).append("\n\n");
			// markdown.append("\n");
			// }
			// }
			String wlhyHaulway = driver.getWlhyHaulway();
			String schedulerHaulway = driver.getSchedulerHaulway();
			String excludedHaulway = driver.getExcludedHaulway();
			String mergedHaulway = mergeHaulway(wlhyHaulway, schedulerHaulway, excludedHaulway);
			if (StringUtils.isNotBlank(mergedHaulway)) {
				markdown.append("常跑路线\n");
				Map<String, Map<String, Object>> stringMapHashMap = new HashMap<>(8);
				if (driver.getLongDistanceRoutes() != null && !driver.getLongDistanceRoutes().isEmpty()) {
					stringMapHashMap = new HashMap<>();
					List<Map<String, Object>> maps = convertToRoutesList(driver.getLongDistanceRoutes());
					for (Map<String, Object> route : maps) {
						String routeName = (String) route.get("routeName");
						stringMapHashMap.put(routeName, route);
					}
				}
				for (String routeName : mergedHaulway.split("、")) {
					markdown.append("**").append(routeName).append("**\n");
					Map<String, Object> stringObjectMap = stringMapHashMap.get(routeName);
					if (MapUtil.isNotEmpty(stringObjectMap)) {
						markdown.append("  运单数量: ").append(stringObjectMap.get("routeCount")).append("\n");
						markdown.append("  平均里程: ").append(stringObjectMap.get("averageMileage")).append("km\n");
						markdown.append("  路线总里程: ").append(stringObjectMap.get("totalMileage")).append("km\n");
						markdown.append("  平均运费: ").append(stringObjectMap.get("averageFare")).append("元\n");
						markdown.append("  最近订单: ").append(sdf.format(stringObjectMap.get("lastOrderTime"))).append("\n");
					} else {
						markdown.append("  运单数量: ").append(0).append("\n");
					}
				}
			}
			// 添加历史表现分析部分
			// markdown.append("### 历史表现分析\n\n");
			// // 根据司机数据生成经验评价
			// String experienceEvaluation = generateExperienceEvaluation(driver);
			// markdown.append("**经验评价**: ").append(experienceEvaluation).append("\n");
			// 根据司机数据生成服务质量评价
			// String serviceQuality = generateServiceQuality(driver);
			// markdown.append("**服务质量**: ").append(serviceQuality).append("\n");
			markdown.append("\n").append("### 司机数据结尾").append("\n\n");
			markdown.append("!").append("\n\n"); // 分隔线
		}
		// 添加文档结尾说明
		markdown.append("# 数据说明\n");
		markdown.append("本文档中的司机画像数据用于物流调度系统的智能匹配算法训练和测试。\n");
		markdown.append("数据包括司机基本信息、车辆信息、常跑路线等多个维度。\n");
		markdown.append("通过这些数据，系统可以更准确地为特定运输任务推荐合适的司机，提高调度效率和客户满意度。\n");
		return markdown.toString();
	}

	/**
	 * 合并并处理常跑路线 将wlhy和scheduler的商品数据合并，排除excluded中的数据
	 */
	public static String mergeHaulway(String wlhyHaulway, String schedulerHaulway, String excludedHaulway) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			// 解析JSON数组
			return getLastData(wlhyHaulway, schedulerHaulway, excludedHaulway, objectMapper);
		} catch (Exception e) {
			log.error("处理常跑路线时发生错误", e);
			return null;
		}
	}

	/**
	 * 合并并处理商品数据 将wlhy和scheduler的商品数据合并，排除excluded中的数据
	 */
	public static String mergeAndProcessGoods(String wlhyGoods, String schedulerGoods, String excludedGoods) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			// 解析JSON数组
			return getLastData(wlhyGoods, schedulerGoods, excludedGoods, objectMapper);
		} catch (Exception e) {
			log.error("处理商品数据时发生错误", e);
			return null;
		}
	}

	/**
	 * 合并并处理车辆结构数据 将wlhy和scheduler的车辆结构数据合并，排除excluded中的数据
	 */
	public static String mergeAndProcessVehicleStructure(String wlhyVehicleStructure, String schedulerVehicleStructure, String excludedVehicleStructure) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			// 解析JSON数组
			return getLastData(wlhyVehicleStructure, schedulerVehicleStructure, excludedVehicleStructure, objectMapper);
			// 转换回JSON数组字符串
		} catch (Exception e) {
			log.error("处理车辆结构数据时发生错误", e);
			return null;
		}
	}

	@Nullable
	private static String getLastData(String wlhyData, String schedulerData, String excludedData, ObjectMapper objectMapper) throws JsonProcessingException {
		Set<String> mergedSet = getMergedLengths(wlhyData, schedulerData, objectMapper);
		excludedHandle(excludedData, objectMapper, mergedSet);
		// 转换为列表并排序（按字母顺序）
		List<String> sortedList = new ArrayList<>(mergedSet);
		Collections.sort(sortedList);
		if (sortedList.isEmpty()) {
			return null;
		} else {
			return String.join("、", sortedList);
		}
	}

	/**
	 * 合并并处理车辆长度数据
	 */
//	public static String mergeAndProcessVehicleLengths(String wlhyVehicleLength, String schedulerVehicleLength, String excludedVehicleLength) {
//		try {
//			ObjectMapper objectMapper = new ObjectMapper();
//			// 解析JSON数组
//			Set<String> mergedLengths = getMergedLengths(wlhyVehicleLength, schedulerVehicleLength, objectMapper);
//			excludedHandle(excludedVehicleLength, objectMapper, mergedLengths);
//			// 处理数值，将"13000"转换为"13"等
//			List<String> processedLengthsStr = new ArrayList<>();
//			for (String length : mergedLengths) {
//				try {
//					// 尝试解析为数字
//					double numericValue = Double.parseDouble(length);
//					// 如果是以米为单位的数值(如13000毫米 = 13米)
//					if (numericValue >= 1000) {
//						// 将毫米转换为米，保留两位小数
//						double meters = numericValue / 1000.0;
//						// 只有当不为0时才添加
//						if (meters > 0) {
//							// 格式化为保留两位小数的字符串
//							String formattedLength = String.format("%.2f", meters).replaceAll("\\.00$", "");
//							processedLengthsStr.add(formattedLength + "米");
//						}
//					} else if (numericValue > 0) {
//						// 如果已经是以米为单位，且大于0，保留两位小数
//						String formattedLength = String.format("%.2f", numericValue).replaceAll("\\.00$", "");
//						processedLengthsStr.add(formattedLength + "米");
//					}
//				} catch (NumberFormatException e) {
//					// 如果无法解析为数字，忽略该值
//					log.debug("无法解析车辆长度值: {}", length);
//				}
//			}
//			// 排序处理后的长度值（按数值大小排序）
//			processedLengthsStr.sort((a, b) -> {
//				try {
//					double aValue = Double.parseDouble(a.substring(0, a.length() - 1));
//					double bValue = Double.parseDouble(b.substring(0, b.length() - 1));
//					return Double.compare(aValue, bValue);
//				} catch (Exception e) {
//					return a.compareTo(b);
//				}
//			});
//			if (processedLengthsStr.isEmpty()) {
//				return null;
//			} else {
//				return String.join("、", processedLengthsStr);
//			}
//		} catch (Exception e) {
//			log.error("处理车辆长度数据时发生错误", e);
//			return null;
//		}
//	}

	/**
	 * 合并并处理车辆长度数据
	 */
	public static String mergeAndProcessVehicleLengths(String wlhyVehicleLength, String schedulerVehicleLength, String excludedVehicleLength) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			// 解析JSON数组
			Set<String> mergedLengths = getMergedLengths(wlhyVehicleLength, schedulerVehicleLength, objectMapper);
			excludedHandle(excludedVehicleLength, objectMapper, mergedLengths);
			// 处理数值，将"13000"转换为"13"等
			// 使用Map来存储处理后的值，避免重复
			Map<String, Double> processedLengthsMap = new HashMap<>();
			for (String length : mergedLengths) {
				try {
					// 尝试解析为数字
					double numericValue = Double.parseDouble(length);
					// 如果是以米为单位的数值(如13000毫米 = 13米)
					if (numericValue >= 1000) {
						// 将毫米转换为米，保留三位小数
						double meters = numericValue / 1000.0;
						// 只有当不为0时才添加
						if (meters > 0) {
							// 格式化为保留三位小数的字符串，并移除末尾的.000
							String formattedLength = String.format("%.3f", meters).replaceAll("\\.000$", "");
							// 如果末尾是.00，也移除
							formattedLength = formattedLength.replaceAll("\\.00$", "");
							// 如果末尾是.0，也移除
							formattedLength = formattedLength.replaceAll("\\.0$", "");
							// 使用格式化后的值作为键，原始值作为值
							processedLengthsMap.put(formattedLength, meters);
						}
					} else if (numericValue > 0) {
						// 如果已经是以米为单位，且大于0，保留三位小数
						String formattedLength = String.format("%.3f", numericValue).replaceAll("\\.000$", "");
						// 如果末尾是.00，也移除
						formattedLength = formattedLength.replaceAll("\\.00$", "");
						// 如果末尾是.0，也移除
						formattedLength = formattedLength.replaceAll("\\.0$", "");
						processedLengthsMap.put(formattedLength, numericValue);
					}
				} catch (NumberFormatException e) {
					// 如果无法解析为数字，忽略该值
					log.debug("无法解析车辆长度值: {}", length);
				}
			}
			// 将Map转换为列表
			List<String> processedLengthsStr = new ArrayList<>();
			for (Map.Entry<String, Double> entry : processedLengthsMap.entrySet()) {
				processedLengthsStr.add(entry.getKey() + "米");
			}
			// 排序处理后的长度值（按数值大小排序）
			processedLengthsStr.sort((a, b) -> {
				try {
					double aValue = Double.parseDouble(a.substring(0, a.length() - 1));
					double bValue = Double.parseDouble(b.substring(0, b.length() - 1));
					return Double.compare(aValue, bValue);
				} catch (Exception e) {
					return a.compareTo(b);
				}
			});
			if (processedLengthsStr.isEmpty()) {
				return null;
			} else {
				return String.join("、", processedLengthsStr);
			}
		} catch (Exception e) {
			log.error("处理车辆长度数据时发生错误", e);
			return null;
		}
	}

	public static void main(String[] args) {
		String s = mergeAndProcessVehicleLengths(null, "[\"13000\", \"5000\", \"5995\", \"6000\", \"7000\", \"7080\"]", null);
		System.out.println(s);
	}

	/**
	 * 合并并处理车辆长度数据 将wlhy和scheduler的车辆结构数据合并，排除excluded中的数据
	 */
	public static String mergeAndProcessVehicleLengthNoChange(String wlhyVehicleLength, String schedulerVehicleLength, String excludedVehicleLength) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			// 解析JSON数组
			Set<String> mergedLengths = getMergedLengths(wlhyVehicleLength, schedulerVehicleLength, objectMapper);
			excludedHandle(excludedVehicleLength, objectMapper, mergedLengths);
			// 过滤掉小于等于0的车长
			mergedLengths.removeIf(length -> {
				try {
					double value = Double.parseDouble(length);
					return value <= 0;
				} catch (NumberFormatException e) {
					// 如果无法解析为数字，保留该值
					return false;
				}
			});
			// 转换回JSON数组字符串
			if (mergedLengths.isEmpty()) {
				return null;
			}
			// 转换为列表并排序
			List<String> sortedList = new ArrayList<>(mergedLengths);
			Collections.sort(sortedList);
			return String.join("、", sortedList);
		} catch (Exception e) {
			log.error("处理车辆结构数据时发生错误", e);
			return null;
		}
	}

	/**
	 * 排除操作
	 * 
	 * @param excludedData
	 * @param objectMapper
	 * @param mergedLengths
	 * @throws JsonProcessingException
	 */
	private static void excludedHandle(String excludedData, ObjectMapper objectMapper, Set<String> mergedLengths) throws JsonProcessingException {
		List<String> excludedList = new ArrayList<>();
		excludedList = getStringList(excludedData, excludedList, objectMapper);
		// 排除excluded中的数据
		excludedList.forEach(mergedLengths::remove);
	}

	/**
	 * 合并操作
	 * 
	 * @param wlhyData
	 * @param schedulerData
	 * @param objectMapper
	 * @return
	 * @throws JsonProcessingException
	 */
	@NotNull
	private static Set<String> getMergedLengths(String wlhyData, String schedulerData, ObjectMapper objectMapper) throws JsonProcessingException {
		List<String> wlhyList = new ArrayList<>();
		List<String> schedulerList = new ArrayList<>();
		wlhyList = getStringList(wlhyData, wlhyList, objectMapper);
		schedulerList = getStringList(schedulerData, schedulerList, objectMapper);
		// 合并wlhy和scheduler的数据
		Set<String> mergedLengths = new HashSet<>();
		mergedLengths.addAll(wlhyList);
		mergedLengths.addAll(schedulerList);
		return mergedLengths;
	}

	/**
	 * 读取Json数据
	 * 
	 * @param stringData
	 * @param stringList
	 * @param objectMapper
	 * @return
	 * @throws JsonProcessingException
	 */
	private static List<String> getStringList(String stringData, List<String> stringList, ObjectMapper objectMapper) throws JsonProcessingException {
		if (StringUtils.isNotBlank(stringData)) {
			stringList = objectMapper.readValue(stringData, new TypeReference<List<String>>() {
			});
		}
		return stringList;
	}

	/**
	 * 根据司机数据生成经验评价
	 */
	public static String generateExperienceEvaluation(DriverProfileRespVO driver) {
		long serviceCount = driver.getServiceCount() != null ? driver.getServiceCount() : 0;
		AtomicLong orderCount = new AtomicLong();
		int drivingYears = driver.getDrivingYears() != null ? driver.getDrivingYears() : 0;
		List<Map<String, Object>> longDistanceRoutes = convertToRoutesList(driver.getLongDistanceRoutes());
		if (longDistanceRoutes != null && !longDistanceRoutes.isEmpty()) {
			longDistanceRoutes.forEach(d -> {
				Integer routeCount = (Integer) d.get("routeCount");
				orderCount.addAndGet(routeCount);
			});
		}
		long orderCountL = orderCount.get();
		if (drivingYears > 10 || serviceCount > 50 || orderCountL > 50) {
			return "资深司机，拥有丰富的运输经验，熟悉各类运输路线和货物处理，可胜任复杂运输任务";
		} else if (drivingYears > 5 || serviceCount > 25 || orderCountL > 25) {
			return "经验丰富的司机，熟悉多种运输场景，能够应对大部分运输挑战";
		} else if (drivingYears > 2 || serviceCount > 10 || orderCountL > 10) {
			return "有一定经验的司机，熟悉基本运输流程，可以胜任常规运输任务";
		} else {
			return "新手司机，经验有限，适合简单、固定路线的运输任务";
		}
	}

	/**
	 * 根据司机数据生成服务质量评价
	 */
	public static String generateServiceQuality(DriverProfileRespVO driver) {
		int profileCompleteness = driver.getProfileCompleteness() != null ? driver.getProfileCompleteness() : 0;
		boolean hasValidLicense = driver.getIsTheDrivrLicenseValid() != null && driver.getIsTheDrivrLicenseValid() == 1;
		boolean hasValidQualification = driver.getIsTheQualificationValid() != null && driver.getIsTheQualificationValid() == 1;
		if (profileCompleteness > 90 && hasValidLicense && hasValidQualification) {
			return "优质服务，资料完善，证件齐全有效，可靠性高，适合高要求运输任务";
		} else if (profileCompleteness > 70 && hasValidLicense) {
			return "良好服务，基本资料完善，驾驶证有效，可满足大多数运输需求";
		} else if (profileCompleteness > 50) {
			return "标准服务，资料基本完善，适合一般运输需求";
		} else {
			return "基础服务，资料不够完善，建议仅用于简单运输任务";
		}
	}

	/**
	 * 根据司机车辆数据生成车辆适应性评价
	 */
	private static String generateVehicleAdaptability(VehicleInfoVO vehicle) {
		StringBuilder evaluation = new StringBuilder();
		String vehicleType = vehicle.getVehicleTypeName();
		BigDecimal tonnage = vehicle.getVehicleTonnage();
		if (vehicleType != null && vehicleType.contains("重型")) {
			evaluation.append("重型运输车辆，适合大宗货物和长途运输");
		} else if (vehicleType != null && vehicleType.contains("半挂")) {
			evaluation.append("半挂车，适合集装箱和大型货物运输");
		} else if (vehicleType != null && vehicleType.contains("厢式")) {
			evaluation.append("厢式货车，适合普通货物和城际运输");
		} else if (tonnage != null && tonnage.compareTo(BigDecimal.valueOf(30)) > 0) {
			evaluation.append("大型运输车辆，载重能力强，适合重型货物");
		} else if (tonnage != null && tonnage.compareTo(BigDecimal.valueOf(10)) > 0) {
			evaluation.append("中型运输车辆，适合中等体积和重量的货物");
		} else {
			evaluation.append("暂无车辆类型信息，无法评估车辆适应性");
		}
		return evaluation.toString();
	}

	/**
	 * 获取司机来源描述
	 */
	public static String getOperationDesc(Integer operation) {
		if (operation == null)
			return "未知";
		switch (operation) {
		case 1:
			return "PC";
		case 2:
			return "微信小程序";
		case 3:
			return "司机名片推送";
		case 4:
			return "数据迁移";
		default:
			return "未知";
		}
	}

	/**
	 * 获取证件有效性描述
	 */
	public static String getLicenseValidDesc(Integer status) {
		if (status == null)
			return "未知";
		switch (status) {
		case 1:
			return "有效";
		case 0:
			return "即将失效";
		case -1:
			return "无效";
		default:
			return "未知";
		}
	}

	/**
	 * 获取司机状态描述
	 */
	public static String getDriverStateDesc(Integer state) {
		if (state == null)
			return "未知";
		switch (state) {
		case 0:
			return "生效";
		case 1:
			return "禁用";
		case -1:
			return "删除";
		default:
			return "未知";
		}
	}

	/**
	 * 获取能源类型描述
	 */
	public static String getEnergyTypeDesc(Long type) {
		if (type == null)
			return "未知";
		switch (type.intValue()) {
		case 1:
			return "汽油";
		case 2:
			return "柴油";
		case 3:
			return "天然气";
		case 4:
			return "纯电动";
		case 5:
			return "混合动力";
		default:
			return "未知";
		}
	}

	/**
	 * 获取GPS状态描述
	 */
	private static String getGpsStatusDesc(Integer status) {
		if (status == null)
			return "未知";
		switch (status) {
		case 1:
			return "已安装";
		case 2:
			return "未安装";
		case 0:
		default:
			return "未知";
		}
	}

	/**
	 * 获取车辆状态描述
	 */
	public static String getVehicleStateDesc(Integer state) {
		if (state == null)
			return "未知";
		switch (state) {
		case 0:
			return "生效";
		case 1:
			return "失效";
		case -1:
			return "删除";
		default:
			return "未知";
		}
	}

	/**
	 * 将路线信息字符串转换为List<Map<String, Object>>
	 *
	 * @param routesJson
	 *            路线信息JSON字符串
	 * @return 路线信息列表
	 */
	public static List<Map<String, Object>> convertToRoutesList(String routesJson) {
		if (StringUtils.isBlank(routesJson)) {
			return Collections.emptyList();
		}
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			return objectMapper.readValue(routesJson, new TypeReference<List<Map<String, Object>>>() {
			});
		} catch (Exception e) {
			log.error("解析路线信息失败", e);
			return Collections.emptyList();
		}
	}

	/**
	 * 将车辆信息字符串转换为VehicleInfoVO对象列表
	 *
	 * @param vehicles
	 *            车辆信息字符串
	 * @return 车辆信息对象列表
	 */
	public static List<VehicleInfoVO> convertToVehicleInfoList(String vehicles) {
		if (StringUtils.isBlank(vehicles)) {
			return Collections.emptyList();
		}
		try {
			// 使用Jackson进行JSON解析
			ObjectMapper objectMapper = new ObjectMapper();
			List<Map<String, Object>> vehicleList = objectMapper.readValue(vehicles, new TypeReference<List<Map<String, Object>>>() {
			});
			List<VehicleInfoVO> result = new ArrayList<>();
			for (Map<String, Object> vehicleMap : vehicleList) {
				VehicleInfoVO vehicleInfo = new VehicleInfoVO();
				// 设置基本信息
				vehicleInfo.setDriverId(getLongValue(vehicleMap.get("driverId")));
				vehicleInfo.setVehicleNumber(getStringValue(vehicleMap.get("vehicleNumber")));
				vehicleInfo.setVehicleType(getStringValue(vehicleMap.get("vehicleType")));
				vehicleInfo.setVehicleTypeName(getStringValue(vehicleMap.get("vehicleTypeName")));
				vehicleInfo.setVehicleLength(getLongValue(vehicleMap.get("vehicleLength")));
				vehicleInfo.setVehicleWidth(getLongValue(vehicleMap.get("vehicleWidth")));
				vehicleInfo.setVehicleHeight(getLongValue(vehicleMap.get("vehicleHeight")));
				vehicleInfo.setVehicleEnergyType(getLongValue(vehicleMap.get("vehicleEnergyType")));
				vehicleInfo.setVehicleEnergyTypeDesc(getStringValue(vehicleMap.get("vehicleEnergyTypeDesc")));
				vehicleInfo.setVehicleTonnage(getBigDecimalValue(vehicleMap.get("vehicleTonnage")));
				vehicleInfo.setGrossMass(getBigDecimalValue(vehicleMap.get("grossMass")));
				vehicleInfo.setState(getIntegerValue(vehicleMap.get("state")));
				vehicleInfo.setIsGps(getIntegerValue(vehicleMap.get("isGps")));
				vehicleInfo.setIsExist(getIntegerValue(vehicleMap.get("isExist")));
				vehicleInfo.setUseCharacter(getStringValue(vehicleMap.get("useCharacter")));
				vehicleInfo.setLastServiceTime(getDateValue(vehicleMap.get("lastServiceTime")));
				vehicleInfo.setServiceCount(getLongValue(vehicleMap.get("serviceCount")));
				vehicleInfo.setTotalCourse(getBigDecimalValue(vehicleMap.get("totalCourse")));
				vehicleInfo.setInfoIsComplete(getIntegerValue(vehicleMap.get("infoIsComplete")));
				vehicleInfo.setVehiclePlateColorCode(getIntegerValue(vehicleMap.get("vehiclePlateColorCode")));
				// 处理GPS信息
				if (vehicleMap.containsKey("gpsInfo")) {
					Map<String, Object> gpsMap = (Map<String, Object>) vehicleMap.get("gpsInfo");
					if (gpsMap != null) {
						VehicleGpsInfoVO gpsInfo = new VehicleGpsInfoVO();
						gpsInfo.setVehicleId(getLongValue(gpsMap.get("vehicleId")));
						gpsInfo.setLongitude(getStringValue(gpsMap.get("longitude")));
						gpsInfo.setLatitude(getStringValue(gpsMap.get("latitude")));
						gpsInfo.setAddress(getStringValue(gpsMap.get("address")));
						gpsInfo.setSpeed(getDoubleValue(gpsMap.get("speed")));
						gpsInfo.setDirection(getIntegerValue(gpsMap.get("direction")));
						gpsInfo.setAltitude(getDoubleValue(gpsMap.get("altitude")));
						gpsInfo.setProvince(getStringValue(gpsMap.get("province")));
						gpsInfo.setCity(getStringValue(gpsMap.get("city")));
						gpsInfo.setCountry(getStringValue(gpsMap.get("country")));
						gpsInfo.setUpdateTime(getDateValue(gpsMap.get("updateTime")));
						gpsInfo.setCreateTime(getDateValue(gpsMap.get("createTime")));
						vehicleInfo.setGpsInfo(gpsInfo);
					}
				}
				result.add(vehicleInfo);
			}
			return result;
		} catch (Exception e) {
			log.error("解析车辆信息失败", e);
			return Collections.emptyList();
		}
	}

	// 工具方法
	private static String getStringValue(Object value) {
		return value != null ? value.toString() : null;
	}

	private static Long getLongValue(Object value) {
		if (value == null)
			return null;
		if (value instanceof Number) {
			return ((Number) value).longValue();
		}
		try {
			return Long.parseLong(value.toString());
		} catch (NumberFormatException e) {
			return null;
		}
	}

	private static Integer getIntegerValue(Object value) {
		if (value == null)
			return null;
		if (value instanceof Number) {
			return ((Number) value).intValue();
		}
		try {
			return Integer.parseInt(value.toString());
		} catch (NumberFormatException e) {
			return null;
		}
	}

	private static Double getDoubleValue(Object value) {
		if (value == null)
			return null;
		if (value instanceof Number) {
			return ((Number) value).doubleValue();
		}
		try {
			return Double.parseDouble(value.toString());
		} catch (NumberFormatException e) {
			return null;
		}
	}

	private static BigDecimal getBigDecimalValue(Object value) {
		if (value == null)
			return null;
		if (value instanceof BigDecimal) {
			return (BigDecimal) value;
		}
		try {
			return new BigDecimal(value.toString());
		} catch (NumberFormatException e) {
			return null;
		}
	}

	private static Date getDateValue(Object value) {
		if (value == null)
			return null;
		if (value instanceof Date) {
			return (Date) value;
		}
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			return sdf.parse(value.toString());
		} catch (ParseException e) {
			return null;
		}
	}
}