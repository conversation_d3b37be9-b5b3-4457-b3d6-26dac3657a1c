package com.zly.common.utils;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;
import java.io.*;
import java.net.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 请求网络，模拟http请求
 *
 * @Title: HttpClienttUtils.java
 * @Description: http请求工具类
 * @Create DateTime: 2018年8月19日 下午9:24:12
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class HttpClientUtils {

	// 参数编码
	private static final String ENCODE_CHARSET = "UTF-8";
	// 读取超时时间
	private static final int READ_TIMEOUT = 240000;
	// 连接超时时间
	private static final int CONNECTION_TIMEOUT = 10 * 60 * 1000;

	private static PoolingHttpClientConnectionManager cm;

	private static List<Header> default_headers;

	private static RequestConfig config;

	private static HttpRequestRetryHandler httpRequestRetryHandler;

	private static CloseableHttpClient httpClient;

	@PostConstruct
	public void init() {
		log.info("初始化HTTP链接开始");

		// 同步http配置
		LayeredConnectionSocketFactory ssl = null;
		try {
			ssl = new SSLConnectionSocketFactory(SSLContext.getDefault());
		} catch (NoSuchAlgorithmException e) {
			log.info("初始化HTTP链接ssl失败：{}", ExceptionUtils.getStackTrace(e));
		}
		Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory> create().register("https", Objects.requireNonNull(ssl))
				.register("http", new PlainConnectionSocketFactory()).build();

		// 链接池配置
		cm = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
		// 连接池最大连接数
		int poolSize = 800;
		cm.setMaxTotal(poolSize);// 连接池最大并发连接数
		int maxPerRoute = 400;
		cm.setDefaultMaxPerRoute(maxPerRoute); // 单路由最大并发数,路由是对maxTotal的细分
		// headers
		default_headers = new ArrayList<Header>();
		default_headers.add(new BasicHeader("User-Agent", "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.16 Safari/537.36"));
		default_headers.add(new BasicHeader("Accept-Encoding", "gzip,deflate"));
		default_headers.add(new BasicHeader("Accept-Language", "zh-CN"));
		default_headers.add(new BasicHeader("Connection", "Close"));
		// 超时配置
		config = RequestConfig.copy(RequestConfig.DEFAULT).setSocketTimeout(READ_TIMEOUT).setConnectTimeout(CONNECTION_TIMEOUT).setConnectionRequestTimeout(CONNECTION_TIMEOUT).build();
		// 重试机制
		httpRequestRetryHandler = (exception, executionCount, context) -> {
			if (executionCount >= 5) {// 假设已经重试了5次，就放弃
				return false;
			}
			if (exception instanceof NoHttpResponseException) {// 假设server丢掉了连接。那么就重试
				return true;
			}
			if (exception instanceof SSLHandshakeException) {// 不要重试SSL握手异常
				return false;
			}
			if (exception instanceof InterruptedIOException) {// 超时
				return false;
			}
			if (exception instanceof UnknownHostException) {// 目标server不可达
				return false;
			}
			if (exception instanceof SSLException) {// SSL握手异常
				return false;
			}
			HttpClientContext clientContext = HttpClientContext.adapt(context);
			HttpRequest request = clientContext.getRequest();
			// 假设请求是幂等的，就再次尝试
			return !(request instanceof HttpEntityEnclosingRequest);
		};
		httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultHeaders(default_headers).setRetryHandler(httpRequestRetryHandler).setDefaultRequestConfig(config).build();
		log.info("初始化HTTP成功，httpClient：{}", httpClient);
	}

	/**
	 * get请求不要添加头文件
	 *
	 * @param url
	 *            url
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String getHttp(String url) throws Exception {
		return getHttpAddHeader(url, null, false);
	}

	/**
	 * get请求，添加头文件
	 *
	 * @param url
	 *            url
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String getHttp(String url, Map<String, String> mapHeader) throws Exception {
		return getHttpAddHeader(url, mapHeader, false);
	}

	/**
	 * get请求，添加头文件
	 *
	 * @param url
	 *            url
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String getJsonHttp(String url, Map<String, String> mapHeader) throws Exception {
		return getHttpAddHeader(url, mapHeader, true);
	}

	/**
	 * post请求，没有头内容,参数格式是json
	 *
	 * @param url
	 *            url
	 * @param map
	 *            map
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String postHttp(String url, Map<String, String> map) throws Exception {
		return postHttpAddHeader(url, mapToJson(map), null, false);
	}

	/**
	 * post请求，没有头内容,表单提交,参数格式是json
	 *
	 * @param url
	 *            url
	 * @param params
	 *            params
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String postHttp(String url, String params) throws Exception {
		return postHttpAddHeader(url, params, null, false);
	}

	/**
	 * post请求，默认json请求,参数格式是json
	 *
	 * @param url
	 *            url
	 * @param params
	 *            params
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String postJsonHttp(String url, String params) throws Exception {
		return postHttpAddHeader(url, params, null, true);
	}

	/**
	 * post请求，添加头内容,参数格式是json
	 *
	 * @param url
	 *            url
	 * @param params
	 *            params
	 * @param mapHeader
	 *            mapHeader
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String postHttp(String url, String params, Map<String, String> mapHeader) throws Exception {
		return postHttpAddHeader(url, params, mapHeader, true);
	}

	/**
	 * post请求，添加头内容,参数格式是json
	 *
	 * @param url
	 *            url
	 * @param map
	 *            map
	 * @param mapHeader
	 *            mapHeader
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String postJsonHttp(String url, Map<String, String> map, Map<String, String> mapHeader) throws Exception {
		return postHttpAddHeader(url, mapToJson(map), mapHeader, true);
	}

	/*	public static void postHttpAsync(String url, String params) {
			postHttpAsyncAddHeader(url, params, null);
		}*/

	/*
	public static void postHttpAsync(String url, String params, Map<String, String> mapHeader) {
		postHttpAsyncAddHeader(url, params, mapHeader);
	}*/

	/**
	 * post请求，接入方权限请求
	 *
	 * @param url
	 *            请求路径
	 * @param userName
	 *
	 * @param password
	 *            密码
	 * @param ip
	 *            ip
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String postHttpHeaderByCaller(String url, String userName, String password, String ip, String domainName) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		map.put("userName", userName);
		map.put("password", password);
		map.put("ip", ip);
		map.put("domainName", domainName);
		return postHttpAddHeader(url, mapToJson(map), null, true);
	}

	/**
	 * 使用getHttp请求，访问数据,需要添加头文件
	 *
	 * @param url
	 *            URl
	 * @param mapHeader
	 *            请求头
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	private static String getHttpAddHeader(String url, Map<String, String> mapHeader, Boolean isJsonContent) throws Exception {
		// 创建默认的httpClient实例
		String string = "";
		CloseableHttpResponse httpResponse = null;
		// 用get方法发送http请求
		try {
			HttpGet get = new HttpGet(url);
			getAddHeader(mapHeader, get, isJsonContent);
			log.info("HttpClientt执行get请求:...." + get.getURI());
			if (null == httpClient) {
				httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultHeaders(default_headers).setRetryHandler(httpRequestRetryHandler).setDefaultRequestConfig(config).build();
			}
			// 发送get请求
			httpResponse = httpClient.execute(get);
			// response实体
			HttpEntity entity = httpResponse.getEntity();
			// 添加返回数据
			if (null != entity && httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				string = EntityUtils.toString(entity);

				// 返回如果有乱码，则转换
				if (!Charset.forName("GBK").newEncoder().canEncode(string)) {
					string = new String(string.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8); // 转码UTF8
				}

				log.info("------  返回数据   ---------" + string);
			}
			// 并确保它被完全消耗。
			EntityUtils.consume(entity);
		} catch (Exception e) {
			log.error("HTTP get链接异常：", e);
			string = "";
		} finally {
			closeHttpResponse(httpResponse);
		}
		return string;
	}

	/**
	 * 使用postHttp请求，访问数据,需要添加头文件
	 *
	 * @param url
	 *            URl
	 * @param params
	 *            params
	 * @param mapHeader
	 *            请求头
	 * @return ResponseBean
	 * @throws Exception
	 *             异常
	 */
	private static String postHttpAddHeader(String url, String params, Map<String, String> mapHeader, Boolean isJsonContent) throws Exception {
		// 设置超时时间
		String string = "";
		CloseableHttpResponse httpResponse = null;
		try {
			// 添加头文件
			HttpPost post = new HttpPost(url);
			postAddHeader(mapHeader, post, isJsonContent);
			// url格式编码
			post.setEntity(new StringEntity(params, ENCODE_CHARSET));
			log.info("请求HTTP URL:{},参数:{}", url, params);
			// 执行请求
			if (null == httpClient) {
				httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultHeaders(default_headers).setRetryHandler(httpRequestRetryHandler).setDefaultRequestConfig(config).build();
			}
			httpResponse = httpClient.execute(post);
			HttpEntity entity = httpResponse.getEntity();
			// 添加返回数据
			if (null != entity && httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				string = EntityUtils.toString(entity, StandardCharsets.UTF_8);
			}
			log.info("请求HTTP返回结果{}", string);
			// 并确保它被完全消耗。
			EntityUtils.consume(entity);
		} catch (Exception e) {
			log.info("HTTP post链接异常：", e);
			string = "";
		} finally {
			closeHttpResponse(httpResponse);
		}
		return string;
	}

	/*private static void postHttpAsyncAddHeader(String url, String params, Map<String, String> mapHeader) {
		// 设置超时时间
		getHttpAsyncClient();
		HttpPost post = new HttpPost(url);
		try {
			// 添加头文件
			postAddHeader(mapHeader, post);
			// url格式编码
			post.setEntity(new StringEntity(params, ENCODE_CHARSET));
			log.info("请求HTTPAsync参数{}", params);
			// 执行请求
			httpAsyncClient.execute(post, new FutureCallback<HttpResponse>() {
				@Override
				public void completed(HttpResponse httpResponse) {
					try {
						HttpEntity entity = httpResponse.getEntity();
						String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
						if (httpResponse.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
							log.info("请求HTTPAsync结果成功,状态:{},结果:{},通知消息:{}", httpResponse.getStatusLine().getStatusCode(), result, params);
						} else {
							log.error("请求HTTPAsync结果失败,状态:{},结果:{},通知消息:{}", httpResponse.getStatusLine().getStatusCode(), result, params);
						}
						// 并确保它被完全消耗。
						EntityUtils.consume(entity);
					} catch (IOException e) {
						log.error("请求HTTPAsync结果处理异常,通知消息:{}", params);
					}
				}

				@Override
				public void failed(Exception ex) {
					log.error("请求HTTPAsync结果失败,异常消息:{},通知消息:{}", ex.getMessage(), params, ex);
				}

				@Override
				public void cancelled() {
					log.error("请求HTTPAsync结果取消,通知消息:{}", params);
				}

			});
		} catch (Exception e) {
			log.error("HTTP post链接异常", e);
			throw e;
		}
	}*/

	/**
	 * map转list
	 *
	 * @param map
	 *            参数map
	 * @return List<NameValuePair>
	 */
	@SuppressWarnings("unused")
	private static List<NameValuePair> mapToList(Map<String, String> map) {
		// 创建参数列表
		List<NameValuePair> list = new ArrayList<NameValuePair>();
		if (null != map) {
			for (Map.Entry<String, String> entry : map.entrySet()) {
				list.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}
		}
		return list;
	}

	/**
	 * map转json
	 *
	 * @param map
	 *            参数map
	 * @return List<NameValuePair>
	 */
	private static String mapToJson(Map<String, String> map) {
		return JSON.toJSONString(map);
	}

	/**
	 * post添加头文件
	 *
	 * @param map
	 *            参数map
	 * @param post
	 * @param isJsonContent
	 */
	private static void postAddHeader(Map<String, String> map, HttpPost post, Boolean isJsonContent) {
		if (null != isJsonContent && isJsonContent) {
			post.addHeader("Content-Type", "application/json;charset=UTF-8");
		} else {
			post.setHeader("Content-Type", "application/x-www-form-urlencoded");
		}
		if (null != map) {
			for (Map.Entry<String, String> entry : map.entrySet()) {
				post.addHeader(entry.getKey(), entry.getValue());
			}
		}
	}

	/**
	 * get添加头文件
	 *
	 * @param map
	 *            参数map
	 * @param get
	 *            get
	 */
	private static void getAddHeader(Map<String, String> map, HttpGet get, Boolean isJsonContent) {
		if (null != isJsonContent && isJsonContent) {
			get.addHeader("Content-Type", "application/json;charset=UTF-8");
		} else {
			get.setHeader("Content-Type", "application/x-www-form-urlencoded");
		}
		if (null != map) {
			for (Map.Entry<String, String> entry : map.entrySet()) {
				get.addHeader(entry.getKey(), entry.getValue());
			}
		}
	}

	/**
	 * 初始化HTTP链接
	 *
	 */
	private final static Object syncLock = new Object(); // 相当于线程锁,用于线程安全

	@SuppressWarnings("unused")
	private static void getHttpClient() {
		if (null == httpClient) {
			synchronized (syncLock) {
				httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultHeaders(default_headers).setRetryHandler(httpRequestRetryHandler).setDefaultRequestConfig(config).build();
			}
		}
	}

	/*private static void getHttpAsyncClient() {
		if (null == httpAsyncClient) {
			httpAsyncClient = HttpAsyncClients.custom().setConnectionManager(conMgr).setDefaultHeaders(default_headers).setDefaultAuthSchemeRegistry(authSchemeRegistry)
					.setDefaultCookieStore(new BasicCookieStore()).setDefaultRequestConfig(config).build();
			httpAsyncClient.start();
		}
	}*/

	/**
	 * 关闭HttpResponse链接
	 *
	 * @param httpResponse
	 *            httpResponse
	 * @throws IOException
	 *             异常
	 */
	private static void closeHttpResponse(CloseableHttpResponse httpResponse) throws Exception {
		if (httpResponse != null) {
			httpResponse.close();
		}
	}

	/**
	 * 发送HTTP_POST请求
	 *
	 * @param reqURL
	 *            请求地址
	 * @param params
	 *            发送到远程主机的正文数据[a=1,b=2]
	 * @param isBase64
	 *            参数是否需要BASE64加密
	 * @return String
	 */
	@SuppressWarnings("unused")
	public static String postRequestUTF8(String reqURL, String params, boolean isBase64) throws IOException {
		params = isBase64 ? Base64.encodeBase64String(StringUtils.getBytesUtf8(params)) : URLEncoder.encode(params, ENCODE_CHARSET);
		return postRequest(reqURL, params, null);
	}

	/**
	 * 发送HTTP_POST请求
	 *
	 * @param reqURL
	 *            请求地址
	 * @param params
	 *            发送到远程主机的正文数据[a=1,b=2]
	 * @param headers
	 *            请求头
	 * @return String
	 */
	@SuppressWarnings("unused")
	public static String postRequestUTF8(String reqURL, String params, Map<String, String> headers) {
		return postRequest(reqURL, params, headers);
	}

	/**
	 * 发送HTTP_POST请求
	 *
	 * @param reqURL
	 *            请求地址
	 * @param params
	 *            发送到远程主机的正文数据[a=1,b=2]
	 * @param isURLEncoder
	 *            参数是否需要URLEncoder编码
	 * @param charset
	 *            编码格式
	 * @return String
	 * @throws UnsupportedEncodingException
	 *             异常
	 */
	@SuppressWarnings("unused")
	public static String postRequest(String reqURL, String params, boolean isURLEncoder, String charset) throws UnsupportedEncodingException {
		params = new String(params.getBytes(charset), charset);
		params = isURLEncoder ? URLEncoder.encode(params, charset) : params;
		return postRequest(reqURL, params, null);
	}

	private static String postRequest(String reqURL, String params, Map<String, String> headers) {
		HttpURLConnection httpConn = null;
		DataOutputStream dos = null;
		BufferedReader responseReader = null;
		StringBuilder resultData = new StringBuilder();
		try {
			if (TextUtil.isNotNull(reqURL)) {
				// 建立连接
				log.info("HTTP链接请求参数--->" + params);
				URL url = new URL(reqURL);
				httpConn = (HttpURLConnection) url.openConnection();
				postAddHeader(headers, httpConn);
				// 设置参数
				httpConn.setDoOutput(true); // 需要输出
				httpConn.setDoInput(true); // 需要输入
				httpConn.setUseCaches(false); // 不允许缓存
				httpConn.setRequestMethod("POST"); // 设置POST方式连接
				httpConn.setConnectTimeout(CONNECTION_TIMEOUT);// 设置连接超时
				httpConn.setReadTimeout(READ_TIMEOUT); // 设置读取超时
				// 设置请求属性
				httpConn.setRequestProperty("Connection", "Keep-Alive");// 维持长连接
				httpConn.setRequestProperty("Charset", ENCODE_CHARSET);
				httpConn.setRequestProperty("Content-Length", params.getBytes().length + "");
				// 连接,也可以不用明文connect，使用下面的httpConn.getOutputStream()会自动connect
				httpConn.connect();
				// 建立输入流，向指向的URL传入参数
				dos = new DataOutputStream(httpConn.getOutputStream());
				dos.write(params.getBytes());
				dos.flush();
				// 获得响应状态
				int resultCode = httpConn.getResponseCode();
				log.info("HTTP链接返回状态CODE--->" + resultCode);
				if (HttpURLConnection.HTTP_OK == resultCode) {
					String readLine;
					responseReader = new BufferedReader(new InputStreamReader(httpConn.getInputStream(), ENCODE_CHARSET));
					while ((readLine = responseReader.readLine()) != null) {
						resultData.append(readLine);
					}
				}
			}
		} catch (Exception e) {
			log.error("HTTP链接异常：{},{}", ExceptionUtils.getStackTrace(e));
		} finally {
			try {
				if (null != responseReader) {
					responseReader.close();
				}
				if (null != dos) {
					dos.close();
				}
				if (null != httpConn) {
					httpConn.disconnect();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultData.toString();
	}

	/**
	 * 发送HTTP_POST请求<br/>
	 * 龙昆保险中使用到，其他模块中没有使用到（准备废弃）
	 *
	 * @param reqURL
	 *            请求地址
	 * @param params
	 *            发送到远程主机的正文数据[a=1,b=2]
	 * @param isBase64
	 *            参数是否需要BASE64加密
	 * @return String
	 */
	@Deprecated
	public static String postRequest(String reqURL, String params, boolean isBase64) {
		HttpURLConnection httpConn = null;
		DataOutputStream dos = null;
		BufferedReader responseReader = null;
		StringBuilder resultData = new StringBuilder();
		try {
			if (TextUtil.isNotNull(reqURL)) {
				String param = isBase64 ? Base64.encodeBase64String(StringUtils.getBytesUtf8(params)) : URLEncoder.encode(params, ENCODE_CHARSET);
				// 建立连接
				URL url = new URL(reqURL);
				httpConn = (HttpURLConnection) url.openConnection();
				// 设置参数
				httpConn.setDoOutput(true); // 需要输出
				httpConn.setDoInput(true); // 需要输入
				httpConn.setUseCaches(false); // 不允许缓存
				httpConn.setRequestMethod("POST"); // 设置POST方式连接
				httpConn.setConnectTimeout(CONNECTION_TIMEOUT);// 设置连接超时
				httpConn.setReadTimeout(READ_TIMEOUT); // 设置读取超时
				// 设置请求属性
				httpConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
				httpConn.setRequestProperty("Connection", "Keep-Alive");// 维持长连接
				httpConn.setRequestProperty("Charset", ENCODE_CHARSET);
				httpConn.setRequestProperty("Content-Length", param.getBytes().length + "");
				// 连接,也可以不用明文connect，使用下面的httpConn.getOutputStream()会自动connect
				httpConn.connect();
				// 建立输入流，向指向的URL传入参数
				dos = new DataOutputStream(httpConn.getOutputStream());
				dos.writeBytes(param);
				dos.flush();
				// 获得响应状态
				int resultCode = httpConn.getResponseCode();
				if (HttpURLConnection.HTTP_OK == resultCode) {
					String readLine;
					responseReader = new BufferedReader(new InputStreamReader(httpConn.getInputStream(), ENCODE_CHARSET));
					while ((readLine = responseReader.readLine()) != null) {
						resultData.append(readLine);
					}
				}
			}
		} catch (Exception e) {
			log.error("http调用异常：{}", ExceptionUtils.getStackTrace(e));
		} finally {
			try {
				if (null != responseReader) {
					responseReader.close();
				}
				if (null != dos) {
					dos.close();
				}
				if (null != httpConn) {
					httpConn.disconnect();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultData.toString();
	}

	/**
	 * post添加头文件
	 *
	 * @param map
	 *            请求参数
	 * @param httpConn
	 *            httpConn
	 */
	private static void postAddHeader(Map<String, String> map, HttpURLConnection httpConn) {
		if (null != map) {
			for (Map.Entry<String, String> entry : map.entrySet()) {
				httpConn.setRequestProperty(entry.getKey(), entry.getValue());
			}
			if (!map.containsKey("Content-Type")) {
				httpConn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
			}
		} else {
			httpConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
		}
	}

	public static String doPolicyPost(String url, String json, String acc, String pas) throws Exception {
		String body;
		HttpEntity responseEntity = null;
		HttpClient httpClient = new DefaultHttpClient();
		try {
			HttpPost post = new HttpPost(url);
			StringEntity requestEntity = new StringEntity(json, "UTF-8");
			post.setEntity(requestEntity);
			post.setHeader("Content-Type", "application/json;charset=utf-8");
			if (TextUtil.isNotNull(acc)) {
				post.setHeader("acc", acc);
			}
			if (TextUtil.isNotNull(pas)) {
				post.setHeader("pas", pas);
			}
			HttpResponse httpresponse = httpClient.execute(post);
			responseEntity = httpresponse.getEntity();
			body = EntityUtils.toString(responseEntity, "UTF-8");
		} catch (ConnectException ce) {
			ce.printStackTrace();
			return "{\"code\":\"-9999\",\"message\":\"网络连接失败\"}";
		} catch (Exception ex) {
			ex.printStackTrace();
			throw new Exception(ex.getMessage());
		} finally {
			try {
				EntityUtils.consume(responseEntity);
			} catch (IOException e) {
				e.printStackTrace();
			}
			httpClient.getConnectionManager().shutdown();
		}
		return body;
	}

	@SuppressWarnings({ "resource" })
	public static String doEtcPost(String url, String json) {
		String body;
		HttpEntity responseEntity = null;
		HttpClient httpClient = new DefaultHttpClient();
		try {
			HttpPost post = new HttpPost(url);
			StringEntity requestEntity = new StringEntity(json, "UTF-8");
			post.setEntity(requestEntity);
			post.setHeader("Content-Type", "application/json;charset=utf-8");
			post.setHeader("acc",/* Const.ETC_USER*/"tms");
			post.setHeader("pas", /*Const.ETC_PWD*/"123");

			HttpResponse httpresponse = httpClient.execute(post);
			responseEntity = httpresponse.getEntity();
			body = EntityUtils.toString(responseEntity, "UTF-8");
			// System.out.println(body);
		} catch (Exception ex) {
			ex.printStackTrace();
			return "exception";
		} finally {
			try {
				EntityUtils.consume(responseEntity);
			} catch (IOException e) {
				e.printStackTrace();
			}
			httpClient.getConnectionManager().shutdown();
		}
		return body;
	}
}
