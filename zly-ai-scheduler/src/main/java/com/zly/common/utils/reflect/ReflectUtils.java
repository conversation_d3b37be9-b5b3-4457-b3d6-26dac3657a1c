package com.zly.common.utils.reflect;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.zly.common.exception.ServiceException;
import com.zly.framework.aspectj.lang.annotation.ClientLog;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.poi.ss.usermodel.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.zly.common.core.text.Convert;
import com.zly.common.utils.DateUtils;

/**
 * 反射工具类. 提供调用getter/setter方法, 访问私有变量, 调用私有方法, 获取泛型类型Class, 被AOP过的真实类等工具函数.
 * 
 * <AUTHOR>
 */
@SuppressWarnings("rawtypes")
public class ReflectUtils
{
    private static final String SETTER_PREFIX = "set";

    private static final String GETTER_PREFIX = "get";

    private static final String CGLIB_CLASS_SEPARATOR = "$$";

    private static Logger logger = LoggerFactory.getLogger(ReflectUtils.class);

    /**
     * 调用Getter方法.
     * 支持多级，如：对象名.对象名.方法
     */
    @SuppressWarnings("unchecked")
    public static <E> E invokeGetter(Object obj, String propertyName)
    {
        Object object = obj;
        for (String name : StringUtils.split(propertyName, "."))
        {
            String getterMethodName = GETTER_PREFIX + StringUtils.capitalize(name);
            object = invokeMethod(object, getterMethodName, new Class[] {}, new Object[] {});
        }
        return (E) object;
    }

    /**
     * 调用Setter方法, 仅匹配方法名。
     * 支持多级，如：对象名.对象名.方法
     */
    public static <E> void invokeSetter(Object obj, String propertyName, E value)
    {
        Object object = obj;
        String[] names = StringUtils.split(propertyName, ".");
        for (int i = 0; i < names.length; i++)
        {
            if (i < names.length - 1)
            {
                String getterMethodName = GETTER_PREFIX + StringUtils.capitalize(names[i]);
                object = invokeMethod(object, getterMethodName, new Class[] {}, new Object[] {});
            }
            else
            {
                String setterMethodName = SETTER_PREFIX + StringUtils.capitalize(names[i]);
                invokeMethodByName(object, setterMethodName, new Object[] { value });
            }
        }
    }

    /**
     * 直接读取对象属性值, 无视private/protected修饰符, 不经过getter函数.
     */
    @SuppressWarnings("unchecked")
    public static <E> E getFieldValue(final Object obj, final String fieldName)
    {
        Field field = getAccessibleField(obj, fieldName);
        if (field == null)
        {
            logger.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + fieldName + "] 字段 ");
            return null;
        }
        E result = null;
        try
        {
            result = (E) field.get(obj);
        }
        catch (IllegalAccessException e)
        {
            logger.error("不可能抛出的异常{}", e.getMessage());
        }
        return result;
    }

    /**
     * 直接设置对象属性值, 无视private/protected修饰符, 不经过setter函数.
     */
    public static <E> void setFieldValue(final Object obj, final String fieldName, final E value)
    {
        Field field = getAccessibleField(obj, fieldName);
        if (field == null)
        {
            // throw new IllegalArgumentException("在 [" + obj.getClass() + "] 中，没有找到 [" + fieldName + "] 字段 ");
            logger.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + fieldName + "] 字段 ");
            return;
        }
        try
        {
            field.set(obj, value);
        }
        catch (IllegalAccessException e)
        {
            logger.error("不可能抛出的异常: {}", e.getMessage());
        }
    }

    /**
     * 直接调用对象方法, 无视private/protected修饰符.
     * 用于一次性调用的情况，否则应使用getAccessibleMethod()函数获得Method后反复调用.
     * 同时匹配方法名+参数类型，
     */
    @SuppressWarnings("unchecked")
    public static <E> E invokeMethod(final Object obj, final String methodName, final Class<?>[] parameterTypes,
            final Object[] args)
    {
        if (obj == null || methodName == null)
        {
            return null;
        }
        Method method = getAccessibleMethod(obj, methodName, parameterTypes);
        if (method == null)
        {
            logger.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + methodName + "] 方法 ");
            return null;
        }
        try
        {
            return (E) method.invoke(obj, args);
        }
        catch (Exception e)
        {
            String msg = "method: " + method + ", obj: " + obj + ", args: " + args + "";
            throw convertReflectionExceptionToUnchecked(msg, e);
        }
    }

    /**
     * 直接调用对象方法, 无视private/protected修饰符，
     * 用于一次性调用的情况，否则应使用getAccessibleMethodByName()函数获得Method后反复调用.
     * 只匹配函数名，如果有多个同名函数调用第一个。
     */
    @SuppressWarnings("unchecked")
    public static <E> E invokeMethodByName(final Object obj, final String methodName, final Object[] args)
    {
        Method method = getAccessibleMethodByName(obj, methodName, args.length);
        if (method == null)
        {
            // 如果为空不报错，直接返回空。
            logger.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + methodName + "] 方法 ");
            return null;
        }
        try
        {
            // 类型转换（将参数数据类型转换为目标方法参数类型）
            Class<?>[] cs = method.getParameterTypes();
            for (int i = 0; i < cs.length; i++)
            {
                if (args[i] != null && !args[i].getClass().equals(cs[i]))
                {
                    if (cs[i] == String.class)
                    {
                        args[i] = Convert.toStr(args[i]);
                        if (StringUtils.endsWith((String) args[i], ".0"))
                        {
                            args[i] = StringUtils.substringBefore((String) args[i], ".0");
                        }
                    }
                    else if (cs[i] == Integer.class)
                    {
                        args[i] = Convert.toInt(args[i]);
                    }
                    else if (cs[i] == Long.class)
                    {
                        args[i] = Convert.toLong(args[i]);
                    }
                    else if (cs[i] == Double.class)
                    {
                        args[i] = Convert.toDouble(args[i]);
                    }
                    else if (cs[i] == Float.class)
                    {
                        args[i] = Convert.toFloat(args[i]);
                    }
                    else if (cs[i] == Date.class)
                    {
                        if (args[i] instanceof String)
                        {
                            args[i] = DateUtils.parseDate(args[i]);
                        }
                        else
                        {
                            args[i] = DateUtil.getJavaDate((Double) args[i]);
                        }
                    }
                    else if (cs[i] == boolean.class || cs[i] == Boolean.class)
                    {
                        args[i] = Convert.toBool(args[i]);
                    }
                }
            }
            return (E) method.invoke(obj, args);
        }
        catch (Exception e)
        {
            String msg = "method: " + method + ", obj: " + obj + ", args: " + args + "";
            throw convertReflectionExceptionToUnchecked(msg, e);
        }
    }

    /**
     * 循环向上转型, 获取对象的DeclaredField, 并强制设置为可访问.
     * 如向上转型到Object仍无法找到, 返回null.
     */
    public static Field getAccessibleField(final Object obj, final String fieldName)
    {
        // 为空不报错。直接返回 null
        if (obj == null)
        {
            return null;
        }
        Validate.notBlank(fieldName, "fieldName can't be blank");
        for (Class<?> superClass = obj.getClass(); superClass != Object.class; superClass = superClass.getSuperclass())
        {
            try
            {
                Field field = superClass.getDeclaredField(fieldName);
                makeAccessible(field);
                return field;
            }
            catch (NoSuchFieldException e)
            {
                continue;
            }
        }
        return null;
    }

    /**
     * 循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.
     * 如向上转型到Object仍无法找到, 返回null.
     * 匹配函数名+参数类型。
     * 用于方法需要被多次调用的情况. 先使用本函数先取得Method,然后调用Method.invoke(Object obj, Object... args)
     */
    public static Method getAccessibleMethod(final Object obj, final String methodName,
            final Class<?>... parameterTypes)
    {
        // 为空不报错。直接返回 null
        if (obj == null)
        {
            return null;
        }
        Validate.notBlank(methodName, "methodName can't be blank");
        for (Class<?> searchType = obj.getClass(); searchType != Object.class; searchType = searchType.getSuperclass())
        {
            try
            {
                Method method = searchType.getDeclaredMethod(methodName, parameterTypes);
                makeAccessible(method);
                return method;
            }
            catch (NoSuchMethodException e)
            {
                continue;
            }
        }
        return null;
    }

    /**
     * 循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.
     * 如向上转型到Object仍无法找到, 返回null.
     * 只匹配函数名。
     * 用于方法需要被多次调用的情况. 先使用本函数先取得Method,然后调用Method.invoke(Object obj, Object... args)
     */
    public static Method getAccessibleMethodByName(final Object obj, final String methodName, int argsNum)
    {
        // 为空不报错。直接返回 null
        if (obj == null)
        {
            return null;
        }
        Validate.notBlank(methodName, "methodName can't be blank");
        for (Class<?> searchType = obj.getClass(); searchType != Object.class; searchType = searchType.getSuperclass())
        {
            Method[] methods = searchType.getDeclaredMethods();
            for (Method method : methods)
            {
                if (method.getName().equals(methodName) && method.getParameterTypes().length == argsNum)
                {
                    makeAccessible(method);
                    return method;
                }
            }
        }
        return null;
    }

    /**
     * 改变private/protected的方法为public，尽量不调用实际改动的语句，避免JDK的SecurityManager抱怨。
     */
    public static void makeAccessible(Method method)
    {
        if ((!Modifier.isPublic(method.getModifiers()) || !Modifier.isPublic(method.getDeclaringClass().getModifiers()))
                && !method.isAccessible())
        {
            method.setAccessible(true);
        }
    }

    /**
     * 改变private/protected的成员变量为public，尽量不调用实际改动的语句，避免JDK的SecurityManager抱怨。
     */
    public static void makeAccessible(Field field)
    {
        if ((!Modifier.isPublic(field.getModifiers()) || !Modifier.isPublic(field.getDeclaringClass().getModifiers())
                || Modifier.isFinal(field.getModifiers())) && !field.isAccessible())
        {
            field.setAccessible(true);
        }
    }

    /**
     * 通过反射, 获得Class定义中声明的泛型参数的类型, 注意泛型必须定义在父类处
     * 如无法找到, 返回Object.class.
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getClassGenricType(final Class clazz)
    {
        return getClassGenricType(clazz, 0);
    }

    /**
     * 通过反射, 获得Class定义中声明的父类的泛型参数的类型.
     * 如无法找到, 返回Object.class.
     */
    public static Class getClassGenricType(final Class clazz, final int index)
    {
        Type genType = clazz.getGenericSuperclass();

        if (!(genType instanceof ParameterizedType))
        {
            logger.debug(clazz.getSimpleName() + "'s superclass not ParameterizedType");
            return Object.class;
        }

        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();

        if (index >= params.length || index < 0)
        {
            logger.debug("Index: " + index + ", Size of " + clazz.getSimpleName() + "'s Parameterized Type: "
                    + params.length);
            return Object.class;
        }
        if (!(params[index] instanceof Class))
        {
            logger.debug(clazz.getSimpleName() + " not set the actual class on superclass generic parameter");
            return Object.class;
        }

        return (Class) params[index];
    }

    public static Class<?> getUserClass(Object instance)
    {
        if (instance == null)
        {
            throw new RuntimeException("Instance must not be null");
        }
        Class clazz = instance.getClass();
        if (clazz != null && clazz.getName().contains(CGLIB_CLASS_SEPARATOR))
        {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && !Object.class.equals(superClass))
            {
                return superClass;
            }
        }
        return clazz;

    }

    /**
     * 将反射时的checked exception转换为unchecked exception.
     */
    public static RuntimeException convertReflectionExceptionToUnchecked(String msg, Exception e)
    {
        if (e instanceof IllegalAccessException || e instanceof IllegalArgumentException
                || e instanceof NoSuchMethodException)
        {
            return new IllegalArgumentException(msg, e);
        }
        else if (e instanceof InvocationTargetException)
        {
            return new RuntimeException(msg, ((InvocationTargetException) e).getTargetException());
        }
        return new RuntimeException(msg, e);
    }


    /**
     * 新老数据集合对比，判断是否有数据修改
     */
    public static <T> boolean dataCompare(List<T> newData, List<T> oldData) {
        if (newData.size() != oldData.size()) {
            return false;
        }
        for (int i = 0; i < newData.size(); i++) {
            if (!dataCompare(newData.get(i), oldData.get(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 新老数据对比，判断是否有数据修改
     */
    public static <T> boolean dataCompare(T newData, T oldData) {
        if (newData == null || oldData == null) {
            throw new ServiceException("新数据源或老数据源为空");
        }

        Class<?> sourceClass = newData.getClass();
        Class<?> targetClass = oldData.getClass();

        if (!sourceClass.equals(targetClass)) {
            throw new ServiceException("新数据源与老数据源类型不相同");
        }

        Field[] fields = sourceClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object newValue = field.get(newData);
                Object oldValue = field.get(oldData);
                // 如果新数据中的字段值为 null，则跳过比较
                if (newValue == null) {
                    continue;
                }
                // 如果新旧数据中的字段值不相等，则返回 false
                if (!newValue.equals(oldValue)) {
                    //System.out.println(newValue + "<-----------------"+field.getName()+"------------------>" + oldValue);
                    return false;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }finally {
                field.setAccessible(false);
            }
        }
        return true;
    }




    /**
     * 新老数据集合对比，判断是否有数据修改
     */
    public static <T> String convertClassDataToChineseList(List<T> classValues) {
        List<String> joiner = new ArrayList<>();
        for (int i = 0; i < classValues.size(); i++) {
            List<String> strings = convertClassDataToChinese(classValues.get(i));
            joiner = Stream.concat(joiner.stream(), strings.stream()).distinct().collect(Collectors.toList());

        }
        return StringUtils.join(joiner,"，");
    }

    /**
     * 类转换为中文翻译字符串
     * @param classValue
     * @return
     */
    public static <T> List<String> convertClassDataToChinese(T classValue){

        if(null == classValue){
            return new ArrayList<>();
        }

        // 获取类的所有字段
        Field[] fields = classValue.getClass().getDeclaredFields();

        // 拼接字段注解值
        List<String> joiner = new ArrayList<>();
        //关键字
        String keyWord = "";
        for (Field field : fields) {

            try {
                //打破封装
                field.setAccessible(true);
                //通过反射获取字段上每个注解对象
                ClientLog clientLog = field.getAnnotation(ClientLog.class);
                //添加了该注解字段不需要获取
                if(null == clientLog || !clientLog.isKeyWord()){
                    continue;
                }
                //获取该字段value
                Object value = field.get(classValue);
                //拿到ClientLog注解 文本值
                String text = clientLog.text();
                if(StringUtils.isBlank(text)){
                    keyWord = "" + value;
                }else{
                    keyWord = text.replace("%","" + value);
                }
                break;
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }finally {
                field.setAccessible(false);
            }



        }

        for (Field field : fields) {

            try {
                // 打破封装获取字段值
                field.setAccessible(true);
                Object value = field.get(classValue);
                //如果该字段为null就直接跳过
                if (null == value) {
                    continue;
                }
                //获取字段名称
                String name = field.getName();
                if("serialVersionUID".equals(name)){
                    //不需要序列号
                    continue;
                }
                //不需要运单子表中运单子表信息
                if("waybillSlave".equals(name)){
                    continue;
                }
                //屏蔽掉运单子表json串
                if(name.contains("Detail")){
                    continue;
                }
                //包含id也不要
                if(name.equals("id") || name.endsWith("Id") || name.contains("ID") || name.contains("iD")){
                    continue;
                }
                //通过反射获取字段上每个注解对象
                ClientLog clientLog = field.getAnnotation(ClientLog.class);
                //添加了该注解字段不需要获取
                if(null != clientLog){
                    continue;
                }
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                if (null != annotation) {
                    // 优先取值 获取注解name名称
                    name = annotation.name();
                    //次取 notes
                    if (com.zly.common.utils.StringUtils.isEmpty(name)) {
                        //没有注解就取默认值
                        name = annotation.notes();
                    }
                    //次取 value
                    if (com.zly.common.utils.StringUtils.isEmpty(name)) {
                        //没有注解就取默认值
                        name = annotation.value();
                    }

                    //次取 还没有就取字段
                    if (com.zly.common.utils.StringUtils.isEmpty(name)) {
                        //没有注解就取默认值
                        name = field.getName();
                    }

                    //处理包含字典的注释名
                    if(name.contains("字典")){
                        name = name.substring(0,name.indexOf("字"));
                    }
                }

                /*if( field.getName().equals("effectiveTime")){
                    Field timeUnit = Arrays.stream(fields).filter(f -> f.getName().equals("timeUnit")).findFirst().orElse(null);
                    if(null != timeUnit){
                        ApiModelProperty timeUnitAnnotation = timeUnit.getAnnotation(ApiModelProperty.class);
                        //拼接字段
                        //判断是否有规则标记 有说明是状态类型之类字段 需要进一步处理
                        if (null != timeUnitAnnotation && !StringUtils.isEmpty(timeUnitAnnotation.allowableValues())) {
                            //用约定好“,”进行分割规则
                            List<String> types = Arrays.asList(timeUnitAnnotation.allowableValues().split(","));
                            //筛选当前字段值是否包含在规则中
                            String type = types.stream().filter(f -> f.contains("" + value)).findFirst().orElse(null);
                            //截取规则名称
                            if (!StringUtils.isEmpty(type)) {
                                type = type.substring(type.indexOf(":") + 1);//约定必须有:
                            }
                            //拼接字段
                            joiner.add(name + "：" + type +"br");
                            continue;
                        }
                    }

                }*/

                //判断是否有规则标记 有说明是状态类型之类字段 需要进一步处理
                if (null != annotation && !StringUtils.isEmpty(annotation.allowableValues())) {
                    //用约定好“,”进行分割规则
                    List<String> types = Arrays.asList(annotation.allowableValues().split(","));
                    //筛选当前字段值是否包含在规则中
                    String type = types.stream().filter(f -> StringUtils.trim(f.split(":")[0]).equals("" + value)).findFirst().orElse(null);
                    //截取规则名称
                    if (!StringUtils.isEmpty(type)) {
                        type = type.substring(type.indexOf(":") + 1);//约定必须有:
                    } else {
                        //没有就给当前值
                        type = "" + value;
                    }
                    //拼接字段
                    joiner.add(keyWord + name + "：" + type);
                    continue;
                }

                //先用Date类型去比对 再用注释内容做比对
                if (field.getType() == Date.class) {
                    String dateToStr = DateUtils.dateToStr((Date) value);
                    if (null != dateToStr) {
                        if (dateToStr.contains("00:00:00")) {
                            dateToStr = dateToStr.substring(0, dateToStr.indexOf(' '));
                        }
                        if ("2099-12-31".equals(dateToStr)) {
                            dateToStr = "长期有效";
                        }
                    }

                    joiner.add(name + "：" + dateToStr);
                } else if (name.contains("有效期")) {
                    String dateToStr = (String) value;
                    if (!StringUtils.isEmpty(dateToStr)) {
                        if (dateToStr.contains("00:00:00")) {
                            dateToStr = dateToStr.substring(0, dateToStr.indexOf(' '));
                        }
                        if ("2099-12-31".equals(dateToStr)) {
                            dateToStr = "长期有效";
                        }
                    }

                    joiner.add(keyWord + name + "：" + dateToStr);
                } else {
                    joiner.add(keyWord + name + "：" + value);
                }

            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }finally {
                field.setAccessible(false);
            }

        }
        /*if(joiner.isEmpty()){
            return "";
        }else{
            String className = classValue.getClass().getSimpleName();
            ApiModel apiModel = classValue.getClass().getAnnotation(ApiModel.class);
            if(apiModel != null){
                className = apiModel.value();
                if(StringUtils.isEmpty(className)){
                    className = classValue.getClass().getSimpleName();
                }
            }

            return className + "{" + StringUtils.join(joiner,",") + "}";
        }*/
        //return StringUtils.join(joiner," <br> ");
        return joiner;
    }


    /**
     * 新老数据比对
     */
    public static <T> void compareAndReplaceOldDataList(List<T> newData, List<T> oldData){
        /*if (newData.size() != oldData.size() ) {
            return ;
        }*/
        for (int i = 0; i < newData.size(); i++) {
            if (i < oldData.size()) {
                compareAndReplaceOldData(newData.get(i), oldData.get(i));
            }

        }
    }

    /**
     * 新老数据比对
     */
    public static <T> void compareAndReplaceOldData(T newData, T oldData) {
        if (newData == null || oldData == null) {
            return;
        }

        try {
            // 获取对象所有字段
            Field[] fields = newData.getClass().getDeclaredFields();

            // 遍历所有字段进行比较
            for (Field field : fields) {
                //获取字段名称
                String name = field.getName();
                if("serialVersionUID".equals(name)){
                    //不需要序列号
                    continue;
                }
                /*if("fileName".equals(name) ||"fileUrl".equals(name)){
                    //附件名称也不比 附件查询就两个参数 只要上面整体比对没通过就整体
                    continue;
                }*/
                //屏蔽掉运单子表json串
                if(name.contains("Detail")){
                    continue;
                }

                //时间单位也不做比较
               /* if(name.contains("timeUnit")){
                    continue;
                }*/

                //通过反射获取字段上每个注解对象
                ClientLog clientLog = field.getAnnotation(ClientLog.class);
                //添加了该注解字段不需要获取 字段上加了该注解 并且需要显示得
                if(null != clientLog && clientLog.isKeyWord()){
                    continue;
                }

                field.setAccessible(true); // 设置可访问私有字段

                // 获取newData和oldData中当前字段的值
                Object newValue = field.get(newData);
                Object oldValue = field.get(oldData);

                // 如果字段值相等或者都为null，将oldData中的字段置为null
                if ((newValue != null && oldValue != null) && newValue.equals(oldValue)) {
                    field.set(oldData, null); // 将字段置为空
                    field.set(newData, null); // 将字段置为空
                }

                field.setAccessible(false);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }

    }

}
