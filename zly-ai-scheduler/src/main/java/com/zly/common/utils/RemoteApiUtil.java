package com.zly.common.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zly.common.constant.ApiUrlConstants;
import com.zly.framework.redis.RedisCache;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.zly.common.exception.util.ServiceExceptionUtil.exception;
import static com.zly.enums.ErrorCodeConstants.BASE_SERVER_ERROR;

/**
 * 远程API调用工具类，支持签名、异常处理
 */
@Component
public class RemoteApiUtil {

    private static final Logger logger = LoggerFactory.getLogger(RemoteApiUtil.class);

    /** Token 缓存前缀 */
    private static final String TOKEN_CACHE_PREFIX = "remote_api_token:";

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RedisCache redisCache;

    @Value("${custom.scheduler.auth.clientId:}")
    private String clientId;

    @Value("${custom.scheduler.auth.clientSecret:}")
    private String clientSecret;

    @Value("${custom.scheduler.auth.type:2}")
    private Integer type;

    @Value("${custom.scheduler.auth.tokenExpireTime:600}")
    private long tokenExpireTime;


    /**
     * 获取访问令牌
     *
     * @return 访问令牌
     */
    public String getAccessToken() {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        // 先从缓存中获取
        String cacheKey = TOKEN_CACHE_PREFIX + clientId + "_" + userId;
        String cachedToken = redisCache.getCacheObject(cacheKey);

        if (StringUtils.isNotEmpty(cachedToken)) {
            logger.debug("从缓存中获取到token: {}", cachedToken);
            return cachedToken;
        }

        // 缓存中没有，调用获取token接口
        return refreshAccessToken();
    }

    /**
     * 刷新访问令牌
     *
     * @return 新的访问令牌
     */
    public String refreshAccessToken() {
        if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret)) {
            logger.error("Token获取配置不完整，无法获取token");
            throw exception(BASE_SERVER_ERROR, "Token获取配置不完整，无法获取token");
        }
        try {
            logger.info("开始获取访问令牌, 客户端ID: {}", clientId);

            // 生成时间戳
            long timestamp = System.currentTimeMillis();

            Long userId = SecurityUtils.getLoginUser().getUserId();
            // 计算签名：clientId + userId + type + timestamp + clientSecret
            String signStr = clientId + userId + type + timestamp + clientSecret;
            String signature = DigestUtil.md5Hex(signStr);

            // 构建请求参数
            HttpEntity<String> requestEntity = getRequestEntity(timestamp, signature);

            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    ApiUrlConstants.getBaseUrl() +ApiUrlConstants.GET_TOKEN,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            String responseBody = responseEntity.getBody();
            JSONObject jsonObject = JSONUtil.parseObj(responseBody);
            String code = jsonObject.getStr("code");

            if ("200".equals(code)) {
                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null) {
                    String token = data.getStr("token");
                    if (StringUtils.isNotEmpty(token)) {
                        // 缓存token
                        String cacheKey = TOKEN_CACHE_PREFIX + clientId + "_" + userId;
                        redisCache.setCacheObject(cacheKey, token, (int) tokenExpireTime, TimeUnit.SECONDS);
                        logger.info("成功获取并缓存访问令牌");
                        return token;
                    } else {
                        logger.error("Token响应中未找到token字段");
                        throw exception(BASE_SERVER_ERROR, "Token响应中未找到token字段");
                    }
                } else {
                    logger.error("Token响应中未找到data字段");
                    throw exception(BASE_SERVER_ERROR, "Token响应中未找到data字段");
                }
            } else {
                String msg = jsonObject.getStr("msg", "获取Token失败");
                logger.error("获取Token失败: {}", msg);
                throw exception(BASE_SERVER_ERROR, "获取Token失败: " + msg);
            }
        } catch (Exception e) {
            logger.error("获取访问令牌异常", e);
            throw exception(BASE_SERVER_ERROR, "获取访问令牌异常: " + e.getMessage());
        }
    }

    @NotNull
    private HttpEntity<String> getRequestEntity(long timestamp, String signature) {
        JSONObject tokenRequest = new JSONObject();
        tokenRequest.set("clientId", clientId);
        tokenRequest.set("clientSecret", clientSecret);
        Long userId = SecurityUtils.getLoginUser().getUserId();
        tokenRequest.set("userId", userId);
        tokenRequest.set("type", type);
        tokenRequest.set("timestamp", timestamp);
        tokenRequest.set("signature", signature);

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");

		return new HttpEntity<>(tokenRequest.toString(), headers);
    }

    /**
     * 发送带签名的POST请求，自动处理异常
     * @param url 请求地址
     * @param bodyObj 请求体对象
     * @param paramStr 请求参数字符串
     * @return JSONObject 响应内容
     */
    public JSONObject postWithSign(String url, Object bodyObj, String paramStr) {
        return postWithSign(url, bodyObj, true, paramStr);
    }

    /**
     * 发送带签名的POST请求，自动处理异常
     * @param url 请求地址
     * @param bodyObj 请求体对象
     * @param requestParams 请求参数Map
     * @return JSONObject 响应内容
     */
    public JSONObject postWithSign(String url, Object bodyObj, java.util.Map<String, Object> requestParams) {
        return postWithSign(url, bodyObj, true, mapToParamString(requestParams));
    }

    /**
     * 发送带签名的POST请求，自动处理异常
     * @param url 请求地址
     * @param bodyObj 请求体对象
     * @param needAuth 是否需要认证token
     * @param requestParams 请求参数Map
     * @return JSONObject 响应内容
     */
    public JSONObject postWithSign(String url, Object bodyObj, boolean needAuth, java.util.Map<String, Object> requestParams) {
        return postWithSign(url, bodyObj, needAuth, mapToParamString(requestParams));
    }

    /**
     * 发送带签名的POST请求，自动处理异常
     * @param url 请求地址
     * @param bodyObj 请求体对象
     * @return JSONObject 响应内容
     */
    public JSONObject postWithSign(String url, Object bodyObj) {
        return postWithSign(url, bodyObj, true,"");
    }

    /**
     * 发送带签名的POST请求，自动处理异常
     * @param url 请求地址
     * @param bodyObj 请求体对象
     * @param needAuth 是否需要认证token
     * @param paramStr 请求参数字符串，格式：key1=value1&key2=value2 或 key1#value1#key2#value2
     * @return JSONObject 响应内容
     */
    public JSONObject postWithSign(String url, Object bodyObj, boolean needAuth, String paramStr) {
        try {
            JSONObject bodyParamJson = JSONUtil.parseObj(bodyObj);
            String bodyJson = JSONUtil.toJsonStr(bodyParamJson);

            // 处理URL参数
            String finalUrl = buildUrlWithParams(url, paramStr);

            String timestamp = String.valueOf(System.currentTimeMillis());

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            headers.add("timestamp", timestamp);

            // 如果需要认证，添加Authorization header
            if (needAuth) {
                String token = getAccessToken();
                if (StringUtils.isNotEmpty(token)) {
                    headers.add("Authorization", "Bearer " + token);
                }
            }

            HttpEntity<String> requestEntity = new HttpEntity<>(bodyJson, headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    finalUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            String responseBody = responseEntity.getBody();
            JSONObject jsonObject = JSONUtil.parseObj(responseBody);
            String code = jsonObject.getStr("code");
            if ("200".equals(code)) {
                return jsonObject;
            } else {
                String msg = jsonObject.getStr("msg", "远程接口调用失败");
                logger.info("postWithSign,msg:{}",msg);
                throw exception(BASE_SERVER_ERROR, msg);
            }
        } catch (Exception e) {
            throw exception(BASE_SERVER_ERROR, "远程接口调用异常: " + e.getMessage());
        }
    }

    /**
     * 清除缓存的token
     */
    public void clearCachedToken() {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        String cacheKey = TOKEN_CACHE_PREFIX + clientId + "_" + userId;
        redisCache.deleteObject(cacheKey);
        logger.info("已清除缓存的访问令牌");
    }

    /**
     * 计算签名
     *
     * @param clientId 客户端ID
     * @param userId 用户ID
     * @param type 客户端类型
     * @param timestamp 时间戳
     * @param clientSecret 客户端密钥
     * @return 签名字符串
     */
    public static String calculateSignature(String clientId, Long userId, Integer type, Long timestamp, String clientSecret) {
        String signStr = clientId + userId + type + timestamp + clientSecret;
        return DigestUtil.md5Hex(signStr);
    }

    /**
     * 构建带参数的URL
     *
     * @param url 基础URL
     * @param paramStr 参数字符串，支持两种格式：
     *                 1. key1=value1&key2=value2 (标准URL参数格式)
     *                 2. key1#value1#key2#value2 (自定义格式)
     * @return 带参数的完整URL
     */
    private String buildUrlWithParams(String url, String paramStr) {
        if (StringUtils.isEmpty(paramStr)) {
            return url;
        }
        StringBuilder urlBuilder = new StringBuilder(url);

        // 检查URL是否已经包含参数
        if (url.contains("?")) {
            urlBuilder.append("&");
        } else {
            urlBuilder.append("?");
        }

        // 处理不同格式的参数字符串
        if (paramStr.contains("=") && paramStr.contains("&")) {
            // 标准URL参数格式：key1=value1&key2=value2
            urlBuilder.append(paramStr);
        } else if (paramStr.contains("#")) {
            // 自定义格式：key1#value1#key2#value2
            String[] parts = paramStr.split("#");
            for (int i = 0; i < parts.length - 1; i += 2) {
                if (i > 0) {
                    urlBuilder.append("&");
                }
                urlBuilder.append(parts[i]).append("=").append(parts[i + 1]);
            }
        } else {
            // 单个参数，假设格式为 key=value
            urlBuilder.append(paramStr);
        }

        return urlBuilder.toString();
    }

    /**
     * 将Map参数转换为参数字符串
     *
     * @param params 参数Map
     * @return 参数字符串，格式：key1=value1&key2=value2
     */
    private String mapToParamString(java.util.Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        StringBuilder paramBuilder = new StringBuilder();
        boolean first = true;

        for (java.util.Map.Entry<String, Object> entry : params.entrySet()) {
            if (!first) {
                paramBuilder.append("&");
            }
            paramBuilder.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }

        return paramBuilder.toString();
    }

    /**
     * 发送带签名的GET请求，自动处理异常
     * @param url 请求地址
     * @param requestParams 请求参数Map
     * @return JSONObject 响应内容
     */
    public JSONObject getWithSign(String url, java.util.Map<String, Object> requestParams) {
        return getWithSign(url, requestParams, true);
    }

    /**
     * 发送带签名的GET请求，自动处理异常
     * @param url 请求地址
     * @param requestParams 请求参数Map
     * @param needAuth 是否需要认证token
     * @return JSONObject 响应内容
     */
    public JSONObject getWithSign(String url, java.util.Map<String, Object> requestParams, boolean needAuth) {
        try {
            // 处理URL参数
            String paramStr = mapToParamString(requestParams);
            String finalUrl = buildUrlWithParams(url, paramStr);

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            // 如果需要认证，添加Authorization header
            if (needAuth) {
                String token = getAccessToken();
                if (StringUtils.isNotEmpty(token)) {
                    headers.add("Authorization", "Bearer " + token);
                }
            }

            HttpEntity<String> requestEntity = new HttpEntity<>(headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    finalUrl,
                    HttpMethod.GET,
                    requestEntity,
                    String.class
            );

            String responseBody = responseEntity.getBody();
            logger.info("getWithSign,responseBody:{}",responseBody);
            JSONObject jsonObject = JSONUtil.parseObj(responseBody);
            String code = jsonObject.getStr("code");
            if ("200".equals(code)) {
                return jsonObject;
            } else {
                String msg = jsonObject.getStr("msg", "远程接口调用失败");
                logger.info("getWithSign,msg:{}",msg);
                throw exception(BASE_SERVER_ERROR, msg);
            }
        } catch (Exception e) {
            throw exception(BASE_SERVER_ERROR, "远程接口调用异常: " + e.getMessage());
        }
    }
}