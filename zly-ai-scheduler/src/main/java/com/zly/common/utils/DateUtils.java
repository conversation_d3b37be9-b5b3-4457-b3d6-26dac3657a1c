package com.zly.common.utils;

import java.lang.management.ManagementFactory;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;


/**
 * 时间工具类
 * 
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
	public static String YYYY = "yyyy";

	public static final String YYSS = "yyyy-MM-dd HH:mm:ss";

	public static final String YM = "yyyyMM";

	public static String YYYY_MM = "yyyy-MM";

	public static String YYYY_MM_DD = "yyyy-MM-dd";

	public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

	public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

	public static final String YSSSS = "yyMMddHHmmssSSS";

	public static final String YYMMDD = "yyyy-MM-dd";

	private static String[] parsePatterns = { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd",
			"yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM" };

	/**
	 * 获取当前Date型日期
	 * 
	 * @return Date() 当前日期
	 */
	public static Date getNowDate() {
		return new Date();
	}

	@NotNull
	public static Date getDate(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	/**
	 * 获取当前日期, 默认格式为yyyy-MM-dd
	 * 
	 * @return String
	 */
	public static String getDate() {
		return dateTimeNow(YYYY_MM_DD);
	}

	public static final String getTime() {
		return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
	}

	public static final String dateTimeNow() {
		return dateTimeNow(YYYYMMDDHHMMSS);
	}

	public static final String dateTimeNow(final String format) {
		return parseDateToStr(format, new Date());
	}

	public static final String dateTime(final Date date) {
		return parseDateToStr(YYYY_MM_DD, date);
	}

	public static final String parseDateToStr(final String format, final Date date) {
		return new SimpleDateFormat(format).format(date);
	}

	public static final Date dateTime(final String format, final String ts) {
		try {
			return new SimpleDateFormat(format).parse(ts);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 日期路径 即年/月/日 如2018/08/08
	 */
	public static final String datePath() {
		Date now = new Date();
		return DateFormatUtils.format(now, "yyyy/MM/dd");
	}

	/**
	 * 日期路径 即年/月/日 如20180808
	 */
	public static final String dateTime() {
		Date now = new Date();
		return DateFormatUtils.format(now, "yyyyMMdd");
	}

	/**
	 * 日期型字符串转化为日期 格式
	 */
	public static Date parseDate(Object str) {
		if (str == null) {
			return null;
		}
		try {
			return parseDate(str.toString(), parsePatterns);
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * 获取服务器启动时间
	 */
	public static Date getServerStartDate() {
		long time = ManagementFactory.getRuntimeMXBean().getStartTime();
		return new Date(time);
	}

	/**
	 * 计算相差天数
	 */
	public static int differentDaysByMillisecond(Date date1, Date date2) {
		return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
	}

	/**
	 * 计算时间差
	 *
	 * @param endDate
	 *            最后时间
	 * @param startTime
	 *            开始时间
	 * @return 时间差（天/小时/分钟）
	 */
	public static String timeDistance(Date endDate, Date startTime) {
		long nd = 1000 * 24 * 60 * 60;
		long nh = 1000 * 60 * 60;
		long nm = 1000 * 60;
		// long ns = 1000;
		// 获得两个时间的毫秒时间差异
		long diff = endDate.getTime() - startTime.getTime();
		// 计算差多少天
		long day = diff / nd;
		// 计算差多少小时
		long hour = diff % nd / nh;
		// 计算差多少分钟
		long min = diff % nd % nh / nm;
		// 计算差多少秒//输出结果
		// long sec = diff % nd % nh % nm / ns;
		return day + "天" + hour + "小时" + min + "分钟";
	}

	/**
	 * 增加 LocalDateTime ==> Date
	 */
	public static Date toDate(LocalDateTime temporalAccessor) {
		ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
		return Date.from(zdt.toInstant());
	}

	/**
	 * 增加 LocalDate ==> Date
	 */
	public static Date toDate(LocalDate temporalAccessor) {
		LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
		ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
		return Date.from(zdt.toInstant());
	}


	/**
	 * 获取当前月份，例 "1970年1月"返回 "197001"
	 *
	 * @return
	 */
	public static String getCurrentMonthString() {
		return dateToStr(new Date(), YM);
	}


	/**
	 * 将日期类型转化为字符串，默认格式yyyy-MM-dd HH:mm:ss
	 *
	 * @param date
	 *            Date 日期
	 * @return string 日期字符串
	 */
	public static String dateToStr(Date date) {
		return dateToStr(date, YYSS);
	}

	/**
	 * 将日期类型转化为字符串,根据传入的格式输出日期（如YYYY年MM月DD日等）
	 *
	 * @param date
	 *            日期
	 * @param sFormat
	 *            日期格式
	 * @return string 日期字符串
	 */
	public static String dateToStr(Date date, String sFormat) {
		if (date == null) {
			return null;
		}
		SimpleDateFormat format = new SimpleDateFormat(sFormat);
		return format.format(date);
	}


	/**
	 * 字符串转化为日期类型，默认格式yyyy-MM-dd HH:mm:ss，
	 * 备用格式 "yyyy/MM/dd HH:mm:ss";
	 *
	 * @param strDate 字符串
	 * @return 日期
	 */
	public static Date strToDate(String strDate) {
		return strToDate(strDate, YYSS);
	}


	/**
	 * 字符串转化成指定格式的日期类型(如yyyy年MM月DD日->date)
	 *
	 * @param strDate 字符串
	 * @param sFormat 字符串
	 * @return 返回日期
	 */
	public static Date strToDate(String strDate, String sFormat) {
		if (StringUtils.isBlank(strDate)) {
			return null;
		}
		SimpleDateFormat format = new SimpleDateFormat(sFormat);
		format.setLenient(false);
		try {
			return format.parse(strDate);
		} catch (ParseException ex) {
			return null;
		}
	}


	/**
	 * 日期中本月月末的日期
	 *
	 * @param date
	 *            日期
	 * @return 月末时间
	 */
	public static Date getTheLastMomentOfMonth(Date date) {
		// 取 本月 月末
		if (date == null) {
			return null;
		}
		int year = Integer.parseInt(asShortString(date).substring(0, 4));
		int month = Integer.parseInt(asShortString(date).substring(5, 7));
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.YEAR, year);
		calendar.set(Calendar.MONTH, month);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.add(Calendar.SECOND, -1);

		return calendar.getTime();
	}


	/**
	 * 将data对象转换成"yyyy-MM-dd"格式的字符串
	 *
	 * @param date
	 *            Date 日期
	 * @return String yyyy-MM-dd"格式的字符串
	 */
	public static String asShortString(Date date) {
		DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		return formatter.format(date);
	}

	/**
	 * 计算两个日期月份跨度，如2021年4月1日 ~ 2021年6月1日，跨度3个月
	 *
	 * @param dateStart
	 *            开始日期
	 * @param dateEnd
	 *            结束日期
	 * @return int 相差月数
	 */
	public static int getMonthCountCovered(Date dateStart, Date dateEnd) {
		Calendar calendarStart = Calendar.getInstance();
		calendarStart.setTime(dateStart);
		int yearStart = calendarStart.get(Calendar.YEAR);
		int monthStart = calendarStart.get(Calendar.MONTH);

		Calendar calendarEnd = Calendar.getInstance();
		calendarEnd.setTime(dateEnd);
		int yearEnd = calendarEnd.get(Calendar.YEAR);
		int monthEnd = calendarEnd.get(Calendar.MONTH);

		return (yearEnd - yearStart) * 12 + (monthEnd - monthStart) + 1;
	}

	/**
	 * 判断string时间，转换类型，并且精确到秒
	 *
	 * @param dateStr
	 * @return
	 */
	public static Date parseDate(String dateStr) {
		if (StringUtils.isEmpty(dateStr)) {
			return null;
		}
		String[] split = dateStr.split(":");
		if (split.length == 2) {
			dateStr = dateStr + ":00";
		} else if (split.length == 1) {
			dateStr = dateStr + ":00:00";
		}
		return parseDate(dateStr, YYSS);
	}


	/**
	 * 根据指定日期格式将给出的日期字符串dateStr转换成一个日期对象
	 *
	 * @param dateStr
	 *            String 日期字符串
	 * @param pattern
	 *            String 日期格式
	 * @return Date 日期
	 */
	public static Date parseDate(String dateStr, String pattern) {
		if (dateStr == null || dateStr.length() == 0 || pattern == null || pattern.length() == 0) {
			return null;
		}
		DateFormat fmt = new SimpleDateFormat(pattern);
		Date result = null;
		try {
			result = fmt.parse(dateStr);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return result;
	}

}
