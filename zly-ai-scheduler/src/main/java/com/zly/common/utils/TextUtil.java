package com.zly.common.utils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

/**
 * @Title TextUtil.java
 * @Description 文本工具类
 * @Create DateTime: 2018年8月15日 下午1:51:34
 * 
 * <AUTHOR>
 * 
 */
@SuppressWarnings("unused")
public class TextUtil {

	private static final int maxvaluefive = 999999999;
	private static final int minvaluefive = 1;
	private static final Map<String, AtomicInteger> map = new ConcurrentHashMap<String, AtomicInteger>();

	/**
	 * 按coverPad分组生成数字序列号,coverPad几位长度，超过最大位数后最小值1开始（系统重启后会重新计算，做辅助序列，不可唯一性）
	 * 
	 * @param coverPad
	 * @return
	 */
	public static String getSequence(String group, int coverPad) {
		for (;;) {
			// 判断日期
			AtomicInteger mapAtomic = map.computeIfAbsent(group + coverPad, key -> new AtomicInteger(minvaluefive));
			int current = mapAtomic.get();
			int maxPad = Integer.parseInt(StringUtils.rightPad(String.valueOf(1), coverPad, "0")) * 10 - 1;
			int newValue = (current >= maxvaluefive || current >= maxPad) ? minvaluefive : current + 1;
			if (mapAtomic.compareAndSet(current, newValue)) {
				return StringUtils.leftPad(String.valueOf(current), coverPad, "0");
			}
		}
	}

	/**
	 * 生成ID，包装getTimeSequenceID(5)
	 *
	 * @return
	 */
	public static Long generateId() {
		return getTimeSequenceID(5);
	}

	/**
	 * 生成ID,时间戳毫秒13+序列（可唯一性,但是要保证并发在一毫秒内不会达到最大长度）
	 * 
	 * @return
	 */
	public static Long getTimeSequenceID(int coverPad) {
		return Long.valueOf(Instant.now().toEpochMilli() + getSequence("getTimeSequenceID_", coverPad));
	}

	/**
	 * 生成ID,时间戳秒10+序列（可唯一性,但是要保证并发在一秒内不会达到最大长度）
	 * 
	 * @return
	 */
	public static String getSecondTimeSequenceID(int coverPad) {
		return Instant.now().getEpochSecond() + getSequence("getSecondTimeSequenceID_", coverPad);
	}

	/**
	 * 生成ID,日期格式+序列（可唯一性，但是要保证并发在格式时间内不会达到最大长度）
	 **/
	public static String getDateSeqeunceID(String format, int coverPad) {
		return DateUtils.dateTimeNow(format) + getSequence("getDateSequenceID_", coverPad);
	}

	/**
	 * 生成ID,日期格式+序列（可唯一性，但是要保证并发在格式时间内不会达到最大长度）
	 **/
	public static String getDateSeqeunceID(int coverPad) {
		return DateUtils.dateTimeNow(DateUtils.YSSSS) + getSequence("getDateSequenceID_", coverPad);
	}

	/**
	 * 生成ID，不可唯一性，传入值要搭配其他辅助唯一值，比如时间戳，uuid等。
	 **/
	public static String getStrSequenceID(int coverPad, String... str) {
		// （生成规则 14位时间YYYYMMDDHHMMSS + 5位序列）
		StringBuffer sb = new StringBuffer();
		for (String s : str) {
			sb.append(s);
		}
		return sb + getSequence("getStrSequenceID_", coverPad);
	}

	/**
	 * 生成uuid <br/>
	 * 去掉"-"符号
	 * 
	 * @return
	 */
	public static String getUUID() {

		String str = getUUIDNoCut();
		// 去掉"-"符号
		return str.replaceAll("-", "");
	}

	/**
	 * 生成uuid <br/>
	 * 没有去掉"-"符号
	 * 
	 * @return
	 */
	public static String getUUIDNoCut() {

		UUID uuid = UUID.randomUUID();
		return uuid.toString();
	}

	/**
	 * 是null返回""
	 * 
	 * @param input
	 * @return
	 */
	public static String transNull(String input) {

		return input == null ? "" : input;
	}

	/**
	 * 判断数字是否不为０
	 * 
	 * @param iint
	 *            判断的字符串
	 * @return boolean :返回值为boolean
	 */
	public static boolean isNull(Integer iint) {

		return iint == null;
	}

	/**
	 * 判断对象是否为null
	 * 
	 * @param obj
	 *            判断的字符串
	 * @return boolean :返回值为boolean
	 */

	public static boolean isNull(Object obj) {
		return null == obj;
	}

	/**
	 * 判断字符串是否不为空
	 * 
	 * @param sstring
	 *            判断的字符串
	 * @return boolean :返回值为boolean
	 */
	public static boolean isNull(final String sstring) {
		return null == sstring || "null".equalsIgnoreCase(sstring.trim()) || "".equals(sstring.trim());
	}

	/**
	 * 判断列表是否为空
	 * 
	 * @param lst
	 *            判断的字符串
	 * @return boolean :返回值为boolean
	 */

	public static boolean isNull(List<?> lst) {
		boolean bolret = lst == null;
		if (lst != null) {
			if (lst.size() == 0)
				bolret = true;
		}
		return bolret;
	}

	/**
	 * 判断字符串是否不为空
	 * 
	 * @param sstring
	 *            判断的字符串
	 * @return boolean :返回值为boolean
	 */
	public static boolean isNotNull(final String sstring) {
		return !isNull(sstring);
	}

	/**
	 * 判断数字是否不为０
	 * 
	 * @param iint
	 *            判断的字符串
	 * @return boolean :返回值为boolean
	 */
	public static boolean isNotNull(int iint) {
		return iint > 0;
	}

	/**
	 * 判断整数（int）
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isInteger(String str) {
		if (null == str || "".equals(str)) {
			return false;
		}
		Pattern pattern = Pattern.compile("^[-+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}

	/**
	 * 判断浮点数（double和float）
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isDouble(String str) {
		if (null == str || "".equals(str)) {
			return false;
		}
		Pattern pattern = Pattern.compile("^[-+]?[.\\d]*$");
		return pattern.matcher(str).matches();
	}

	/**
	 * 是否是数字或小数
	 * 
	 * @tags @return
	 * @exception <AUTHOR>                wanghc
	 * @date 2015-9-16 下午5:50:15
	 * @return boolean
	 */
	public static boolean isNumber(String str) {
		if (StringUtils.isBlank(str)) {
			return false;
		}
		String reg = "\\d+(\\.\\d+)?";
		return str.matches(reg);
	}

	/**
	 * 是否大于0
	 * 
	 * @tags @return
	 * @exception <AUTHOR>                wanghc
	 * @date 2015-9-16 下午5:50:15
	 * @return boolean
	 */
	public static boolean isGtZero(String str) {
		return isNumber(str) && new BigDecimal(str).compareTo(BigDecimal.ZERO) > 0;
	}

	/**
	 * 判断数组中是否含有某个字段
	 * 
	 * @param arr
	 * @param value
	 * @return
	 */
	public static boolean contains(String[] arr, String value) {
		return Arrays.asList(arr).contains(value);
	}

	/**
	 * 用户身份证号码的打码隐藏加星号加*
	 * <p>
	 * 18位和非18位身份证处理均可成功处理
	 * </p>
	 * <p>
	 * 参数异常直接返回null
	 * </p>
	 *
	 * @param idCardNum
	 *            身份证号码
	 * @param front
	 *            需要显示前几位
	 * @param end
	 *            需要显示末几位
	 * @return 处理完成的身份证
	 */
	public static String idMask(String idCardNum, int front, int end) {
		// 身份证不能为空
		if (StringUtils.isBlank(idCardNum)) {
			return "*";
		}
		// 需要截取的长度不能大于身份证号长度
		if ((front + end) > idCardNum.length()) {
			front = idCardNum.length() / 2 - 1;
			end = idCardNum.length() / 2 - 1;
		}
		// 需要截取的不能小于0
		if (front < 0 || end < 0) {
			return "*";
		}
		// 计算*的数量
		int asteriskCount = idCardNum.length() - (front + end);
		StringBuffer asteriskStr = new StringBuffer();
		for (int i = 0; i < asteriskCount; i++) {
			asteriskStr.append("*");
		}
		String regex = "(\\w*" + front + "})(\\w+)(\\w*" + end + "})";
		return idCardNum.replaceAll(regex, "$1" + asteriskStr + "$3");
	}

	/**
	 * 对字符加星号处理：除前面几位和后面几位外，其他的字符以星号代替
	 *
	 * @param content
	 *            传入的字符串
	 * @param frontNum
	 *            保留前面字符的位数
	 * @param endNum
	 *            保留后面字符的位数
	 * @return 带星号的字符串
	 */
	public static String getStarString2(String content, int frontNum, int endNum) {
		if (frontNum >= content.length() || frontNum < 0) {
			return content;
		}
		if (endNum >= content.length() || endNum < 0) {
			return content;
		}
		if (frontNum + endNum >= content.length()) {
			return content;
		}
		StringBuffer starStr = new StringBuffer();
		for (int i = 0; i < (content.length() - frontNum - endNum); i++) {
			starStr.append("*");
		}
		return content.substring(0, frontNum) + starStr + content.substring(content.length() - endNum);
	}

	public static void main(String[] args) {
		System.out.println(getStarString2("钟珂", 1, 0));
	}

}
