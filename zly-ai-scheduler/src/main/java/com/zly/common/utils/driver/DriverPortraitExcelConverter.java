package com.zly.common.utils.driver;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.zly.project.driver.controller.vo.DriverProfileRespVO;
import com.zly.project.driver.controller.vo.VehicleGpsInfoVO;
import com.zly.project.driver.controller.vo.VehicleInfoVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class DriverPortraitExcelConverter {
    public static final String OUTPUT_FILE = "/Users/<USER>/Downloads/driver_profiles.xlsx";

    /**
     * 将司机画像数据转换为Excel格式并保存到文件
     */
    public static void convertToExcelFile(List<DriverProfileRespVO> portraits) {
        List<DriverExcelData> excelDataList = convertToExcelData(portraits);
        writeToExcel(excelDataList);
    }

    /**
     * 将司机数据转换为Excel数据对象
     */
    public static List<DriverExcelData> convertToExcelData(List<DriverProfileRespVO> portraits) {
        if (portraits == null || portraits.isEmpty()) {
            return Collections.emptyList();
        }
        List<DriverExcelData> excelDataList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (DriverProfileRespVO driver : portraits) {
            DriverExcelData excelData = new DriverExcelData();
            // 基本信息
            excelData.setId(String.valueOf(driver.getId()));
            excelData.setDriverId(driver.getDriverId());
            // 对姓名、手机号和身份证进行脱敏处理
            excelData.setDriverName(maskName(driver.getDriverName()));
            excelData.setTelephone(maskPhone(driver.getTelephone()));
            excelData.setIdentityCard(maskIdCard(driver.getIdentityCard()));
//            excelData.setDrivingYears(driver.getDrivingYears());
//            excelData.setProfileCompleteness(driver.getProfileCompleteness() + "%");
//            excelData.setServiceCount(driver.getServiceCount());
//            excelData.setTotalCourse(driver.getTotalCourse());
            excelData.setDriverSource(driver.getOperationDesc());
//            excelData.setDriverLicenseStatus(getLicenseValidDesc(driver.getIsTheDrivrLicenseValid()));
//            excelData.setQualificationStatus(getLicenseValidDesc(driver.getIsTheQualificationValid()));
//            excelData.setLastServiceTime(driver.getLastServiceTime() != null ? sdf.format(driver.getLastServiceTime()) : "暂无");
            excelData.setDriverStatus(getDriverStateDesc(driver.getState()));
            // 证件信息
//            excelData.setDriverLicenseType(driver.getVehicleClass());
//            excelData.setDriverLicenseExpiry(driver.getValidPeriodTo());
//            excelData.setQualificationCertificate(driver.getQualificationCertificate());
//            excelData.setQualificationExpiry(driver.getActualCarrierEnd());
            // 车辆信息
            List<VehicleInfoVO> vehicles = convertToVehicleInfoList(driver.getVehicles());
            if (!vehicles.isEmpty()) {
                List<String> vehicleInfos = new ArrayList<>();
                for (VehicleInfoVO vehicle : vehicles) {
                    StringBuilder vehicleInfo = new StringBuilder();
                    // 基础车辆信息
                    vehicleInfo.append(String.format("车辆信息##车牌号:%s(车辆类型:%s, 车辆尺寸:%d×%d×%d, 载重:%s吨,能源类型: %s,车辆状态: %s)",
                            vehicle.getVehicleNumber(),
                            vehicle.getVehicleTypeName(),
                            vehicle.getVehicleLength(),
                            vehicle.getVehicleWidth(),
                            vehicle.getVehicleHeight(),
                            vehicle.getVehicleTonnage(),
                            getEnergyTypeDesc(vehicle.getVehicleEnergyType()),
                            getVehicleStateDesc(vehicle.getState())));
                    // GPS信息
//                    VehicleGpsInfoVO gpsInfo = vehicle.getGpsInfo();
//                    if (gpsInfo != null) {
//                        vehicleInfo.append(String.format(" [GPS信息: 当前位置:%s, 经纬度:[%s,%s], 行政区域:%s%s%s, 更新时间:%s]",
//                                gpsInfo.getAddress(),
//                                gpsInfo.getLongitude(),
//                                gpsInfo.getLatitude(),
//                                StringUtils.defaultString(gpsInfo.getProvince(), ""),
//                                StringUtils.defaultString(gpsInfo.getCity(), ""),
//                                StringUtils.defaultString(gpsInfo.getCountry(), ""),
//                                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(gpsInfo.getUpdateTime())));
//                    } else {
//                        vehicleInfo.append(" [GPS信息: 暂无]");
//                    }
                    vehicleInfos.add(vehicleInfo.toString());
                }
                // 使用分号分隔多条车辆信息
                excelData.setVehicleNumber(String.join("-----------", vehicleInfos));
            }
            // 常跑路线信息
            List<Map<String, Object>> routes = convertToRoutesList(driver.getLongDistanceRoutes());
            if (!routes.isEmpty()) {
                List<String> routeInfos = new ArrayList<>();
                for (Map<String, Object> route : routes) {
                    Object lastOrderTime = route.get("lastOrderTime");
                    String format = null;
                    if (lastOrderTime != null){
                        format = sdf.format(route.get("lastOrderTime"));
                    }
                    String routeInfo = String.format("常跑路线:%s(单数:%s, 平均里程:%skm, 平均运费:%s元, 最近订单时间:%s)", route.get("routeName"), route.get("routeCount"),
                            route.get("averageMileage"),route.get("averageFare"), StringUtils.isNotBlank(format) ? format : "无");
                    routeInfos.add(routeInfo);
                }
                // 使用分号分隔多条路线信息
                excelData.setMainRoute(String.join("-----------", routeInfos));
            }
            // 评价信息
//            excelData.setExperienceEvaluation(generateExperienceEvaluation(driver));
//            excelData.setServiceQuality(generateServiceQuality(driver));
            excelDataList.add(excelData);
        }
        return excelDataList;
    }

    /**
     * 写入Excel文件
     */
    private static void writeToExcel(List<DriverExcelData> dataList) {
        try {
            // 设置Excel样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short) 12);
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(
                    headWriteCellStyle, contentWriteCellStyle);
            // 写入Excel
            EasyExcel.write(OUTPUT_FILE, DriverExcelData.class)
                    .registerWriteHandler(styleStrategy)
                    .sheet("司机画像数据")
                    .doWrite(dataList);
            log.info("Excel文件已生成: {}", OUTPUT_FILE);
        } catch (Exception e) {
            log.error("生成Excel文件失败", e);
            throw new RuntimeException("生成Excel文件失败: " + e.getMessage());
        }
    }

    @Data
    public static class DriverExcelData {
        // 基本信息
        @ExcelProperty("画像ID")
        private String id;
        @ExcelProperty("司机ID")
        private String driverId;
        @ExcelProperty("姓名")
        private String driverName;
        @ExcelProperty("手机号")
        private String telephone;
        @ExcelProperty("身份证")
        private String identityCard;
//        @ExcelProperty("驾龄")
//        private Integer drivingYears;
//        @ExcelProperty("资料完善度")
//        private String profileCompleteness;
//        @ExcelProperty("服务次数")
//        private Integer serviceCount;
//        @ExcelProperty("总里程")
//        private String totalCourse;
        @ExcelProperty("司机来源")
        private String driverSource;
//        @ExcelProperty("驾驶证状态")
//        private String driverLicenseStatus;
//        @ExcelProperty("资格证状态")
//        private String qualificationStatus;
//        @ExcelProperty("最近服务时间")
//        private String lastServiceTime;
        @ExcelProperty("司机状态")
        private String driverStatus;

        // 证件信息
//        @ExcelProperty("驾驶证类型")
//        private String driverLicenseType;
//        @ExcelProperty("驾驶证有效期")
//        private String driverLicenseExpiry;
//        @ExcelProperty("从业资格证")
//        private String qualificationCertificate;
//        @ExcelProperty("资格证有效期")
//        private String qualificationExpiry;

        // 车辆信息（合并后只需要一个字段）
        @ExcelProperty("车辆信息")
        private String vehicleNumber;
        // 路线信息（合并后只需要一个字段）
        @ExcelProperty("常跑路线信息")
        private String mainRoute;

        // 评价信息
//        @ExcelProperty("经验评价")
//        private String experienceEvaluation;
//        @ExcelProperty("服务质量")
//        private String serviceQuality;
    }

    // 使用原有的工具方法
    private static String getOperationDesc(Integer operation) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.getOperationDesc(operation);
    }

    private static String getLicenseValidDesc(Integer status) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.getLicenseValidDesc(status);
    }

    private static String getDriverStateDesc(Integer state) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.getDriverStateDesc(state);
    }

    private static String getEnergyTypeDesc(Long type) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.getEnergyTypeDesc(type);
    }

    private static String getVehicleStateDesc(Integer state) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.getVehicleStateDesc(state);
    }

    private static List<Map<String, Object>> convertToRoutesList(String routesJson) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.convertToRoutesList(routesJson);
    }

    private static List<VehicleInfoVO> convertToVehicleInfoList(String vehicles) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.convertToVehicleInfoList(vehicles);
    }

    private static String generateExperienceEvaluation(DriverProfileRespVO driver) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.generateExperienceEvaluation(driver);
    }

    private static String generateServiceQuality(DriverProfileRespVO driver) {
        // 复用原有方法
        return DriverPortraitMarkdownConverter.generateServiceQuality(driver);
    }
    

    /**
     * 对姓名进行脱敏处理
     * 规则：保留姓氏，其他用*代替，如：张三 -> 张*，李四 -> 李*
     */
    private static String maskName(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        if (name.length() == 1) {
            return name;
        }
        // 中文姓名：保留姓氏，其余用*替代
        StringBuilder masked = new StringBuilder();
        masked.append(name.charAt(0));
        for (int i = 0; i < name.length() - 1; i++) {
            masked.append("*");
        }
        return masked.toString();
    }
    
    /**
     * 对手机号进行脱敏处理
     * 规则：保留前3位和后4位，中间用*代替，如：13812345678 -> 138****5678
     */
    private static String maskPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return "";
        }
        if (phone.length() <= 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
    
    /**
     * 对身份证号进行脱敏处理
     * 规则：保留前6位和后4位，中间用*代替，如：110101199001011234 -> 110101********1234
     */
    private static String maskIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return "";
        }
        if (idCard.length() <= 10) {
            return idCard;
        }
        return idCard.substring(0, 6) + "********" + idCard.substring(idCard.length() - 4);
    }
}