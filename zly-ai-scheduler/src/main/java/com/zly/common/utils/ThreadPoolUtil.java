package com.zly.common.utils;


import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import org.jetbrains.annotations.NotNull;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ThreadPoolUtil extends ThreadPoolExecutor {

    /**
     * 核心线程数
     */
	private static final int SIZE_CORE_POOL = 32;
    /**
     * 最大线程数
     */
	private static final int SIZE_MAX_POOL = 64;
    /**
     * 存活时间
     */
	private static final long ALIVE_TIME = 20000;

    /**
     * 存放任务的队列
     */
	private static BlockingQueue<Runnable> bqueue = new ArrayBlockingQueue<>(300000);

    /**
     * The default rejected execution handler
     */
    private static final RejectedExecutionHandler defaultHandler =
            new ThreadPoolExecutor.AbortPolicy();

    private static ThreadPoolExecutor pool =
            new ThreadPoolExecutor(SIZE_CORE_POOL,
                    SIZE_MAX_POOL,
                    ALIVE_TIME,
                    TimeUnit.MILLISECONDS,
                    bqueue,
                    ThreadPoolUtil.myDefaultThreadFactory(),
                    defaultHandler);

    static {
        pool.prestartAllCoreThreads();
    }

	public ThreadPoolUtil(int corePoolSize, int maximumPoolSize, long keepAliveTime, @NotNull TimeUnit unit, @NotNull BlockingQueue<Runnable> workQueue) {
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
	}

    public static ThreadPoolExecutor getPool() {
        return pool;
    }

    public static ThreadFactory myDefaultThreadFactory() {
        return new MyDefaultThreadFactory();
    }

    static class MyDefaultThreadFactory implements ThreadFactory {
        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        MyDefaultThreadFactory() {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() :
                    Thread.currentThread().getThreadGroup();
            namePrefix = "dcss-account" +
                    poolNumber.getAndIncrement() +
                    "-thread-";
        }

        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r,
                    namePrefix + threadNumber.getAndIncrement(),
                    0);
            if (t.isDaemon())
                t.setDaemon(false);
            if (t.getPriority() != Thread.NORM_PRIORITY)
                t.setPriority(Thread.NORM_PRIORITY);
            return t;
        }

    }

	@Override
	public void afterExecute(Runnable r, Throwable t) {
		if (t != null) {
			log.error("打印迁移异常日志：" + t);
		}
	}


}