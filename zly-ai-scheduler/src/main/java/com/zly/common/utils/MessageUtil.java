package com.zly.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.constant.HttpStatus;
import com.zly.common.core.MsgModel;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.domain.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class MessageUtil {

	@Value("${custom.iphone.url}")
	private String NEW_MSG_URL;

	@Value("${custom.iphone.acc}")
	private String NEW_MSG_ACC;

	@Value("${custom.iphone.pas}")
	private String NEW_MSG_PAS;

	@Resource
	private RedisCache redisCache;

	/**
	 * 发送短信验证码
	 *
	 * @param sdst 手机号码
	 * @param smsg 短信内容
	 * @return
	 */
	public JSONObject sendSms(String sdst, String smsg, String taxNo) {
		try {
			MsgModel model = new MsgModel();
			model.setPhone(sdst);
			model.setContent(smsg);
			model.setTaxNo(taxNo);
			String json = JSON.toJSONString(model);
			Map<String, String> map = new HashMap<>();
			map.put("acc", NEW_MSG_ACC);
			map.put("pas", NEW_MSG_PAS);
			String msgInfo = HttpClientUtils.postHttp(NEW_MSG_URL, json, map);
			if (StringUtils.isNotBlank(msgInfo)) {
				return JSON.parseObject(msgInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return null;
	}

	/**
	 * 发送验证码 生成验证码
	 *
	 * @param telephone
	 * @return
	 */
	public AjaxResult sendMessage(String telephone, String taxNo) {
		Integer vericyCode;
		String smsg = "";
		try {
			if (StringUtils.isEmpty(telephone)) {
				return AjaxResult.error("手机号不能为空");
			}
			vericyCode = redisCache.getCacheObject(telephone);
			if (vericyCode != null) {
				long expireTime = redisCache.getExpire(telephone);
				if (expireTime > 540) {
					return AjaxResult.error("无法重复发送，请稍后再试");
				}
			} else {
				vericyCode = (int) ((Math.random() * 9 + 1) * 100000);
			}
			// 如果一分钟到了，重新发送，并且刷新时间。
			smsg = "验证码: " + vericyCode + "。10分钟内登录有效。如非本人操作，请及时联系客服";
			JSONObject jsonObject = sendSms(telephone, smsg, taxNo);
			String code = (String) jsonObject.get("code");
			String msg = (String) jsonObject.get("message");
			if ("1".equals(code) && "发送成功".equals(msg)) {
				redisCache.setCacheObject(telephone, vericyCode, 600, TimeUnit.SECONDS);
				return AjaxResult.success(HttpStatus.SUCCESS, "发送成功");
			}
			return AjaxResult.success(HttpStatus.BAD_REQUEST, "发送失败");
		} catch (Exception e) {
			return AjaxResult.success(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}


	/**
	 * 发送短信--通用
	 *
	 * @param telephone
	 * @return
	 */
	public AjaxResult sendNormalMessage(String telephone, String msg, String taxNo) {
		try {
			if (StringUtils.isEmpty(telephone)) {
				return AjaxResult.error("手机号不能为空");
			}
			if (StringUtils.isEmpty(msg)) {
				return AjaxResult.error("信息内容不能为空");
			}
			// telephone = "13584395408";
			JSONObject jsonObject = sendSms(telephone, msg, taxNo);
			String code = (String) jsonObject.get("code");
			String resMsg = (String) jsonObject.get("message");
			if ("1".equals(code) && "发送成功".equals(resMsg)) {
				return AjaxResult.success(HttpStatus.SUCCESS, "发送成功");
			}
			log.info("短信发送结果" + jsonObject.toJSONString());
			return AjaxResult.success(HttpStatus.BAD_REQUEST, "发送失败");
		} catch (Exception e) {
			return AjaxResult.success(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}

	/**
	 * 发送验证码 生成验证码
	 *
	 * @param telephone
	 * @param redisKey
	 * @return
	 */
	public AjaxResult sendVerificationCode(String telephone, String redisKey, String taxNo) {
		int verificationCode;
		String smsg = "";
		try {
			if (StringUtils.isEmpty(telephone)) {
				return AjaxResult.error("手机号不能为空");
			}
			Integer cacheObject = redisCache.getCacheObject(redisKey);
			if (cacheObject != null) {
				return AjaxResult.error("无法重复发送");
			}

			verificationCode = (int) ((Math.random() * 9 + 1) * 100000);
			smsg = "验证码: " + verificationCode + "。10分钟内登录有效。如非本人操作，请及时联系客服";
			redisCache.setCacheObject(redisKey, verificationCode, 600, TimeUnit.SECONDS);

			JSONObject jsonObject = sendSms(telephone, smsg, taxNo);
			String code = (String) jsonObject.get("code");
			String msg = (String) jsonObject.get("message");
			if ("1".equals(code) && "发送成功".equals(msg)) {
				return AjaxResult.success(HttpStatus.SUCCESS, "发送成功");
			}
			return AjaxResult.success(HttpStatus.BAD_REQUEST, "发送失败");
		} catch (Exception e) {
			return AjaxResult.success(HttpStatus.BAD_REQUEST, e.getMessage());
		}
	}
}
