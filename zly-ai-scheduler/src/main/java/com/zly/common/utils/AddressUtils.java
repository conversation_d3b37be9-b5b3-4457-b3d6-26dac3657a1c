package com.zly.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Title AddressUtils.java
 * @Description 地址工具类
 * @Create 2019-05-16 10:24
 * 
 * <AUTHOR>
 * 
 */
public class AddressUtils {

	/**
	 * 省份简称集合
	 */
	public static List<String> provinces = new ArrayList<String>();
	static {
		provinces.add("北京");
		provinces.add("上海");
		provinces.add("天津");
		provinces.add("重庆");
		provinces.add("河北");
		provinces.add("山西");
		provinces.add("吉林");
		provinces.add("辽宁");
		provinces.add("黑龙江");
		provinces.add("陕西");
		provinces.add("甘肃");
		provinces.add("青海");
		provinces.add("山东");
		provinces.add("福建");
		provinces.add("浙江");
		provinces.add("台湾");
		provinces.add("河南");
		provinces.add("湖北");
		provinces.add("湖南");
		provinces.add("江西");
		provinces.add("江苏");
		provinces.add("安徽");
		provinces.add("广东");
		provinces.add("海南");
		provinces.add("四川");
		provinces.add("贵州");
		provinces.add("云南");
		provinces.add("内蒙古");
		provinces.add("新疆");
		provinces.add("宁夏");
		provinces.add("广西");
		provinces.add("西藏");
		provinces.add("香港");
		provinces.add("澳门");
	}

	/**
	 * 直辖市
	 */
	public static List<String> municipalities = new ArrayList<String>();
	static {
		municipalities.add("北京");
		municipalities.add("上海");
		municipalities.add("天津");
		municipalities.add("重庆");
	}

	/**
	 * 根据省份名称获取省份简称
	 * 
	 * @param provinceName
	 *            省份名称
	 * @return
	 */
	public static String getSimpleProvinceName(String provinceName) {
		String province = "";
		if (StringUtils.isNotBlank(provinceName)) {
			for (String p : provinces) {
				if (provinceName.startsWith(p)) {
					province = p;
					break;
				}
			}
		}

		return province;
	}

	/**
	 * 根据城市名称获取城市简称
	 * 
	 * @param name
	 * @return
	 */
	public static String getSimpleCityName(String name) {
		if (StringUtils.isNotBlank(name)) {
			if (name.contains("自治州")) {
				name = name.replace("自治州", "");
			}
			if (name.contains("自治县")) {
				name = name.replace("自治县", "");
			}
			if (name.contains("地区")) {
				name = name.replace("地区", "");
			}
			if (name.contains("市")) {
				name = name.replace("市", "");
			}
			if (name.contains("县")) {
				name = name.replace("县", "");
			}
		}
		return name;
	}

	/**
	 * 是否直辖市
	 * 
	 * @param name
	 * @return true 是，false 否
	 */
	public static Boolean isMunicipality(String name) {
		String province = getSimpleProvinceName(name);
		return municipalities.contains(province);
	}

	/**
	 * 获取非直辖市省份名称<br/>
	 * 判断是否直辖市，如果是直辖市，省份返回空字符串，否则返回原名
	 * 
	 * @param provinceName
	 * @return
	 */
	public static String getNoneMunicipalityProvince(String provinceName) {
		String province = "";
		if (StringUtils.isNotBlank(provinceName)) {
			province = AddressUtils.getSimpleProvinceName(provinceName);
		}
		return AddressUtils.isMunicipality(province) ? "" : province;
	}

	/**
	 * 获取非直辖县级行政区划城市名称<br/>
	 * 判断是否省、自治区直辖县级行政区划，如果是，城市返回空字符串，否则返回原名
	 *
	 * @param cityName
	 * @return
	 */
	public static String getCityNoneUpDirectCounty(String cityName) {
		return Arrays.asList("省直辖行政单位","省直辖县级行政区划", "自治区直辖县级行政区划").contains(StringUtils.deleteWhitespace(cityName)) ? "" : cityName;
	}

	/**
	 * 拼接省、市、区县，【直辖市】去重，去除地级市级别中的【省、自治区直辖县级行政区划】
	 * 
	 * @param province
	 *            省份名称
	 * @param city
	 *            地级市名称
	 * @param district
	 *            区县名称
	 * @return
	 */
	public static String concatPcd(String province, String city, String district) {
		return getNoneMunicipalityProvince(province) + getCityNoneUpDirectCounty(city) + district;
	}


	/**
	 * 拼接省、市、区县，【直辖市】去重，去除地级市级别中的【省、自治区直辖县级行政区划】
	 *
	 * @param province
	 *            省份名称
	 * @param city
	 *            地级市名称
	 * @param district
	 *            区县名称
	 * @return
	 */
	public static String concatPcdTwo(String province, String city, String district) {
		String noneMunicipalityProvince = getNoneMunicipalityProvince(province);
		String cityNoneUpDirectCounty = getCityNoneUpDirectCounty(city);
		if (StringUtils.isBlank(cityNoneUpDirectCounty)){
			return noneMunicipalityProvince + getSimpleCityName(district);
		}else {
			return noneMunicipalityProvince + getSimpleCityName(cityNoneUpDirectCounty);
		}
	}

	public static void main(String[] args) {
		String s = concatPcdTwo("江苏省", "常州市", "西城区");
		System.out.println(s);
	}
}
