package com.zly.common.utils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Title ValidateUtils
 * @Description 校验工具类
 * @Create 2022-04-18 14:22
 * <AUTHOR>
 */
@SuppressWarnings({ "BooleanMethodIsAlwaysInverted", "unused" })
public class ValidateUtils {

	/**
	 * 校验手机号
	 *
	 * @param val
	 *            手机号
	 * @return 合法手机号则返回true，否则返回false
	 */
	public static boolean checkMobilePhone(String val) {
		if (null == val || val.length() != 11) {
			return false;
		}
		String pattern = "1[3456789]\\d{9}";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验座机号码（兼容带短横线和不带短横线的座机号码）
	 *
	 * @param val
	 *            座机号码
	 * @return 合法座机号码则返回true，否则返回false
	 */
	public static boolean checkTelephone(String val) {
		if (null == val || val.length() == 0) {
			return false;
		}
		String pattern = "^(0(([12])|([3-9][1-9]))\\d)-?\\d{7,8}$";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验中国人姓名（兼容汉族姓名和少数名族汉字姓名）
	 *
	 * @param val
	 *            姓名
	 * @return
	 */
	public static boolean checkChineseName(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		String pattern = "([\\u4e00-\\u9fa5]{2,25}|([\\u4e00-\\u9fa5]{1,20}·[\\u4e00-\\u9fa5]{1,20}))";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验中国公司名称（兼容带括号、&符号、数字、字母的情况）
	 *
	 * @param val
	 *            公司名称
	 * @return
	 */
	public static boolean checkChineseCompanyName(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		String pattern = "[\\u4e00-\\u9fa5A-Za-z0-9()（）&]{2,50}";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验统一社会信用代码<br/>
	 * 统一社会信用代码编码规则:<br/>
	 * 统一社会信用代码由18位的数字或大写英文字母（不适用I、O、Z、S、V）组成，包含以下五个部分：<br/>
	 * 第一部分（第1位）为登记管理部门代码，9表示工商部门（数字或大写英文字母）<br/>
	 * 第二部分（第2位）为机构类别代码（数字或大写英文字母）<br/>
	 * 第三部分（第3-8位）为登记管理机关行政区划码（数字）<br/>
	 * 第四部分（第9-17位）为全国组织机构代码（数字或大写英文字母）<br/>
	 * 第五部分（第18位）为校验码（数字或大写英文字母）<br/>
	 *
	 * @param val
	 *            统一社会信用代码
	 * @return
	 */
	public static boolean checkUnifiedSocialCreditCode(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		String pattern = "[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}";
		return Pattern.matches(pattern, val);
	}

	public static Date getBirthDayFromIdcard(String idCard) {
		Pattern pattern = Pattern.compile("\\d{6}(\\d{4})(\\d{2})(\\d{2}).*");
		Matcher matcher = pattern.matcher(idCard);
		if (matcher.matches()) {
			int year = Integer.parseInt(matcher.group(1));
			int month = Integer.parseInt(matcher.group(2));
			int day = Integer.parseInt(matcher.group(3));

			return DateUtils.parseDate(year + "-" + month + "-" + day, "yyyy-MM-dd");
		}
		return null;
	}

	public static String getGender(String idCard) {
		if (!checkIdentityNo(idCard)) {
			return null;
		}
		int genderBit = Integer.parseInt(idCard.substring(16, 17));
		return genderBit % 2 == 1 ? "男" : "女";
	}

	public static LocalDate getBirthDay(String idCard) {
		if (!checkIdentityNo(idCard)) {
			return null;
		}
		String birthDateStr = idCard.substring(6, 14);
		LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.BASIC_ISO_DATE);
		return birthDate;
	}

	/**
	 * 根据身份证号获取周岁
	 * 
	 * @param idCard
	 * @return
	 */
	public static Integer getAge(String idCard) {
		if (!checkIdentityNo(idCard)) {
			return null;
		}
		String birthDateStr = idCard.substring(6, 14);
		LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.BASIC_ISO_DATE);
		LocalDate currentDate = LocalDate.now();
		Integer diffYear = currentDate.getYear() - birthDate.getYear();
		if (currentDate.getMonthValue() < birthDate.getMonthValue()) {
			diffYear--;
		} else if (currentDate.getMonthValue() == birthDate.getMonthValue()) {
			if (currentDate.getDayOfMonth() < birthDate.getDayOfMonth()) {
				diffYear--;
			}
		}
		return diffYear;
	}

	public static boolean checkIdentityNoAll(String val) {
		if (checkIdentityNo(val) || checkIdentityNo15(val)) {
			return true;
		}
		return false;
	}

	public static boolean checkIdentityNoAllStrict(String val) {
		if (checkIdentityNoStrict(val) || checkIdentityNo15(val)) {
			return true;
		}
		return false;
	}

	/**
	 * 校验18位身份证号码 普通版 支持小写x<br/>
	 * 月日不匹配合法性，如2月30日，6月31日，不做异常校验
	 *
	 * @param val
	 *            18位身份证号码
	 * @return
	 */
	public static boolean checkIdentityNo(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		// 月日不匹配合法性，如2月30日，6月31日，不做异常校验
		String pattern = "[1-9]\\d{5}(19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验18位身份证号码 严格版 不支持小写x<br/>
	 * 月日不匹配合法性，如2月30日，6月31日，不做异常校验
	 *
	 * @param val
	 *            18位身份证号码
	 * @return
	 */
	public static boolean checkIdentityNoStrict(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		// 月日不匹配合法性，如2月30日，6月31日，不做异常校验
		String pattern = "[1-9]\\d{5}(19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9X]";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验15位身份证号码
	 *
	 * @param val
	 *            15位身份证号码
	 * @return
	 */
	public static boolean checkIdentityNo15(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		// 月日不匹配合法性，如2月30日，6月31日，不做异常校验
		String pattern = "[1-9]\\d{5}\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{2}[0-9Xx]";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验车牌号码
	 *
	 * @param val
	 *            车牌号码
	 * @return
	 */
	public static boolean checkVehiclePlateNo(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		val = val.toUpperCase();

		String pattern = "[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-HJ-NP-Z](([A-HJ-NP-Z0-9]{4}([A-HJ-NP-Z0-9]|挂))|[A-HJ-NP-Z0-9]{6})";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验正数（大于0的数字，可以是整数或者小数）
	 *
	 * @param val
	 *            数字字符串
	 * @return
	 */
	public static boolean checkPositiveNumber(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		String pattern = "([1-9]\\d*(\\.\\d+)?)|(0\\.\\d*[1-9]\\d*)";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验非负整数
	 *
	 * @param val
	 *            数字字符串
	 * @return
	 */
	public static boolean checkInteger(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		String pattern = "([1-9]\\d*)|0";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验常规字符（汉字、大小写英文字母、数字、姓名分隔符“·”、下划线、中文中划线、英文中划线等，连接符号只能在中间）
	 *
	 * @param val
	 *            常规字符串
	 * @return
	 */
	public static boolean checkNormalString(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		String pattern = "[\\u4e00-\\u9fa5A-Za-z0-9()（）]+([\\u4e00-\\u9fa5A-Za-z0-9()（）·_—#-]*[\\u4e00-\\u9fa5A-Za-z0-9()（）]+)?";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验数字拼接字符串（由数字和逗号拼接出来的字符串）
	 *
	 * @param val
	 *            数字拼接字符串
	 * @return
	 */
	public static boolean checkDotSplitNumbers(String val) {
		if (null == val || 0 == val.length()) {
			return false;
		}

		String pattern = "([1-9]\\d*)(,([1-9]\\d*)*)*";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验日期类型
	 *
	 * @param val
	 *            日期字符串(yyyy-MM-dd，如 2022-07-01 或 2022-7-1 或 2022/7/1 或 2022/07/01)
	 * @return
	 */
	public static boolean checkSQLDate(String val) {
		if (null == val || val.length() == 0) {
			return false;
		}
		String pattern = "^(?:(?!0000)[0-9]{4}([-/.]?)(?:(?:0?[1-9]|1[0-2])([-/.]?)(?:0?[1-9]|1[0-9]|2[0-8])|(?:0?[13-9]|1[0-2])([-/.]?)(?:29|30)|(?:0?[13578]|1[02])([-/.]?)31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)([-/.]?)0?2([-/.]?)29)$";
		return Pattern.matches(pattern, val);
	}

	/**
	 * 校验小数位数是否符合上限要求
	 *
	 * @param value
	 * @param digitsLimit
	 * @return
	 */
	public static boolean checkDecimalDigits(String value, int digitsLimit) {
		try {
			if (BigDecimal.ZERO.compareTo(new BigDecimal(value)) == 0) {
				return true;
			}
		} catch (Exception e) {
			return false;
		}
		if (!checkPositiveNumber(value)) {
			return false;
		}
		String digits = value.contains(".") ? value.substring(value.indexOf(".") + 1) : "";
		while (digits.endsWith("0")) {
			digits = digits.substring(0, digits.length() - 1);
		}
		return digits.length() <= digitsLimit;
	}


	public static boolean checkVehicleNumber(String vehicleNumber) {
		if (CommonUtil.isNullOrEmpty(vehicleNumber)) {
			return false;
		}
		// 正则表达式校验车牌号
		String pattern = "^[\\u4e00-\\u9fa5][A-Z][A-HJ-NP-Z0-9]{5}$" + // 传统燃油车（7位）
				"|^[\\u4e00-\\u9fa5][A-Z][DF][A-HJ-NP-Z0-9]{5}$" + // 新能源小型车（8位）
				"|^[\\u4e00-\\u9fa5][A-Z]\\d{5}[DF]$"; // 新能源大型车（8位）
		return vehicleNumber.matches(pattern);
	}
}
