package com.zly.common.utils;

import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import com.zly.framework.config.CustomConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

@Slf4j
public class QiNiuUtil {

	/**
	 * 上传文件，使用默认策略，只需要设置上传的空间名就可以了
	 *
	 * @param filePath
	 *            本地文件路径
	 * @param key
	 *            上传到七牛的该文件的标识符
	 * @return
	 * @throws IOException
	 */
	public static Response upload(String filePath, String key) {
		try {
			// 构造一个带指定Zone对象的配置类 华东:Zone.zone0() 华北:Zone.zone1() 华南:Zone.zone2() 北美:Zone.zoneNa0()
			Configuration cfg = new Configuration(Region.region0());
			UploadManager uploadManager = new UploadManager(cfg);// 创建上传对象
			StringMap policy = new StringMap();
			policy.put("mimeLimit", "!application/json;text/plain");// 表示禁止上传json文本和纯文本。注意最前面的感叹号！
			System.out.println(CustomConfig.qiNiuAccessKeyStatic);
			String uploadToken = Auth.create(CustomConfig.qiNiuAccessKeyStatic, CustomConfig.qiNiuSecretKeyStatic).uploadToken(CustomConfig.qiNiuBucketStatic, null, 7200, policy);
			Response res = uploadManager.put(filePath, key, uploadToken);// 调用put方法上传
			// 打印返回的信息
			System.out.println(res.isOK() + "," + res.url() + "," + res.bodyString());
			return res;
		} catch (QiniuException e) {
			log.error(ExceptionUtils.getStackTrace(e));
			e.printStackTrace();
			Response r = e.response;
			// 请求失败时打印的异常的信息
			log.error(r.toString());
			return null;
		}
	}

	/**
	 * 上传文件，使用默认策略，只需要设置上传的空间名就可以了
	 *
	 * @param file
	 *            本地文件路径
	 * @param key
	 *            上传到七牛的该文件的标识符
	 * @return
	 * @throws IOException
	 */
	public static Response uploadByFile(MultipartFile file, String key) throws IOException {
		try {
			// 构造一个带指定Zone对象的配置类 华东:Zone.zone0() 华北:Zone.zone1() 华南:Zone.zone2() 北美:Zone.zoneNa0()
			Configuration cfg = new Configuration(Region.region0());
			UploadManager uploadManager = new UploadManager(cfg);// 创建上传对象
			InputStream inputStream = file.getInputStream();
			StringMap policy = new StringMap();
			policy.put("mimeLimit", "!application/json;text/plain");// 表示禁止上传json文本和纯文本。注意最前面的感叹号！
			String uploadToken = Auth.create(CustomConfig.qiNiuAccessKeyStatic, CustomConfig.qiNiuSecretKeyStatic).uploadToken(CustomConfig.qiNiuBucketStatic, null, 7200, policy);
			Response res = uploadManager.put(inputStream, key, uploadToken, null, null);
			// 打印返回的信息
			System.out.println(res.isOK() + "," + res.url() + "," + res.bodyString());
			return res;
		} catch (QiniuException e) {
			Response r = e.response;
			// 请求失败时打印的异常的信息
			System.out.println(r.toString());
			try {
				// 响应的文本信息
				System.out.println(r.bodyString());
			} catch (QiniuException e1) {
				// ignore
			}
			return r;
		}
	}

	/**
	 * 上传文件，使用默认策略，只需要设置上传的空间名就可以了
	 *
	 * @param inputStream
	 *            本地文件路径
	 * @param key
	 *            上传到七牛的该文件的标识符
	 * @return
	 * @throws IOException
	 */
	public static boolean uploadInputStream(InputStream inputStream, String key) throws IOException {
		try {
			// 构造一个带指定Zone对象的配置类 华东:Zone.zone0() 华北:Zone.zone1() 华南:Zone.zone2() 北美:Zone.zoneNa0()
			Configuration cfg = new Configuration(Region.region0());
			UploadManager uploadManager = new UploadManager(cfg);// 创建上传对象
			StringMap policy = new StringMap();
			policy.put("mimeLimit", "!application/json;text/plain");// 表示禁止上传json文本和纯文本。注意最前面的感叹号！
			String uploadToken = Auth.create(CustomConfig.qiNiuAccessKeyStatic, CustomConfig.qiNiuSecretKeyStatic).uploadToken(CustomConfig.qiNiuBucketStatic, null, 7200, policy);
			Response res = uploadManager.put(inputStream, key, uploadToken, null, null);
			// 打印返回的信息
			System.out.println(res.isOK() + "," + res.url() + "," + res.bodyString());
			return res.isOK();
		} catch (QiniuException e) {
			Response r = e.response;
			// 请求失败时打印的异常的信息
			System.out.println(r.toString());
			try {
				// 响应的文本信息
				System.out.println(r.bodyString());
			} catch (QiniuException e1) {
				// ignore
			}
			return false;
		}
	}

	/**
	 * 上传文件，使用默认策略，只需要设置上传的空间名就可以了
	 *
	 * @param wb
	 *            本地文件路径
	 * @param key
	 *            上传到七牛的该文件的标识符
	 * @return
	 * @throws IOException
	 */
	public static Response uploadByWorkbook(Workbook wb, String key) throws IOException {
		try {
			// 构造一个带指定Zone对象的配置类 华东:Zone.zone0() 华北:Zone.zone1() 华南:Zone.zone2() 北美:Zone.zoneNa0()
			Configuration cfg = new Configuration(Region.region0());
			UploadManager uploadManager = new UploadManager(cfg);// 创建上传对象
			InputStream inputStream = getWorkBookInputStream(wb);
			StringMap policy = new StringMap();
			policy.put("mimeLimit", "!application/json;text/plain");// 表示禁止上传json文本和纯文本。注意最前面的感叹号！
			String uploadToken = Auth.create(CustomConfig.qiNiuAccessKeyStatic, CustomConfig.qiNiuSecretKeyStatic).uploadToken(CustomConfig.qiNiuBucketStatic, null, 7200, policy);
			Response res = uploadManager.put(inputStream, key, uploadToken, null, null);
			// 打印返回的信息
			System.out.println(res.isOK() + "," + res.url() + "," + res.bodyString());
			return res;
		} catch (QiniuException e) {
			Response r = e.response;
			// 请求失败时打印的异常的信息
			System.out.println(r.toString());
			try {
				// 响应的文本信息
				System.out.println(r.bodyString());
			} catch (QiniuException e1) {
				// ignore
			}
			return r;
		}
	}

	private static InputStream getWorkBookInputStream(Workbook wb) {
		ByteArrayInputStream in = null;
		try {
			ByteArrayOutputStream os = new ByteArrayOutputStream();
			wb.write(os);

			byte[] b = os.toByteArray();
			in = new ByteArrayInputStream(b);

			os.close();

		} catch (IOException e) {
			// TODO Auto-generated catch block
			log.error("ExcelUtils getExcelFile error:{}",e.toString());
			return null;
		}

		return in;
	}

	/**
	 * 在多少时间后删除文件
	 *
	 * @param key
	 * @param days
	 * @return
	 */
	public static Response deleteAfterDays(String key, int days) {
		try {
			Configuration cfg = new Configuration(Region.region0());
			Auth auth = Auth.create(CustomConfig.qiNiuAccessKeyStatic, CustomConfig.qiNiuSecretKeyStatic);
			BucketManager bucketManager = new BucketManager(auth, cfg);
			return bucketManager.deleteAfterDays(CustomConfig.qiNiuBucketStatic, key, days);
		} catch (QiniuException ex) {
			System.err.println(ex.response.toString());
			return null;
		}
	}
}
