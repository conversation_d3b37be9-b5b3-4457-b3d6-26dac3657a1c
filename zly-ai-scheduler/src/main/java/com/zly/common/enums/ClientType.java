package com.zly.common.enums;

/**
 * 客户端系统类型（<br/>
 * 1.集团统筹端 2.网络货运人端/平台端 3.托运人端/货主端 <br/>
 * 4.承运人端/司机端 5.微信快速录单 6.微信小程序托运人端 <br/>
 * 7.网络货运人代运营系统 8.物流金融服务系统 9.智联云运维系统<br/>
 * ）
 */
public enum ClientType {
	/** 0.占_位_勿_用 */
	____(0, "占_位_勿_用"),
	/** 1.集团端 */
	GROUP(1, "集团统筹端"),
	/** 2.平台端/网络货运人端 */
	PLATFORM(2, "网络货运人端"),
	/** 3.托运人端/货主端 */
	SHIPPER(3, "托运人端"),
	/** 4.承运人端/司机端 */
	CARRIER(4, "承运人端"),
	/** 5.微信快速录单 */
	QUICK(5, "快速录单"),
	/** 6.微信小程序托运人端 */
	WXMP_TENANT(6, "微信小程序托运人端"),
	/** 7.网络货运人代运营系统 */
	AGENT(7, "网络货运人代运营系统"),
	/** 8.物流金融服务系统 */
	FINANCE(8, "物流金融服务系统"),
	/** 9.智联云运维系统 */
	BACKGROUND(9, "智联云运维系统"),

	DRIVERCARD(10, "司机名片");

	int value;
	String desc;

	ClientType(int value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public int val() {
		return value;
	}

	public String desc() {
		return desc;
	}

	/**
	 * 判断传入的类型是否相等
	 *
	 * @param anotherDesc
	 * @return
	 */
	public boolean equals(String anotherDesc) {
		return this.desc.equals(anotherDesc);
	}

	/**
	 * 判断传入的类型值是否相等
	 *
	 * @param value
	 * @return
	 */
	public boolean equals(int value) {
		return this.value == value;
	}

	/**
	 * 判断传入的类型值是否相等
	 *
	 * @param value
	 * @return
	 */
	public boolean equals(Integer value) {
		if (null == value) {
			return false;
		}
		return this.value == value;
	}

	@Override
	public String toString() {
		return "ClientType{" + "value=" + value + ", desc='" + desc + '\'' + '}';
	}
}
