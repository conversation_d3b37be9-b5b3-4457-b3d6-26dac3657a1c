package com.zly.common.filter;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.AbstractMatcherFilter;
import ch.qos.logback.core.spi.FilterReply;

/**
 * @Title LogbackLevelFilter
 * @Description 日志级别配置过滤器
 * @Create 2023-04-13 13:09
 * <AUTHOR>
 */
public class LogbackLevelFilter extends AbstractMatcherFilter<ILoggingEvent> {

	Level level;

	@Override
	public FilterReply decide(ILoggingEvent event) {
		if (!isStarted()) {
			return FilterReply.NEUTRAL;
		}

		if (level.equals(Level.INFO)) {
			// 如果配置为info级别，则按原有逻辑显示info级别的信息
			if (event.getLevel().equals(level)) {
				return FilterReply.NEUTRAL;
			} else {
				return FilterReply.DENY;
			}
		} else if (level.equals(Level.DEBUG)) {
			// 如果配置为debug级别，则显示info和debug级别的信息
			if (event.getLevel().equals(Level.INFO) || event.getLevel().equals(Level.DEBUG)) {
				return FilterReply.NEUTRAL;
			} else {
				return FilterReply.DENY;
			}
		}
		return FilterReply.NEUTRAL;
	}

	public void setLevel(String level) {
		this.level = Level.toLevel(level);
	}

	public void start() {
		if (this.level != null) {
			super.start();
		}
	}
}
