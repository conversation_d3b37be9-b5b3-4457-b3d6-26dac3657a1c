package com.zly.common.constant;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * API地址常量类
 * 
 * <AUTHOR>
 */
@Component
public class ApiUrlConstants
{
    /**
     * -- GETTER --
     *  获取基础URL
     *
     * @return 基础URL
     */
    @Getter
    private static String baseUrl;

    @Value("${custom.scheduler.baseUrl}")
    public void setBaseUrl(String baseUrl) {
        ApiUrlConstants.baseUrl = baseUrl;
    }

    /** 获取Token */
    public static final String GET_TOKEN = "/api/sys/click/login/getToken";

    /** 获取货主信息列表 */
    public static final String CUSTOMER_INFO_LIST = "/api/consignor/customer/list";

    /** 获取托运人信息详情 */
    public static final String CUSTOMER_INFO_DETAIL = "/api/consignor/customer/info";
    
    /** 获取平台列表 */
    public static final String PLATFORM_LIST = "/api/framework/contract/platform/list";

    public static final String PLATFORM_SEARCH_LIST = "/api/framework/contract/platform/searchList";

    /** 获取善道用户信息 */
    public static final String SHANDAO_USERS = "/api/system/user/getShandaoUsers";

    /** 根据用户编号获取详细信息 */
    public static final String USER_INFO = "/api/system/user";

}
