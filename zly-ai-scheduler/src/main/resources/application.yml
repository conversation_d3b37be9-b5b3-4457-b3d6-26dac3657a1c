# 项目相关配置
sys:
  # 名称
  name: 智联云AI调度管理系统
  # 版本
  version: 1.0.3
  # 版权年份
  copyrightYear: 2025
  # 打包的文件名
  app_name: zly-ai-scheduler
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 日志配置
logging:
  level:
    com.zly: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10
    secret: YtZayqRxbu+Fsrn65g/ReJv5A3HcqPsIyuY0FWiWA14=

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  jackson:
    #日期格式化
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  profiles:
    active: test
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
      additional-paths : src/main/java/project

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: dV.t2j{>sz+h7AY=]CGH
  # 令牌有效期（默认30分钟）
  expireTime: 1200
  
# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.zly.project.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mybatis/**/*Mapper.xml,classpath*:mybatis/**/*MapperEx.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/system/personalize/edit
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
  
# 代码生成
gen:
  # 作者
  author: zly
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.zly.project.code
  # 自动去除表前缀，默认是true
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_
  # 是否允许生成文件覆盖到本地（自定义路径），默认不允许
  allowOverwrite: false
