<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.driver.mapper.SchedulerDriverProfileMapper">
    
    <resultMap type="SchedulerDriverProfile" id="SchedulerDriverProfileResult">
        <result property="id"    column="id"    />
        <result property="driverId"    column="driver_id"    />
        <result property="driverName"    column="driver_name"    />
        <result property="telephone"    column="telephone"    />
        <result property="schedulerTelephone"    column="scheduler_telephone"    />
        <result property="identityCard"    column="identity_card"    />
        <result property="drivingYears"    column="driving_years"    />
        <result property="profileCompleteness"    column="profile_completeness"    />
        <result property="serviceCount"    column="service_count"    />
        <result property="totalCourse"    column="total_course"    />
        <result property="operation"    column="operation"    />
        <result property="operationDesc"    column="operation_desc"    />
        <result property="isTheDrivrLicenseValid"    column="is_the_drivr_license_valid"    />
        <result property="vehicleClass"    column="vehicle_class"    />
        <result property="validPeriodTo"    column="valid_period_to"    />
        <result property="qualificationCertificate"    column="qualification_certificate"    />
        <result property="isTheQualificationValid"    column="is_the_qualification_valid"    />
        <result property="actualCarrierEnd"    column="actual_carrier_end"    />
        <result property="lastServiceTime"    column="last_service_time"    />
        <result property="state"    column="state"    />
        <result property="address"    column="address"    />
        <result property="vehicles"    column="vehicles"    />
        <result property="longDistanceRoutes"    column="long_distance_routes"    />
        <result property="cooperativeStatus"    column="cooperative_status"    />
        <result property="blacklistStatus"    column="blacklist_status"    />
        <result property="overallEvaluate"    column="overall_evaluate"    />
        <result property="schedulerVehicleStructure"    column="scheduler_vehicle_structure"    />
        <result property="wlhyVehicleStructure"    column="wlhy_vehicle_structure"    />
        <result property="excludedVehicleStructure"    column="excluded_vehicle_structure"    />
        <result property="schedulerVehicleLength"    column="scheduler_vehicle_length"    />
        <result property="wlhyVehicleLength"    column="wlhy_vehicle_length"    />
        <result property="excludedVehicleLength"    column="excluded_vehicle_length"    />
        <result property="schedulerGoods"    column="scheduler_goods"    />
        <result property="wlhyGoods"    column="wlhy_goods"    />
        <result property="excludedGoods"    column="excluded_goods"    />
        <result property="schedulerHaulway"    column="scheduler_haulway"    />
        <result property="wlhyHaulway"    column="wlhy_haulway"    />
        <result property="excludedHaulway"    column="excluded_haulway"    />
        <result property="blackenReason"    column="blacken_reason"    />
        <result property="changeTelReason"    column="change_tel_reason"    />
        <result property="remark"    column="remark"    />
        <result property="driverRating"    column="driver_rating"    />
        <result property="driverTag"    column="driver_tag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
    </resultMap>

    <sql id="selectSchedulerDriverProfileVo">
        select id, driver_id, driver_name, telephone, scheduler_telephone, identity_card, driving_years, profile_completeness, service_count, total_course, operation, operation_desc, is_the_drivr_license_valid, vehicle_class, valid_period_to, qualification_certificate, is_the_qualification_valid, actual_carrier_end, last_service_time, state, address, vehicles, long_distance_routes, cooperative_status, blacklist_status, overall_evaluate, scheduler_vehicle_structure, wlhy_vehicle_structure, excluded_vehicle_structure, scheduler_vehicle_length, wlhy_vehicle_length, excluded_vehicle_length, scheduler_goods, wlhy_goods, excluded_goods, scheduler_haulway, wlhy_haulway, excluded_haulway, blacken_reason, change_tel_reason, remark, driver_rating, driver_tag, create_by, create_time, update_by, update_time, deleted from scheduler_driver_profile
    </sql>

    <select id="selectSchedulerDriverProfileList" parameterType="SchedulerDriverProfile" resultMap="SchedulerDriverProfileResult">
        <include refid="selectSchedulerDriverProfileVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="driverId != null  and driverId != ''"> and driver_id = #{driverId}</if>
            <if test="driverName != null  and driverName != ''"> and driver_name = #{driverName}</if>
            <if test="telephone != null  and telephone != ''"> and telephone = #{telephone}</if>
            <if test="schedulerTelephone != null  and schedulerTelephone != ''"> and scheduler_telephone = #{schedulerTelephone}</if>
            <if test="identityCard != null  and identityCard != ''"> and identity_card = #{identityCard}</if>
            <if test="drivingYears != null "> and driving_years = #{drivingYears}</if>
            <if test="profileCompleteness != null "> and profile_completeness = #{profileCompleteness}</if>
            <if test="serviceCount != null "> and service_count = #{serviceCount}</if>
            <if test="totalCourse != null  and totalCourse != ''"> and total_course = #{totalCourse}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="operationDesc != null  and operationDesc != ''"> and operation_desc = #{operationDesc}</if>
            <if test="isTheDrivrLicenseValid != null "> and is_the_drivr_license_valid = #{isTheDrivrLicenseValid}</if>
            <if test="vehicleClass != null  and vehicleClass != ''"> and vehicle_class = #{vehicleClass}</if>
            <if test="validPeriodTo != null  and validPeriodTo != ''"> and valid_period_to = #{validPeriodTo}</if>
            <if test="qualificationCertificate != null  and qualificationCertificate != ''"> and qualification_certificate = #{qualificationCertificate}</if>
            <if test="isTheQualificationValid != null "> and is_the_qualification_valid = #{isTheQualificationValid}</if>
            <if test="actualCarrierEnd != null  and actualCarrierEnd != ''"> and actual_carrier_end = #{actualCarrierEnd}</if>
            <if test="params.beginLastServiceTime != null and params.beginLastServiceTime != '' and params.endLastServiceTime != null and params.endLastServiceTime != ''"> and last_service_time between #{params.beginLastServiceTime} and #{params.endLastServiceTime}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="vehicles != null  and vehicles != ''"> and vehicles = #{vehicles}</if>
            <if test="longDistanceRoutes != null  and longDistanceRoutes != ''"> and long_distance_routes = #{longDistanceRoutes}</if>
            <if test="cooperativeStatus != null "> and cooperative_status = #{cooperativeStatus}</if>
            <if test="blacklistStatus != null "> and blacklist_status = #{blacklistStatus}</if>
            <if test="overallEvaluate != null  and overallEvaluate != ''"> and overall_evaluate = #{overallEvaluate}</if>
            <if test="schedulerVehicleStructure != null  and schedulerVehicleStructure != ''"> and scheduler_vehicle_structure = #{schedulerVehicleStructure}</if>
            <if test="wlhyVehicleStructure != null  and wlhyVehicleStructure != ''"> and wlhy_vehicle_structure = #{wlhyVehicleStructure}</if>
            <if test="excludedVehicleStructure != null  and excludedVehicleStructure != ''"> and excluded_vehicle_structure = #{excludedVehicleStructure}</if>
            <if test="schedulerVehicleLength != null  and schedulerVehicleLength != ''"> and scheduler_vehicle_length = #{schedulerVehicleLength}</if>
            <if test="wlhyVehicleLength != null  and wlhyVehicleLength != ''"> and wlhy_vehicle_length = #{wlhyVehicleLength}</if>
            <if test="excludedVehicleLength != null  and excludedVehicleLength != ''"> and excluded_vehicle_length = #{excludedVehicleLength}</if>
            <if test="schedulerGoods != null  and schedulerGoods != ''"> and scheduler_goods = #{schedulerGoods}</if>
            <if test="wlhyGoods != null  and wlhyGoods != ''"> and wlhy_goods = #{wlhyGoods}</if>
            <if test="excludedGoods != null  and excludedGoods != ''"> and excluded_goods = #{excludedGoods}</if>
            <if test="schedulerHaulway != null  and schedulerHaulway != ''"> and scheduler_haulway = #{schedulerHaulway}</if>
            <if test="wlhyHaulway != null  and wlhyHaulway != ''"> and wlhy_haulway = #{wlhyHaulway}</if>
            <if test="excludedHaulway != null  and excludedHaulway != ''"> and excluded_haulway = #{excludedHaulway}</if>
            <if test="blackenReason != null  and blackenReason != ''"> and blacken_reason = #{blackenReason}</if>
            <if test="changeTelReason != null  and changeTelReason != ''"> and change_tel_reason = #{changeTelReason}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
            <if test="driverRating != null  and driverRating != ''"> and driver_rating = #{driverRating}</if>
            <if test="driverTag != null  and driverTag != ''"> and driver_tag = #{driverTag}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''"> and update_time between #{params.beginUpdateTime} and #{params.endUpdateTime}</if>
            <if test="deleted != null "> and deleted = #{deleted}</if>
        </where>
    </select>
    
    <select id="selectSchedulerDriverProfileById" parameterType="Long" resultMap="SchedulerDriverProfileResult">
        <include refid="selectSchedulerDriverProfileVo"/>
        where id = #{id}
    </select>

    <insert id="insertSchedulerDriverProfile" parameterType="SchedulerDriverProfile" useGeneratedKeys="true" keyProperty="id">
        insert into scheduler_driver_profile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="driverId != null and driverId != ''">driver_id,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="telephone != null">telephone,</if>
            <if test="schedulerTelephone != null and schedulerTelephone != ''">scheduler_telephone,</if>
            <if test="identityCard != null">identity_card,</if>
            <if test="drivingYears != null">driving_years,</if>
            <if test="profileCompleteness != null">profile_completeness,</if>
            <if test="serviceCount != null">service_count,</if>
            <if test="totalCourse != null">total_course,</if>
            <if test="operation != null">operation,</if>
            <if test="operationDesc != null">operation_desc,</if>
            <if test="isTheDrivrLicenseValid != null">is_the_drivr_license_valid,</if>
            <if test="vehicleClass != null">vehicle_class,</if>
            <if test="validPeriodTo != null">valid_period_to,</if>
            <if test="qualificationCertificate != null">qualification_certificate,</if>
            <if test="isTheQualificationValid != null">is_the_qualification_valid,</if>
            <if test="actualCarrierEnd != null">actual_carrier_end,</if>
            <if test="lastServiceTime != null">last_service_time,</if>
            <if test="state != null">state,</if>
            <if test="address != null">address,</if>
            <if test="vehicles != null">vehicles,</if>
            <if test="longDistanceRoutes != null">long_distance_routes,</if>
            <if test="cooperativeStatus != null">cooperative_status,</if>
            <if test="blacklistStatus != null">blacklist_status,</if>
            <if test="overallEvaluate != null and overallEvaluate != ''">overall_evaluate,</if>
            <if test="schedulerVehicleStructure != null">scheduler_vehicle_structure,</if>
            <if test="wlhyVehicleStructure != null">wlhy_vehicle_structure,</if>
            <if test="excludedVehicleStructure != null">excluded_vehicle_structure,</if>
            <if test="schedulerVehicleLength != null">scheduler_vehicle_length,</if>
            <if test="wlhyVehicleLength != null">wlhy_vehicle_length,</if>
            <if test="excludedVehicleLength != null">excluded_vehicle_length,</if>
            <if test="schedulerGoods != null">scheduler_goods,</if>
            <if test="wlhyGoods != null">wlhy_goods,</if>
            <if test="excludedGoods != null">excluded_goods,</if>
            <if test="schedulerHaulway != null">scheduler_haulway,</if>
            <if test="wlhyHaulway != null">wlhy_haulway,</if>
            <if test="excludedHaulway != null">excluded_haulway,</if>
            <if test="blackenReason != null and blackenReason != ''">blacken_reason,</if>
            <if test="changeTelReason != null and changeTelReason != ''">change_tel_reason,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="driverRating != null">driver_rating,</if>
            <if test="driverTag != null">driver_tag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="driverId != null and driverId != ''">#{driverId},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="telephone != null">#{telephone},</if>
            <if test="schedulerTelephone != null and schedulerTelephone != ''">#{schedulerTelephone},</if>
            <if test="identityCard != null">#{identityCard},</if>
            <if test="drivingYears != null">#{drivingYears},</if>
            <if test="profileCompleteness != null">#{profileCompleteness},</if>
            <if test="serviceCount != null">#{serviceCount},</if>
            <if test="totalCourse != null">#{totalCourse},</if>
            <if test="operation != null">#{operation},</if>
            <if test="operationDesc != null">#{operationDesc},</if>
            <if test="isTheDrivrLicenseValid != null">#{isTheDrivrLicenseValid},</if>
            <if test="vehicleClass != null">#{vehicleClass},</if>
            <if test="validPeriodTo != null">#{validPeriodTo},</if>
            <if test="qualificationCertificate != null">#{qualificationCertificate},</if>
            <if test="isTheQualificationValid != null">#{isTheQualificationValid},</if>
            <if test="actualCarrierEnd != null">#{actualCarrierEnd},</if>
            <if test="lastServiceTime != null">#{lastServiceTime},</if>
            <if test="state != null">#{state},</if>
            <if test="address != null">#{address},</if>
            <if test="vehicles != null">#{vehicles},</if>
            <if test="longDistanceRoutes != null">#{longDistanceRoutes},</if>
            <if test="cooperativeStatus != null">#{cooperativeStatus},</if>
            <if test="blacklistStatus != null">#{blacklistStatus},</if>
            <if test="overallEvaluate != null and overallEvaluate != ''">#{overallEvaluate},</if>
            <if test="schedulerVehicleStructure != null">#{schedulerVehicleStructure},</if>
            <if test="wlhyVehicleStructure != null">#{wlhyVehicleStructure},</if>
            <if test="excludedVehicleStructure != null">#{excludedVehicleStructure},</if>
            <if test="schedulerVehicleLength != null">#{schedulerVehicleLength},</if>
            <if test="wlhyVehicleLength != null">#{wlhyVehicleLength},</if>
            <if test="excludedVehicleLength != null">#{excludedVehicleLength},</if>
            <if test="schedulerGoods != null">#{schedulerGoods},</if>
            <if test="wlhyGoods != null">#{wlhyGoods},</if>
            <if test="excludedGoods != null">#{excludedGoods},</if>
            <if test="schedulerHaulway != null">#{schedulerHaulway},</if>
            <if test="wlhyHaulway != null">#{wlhyHaulway},</if>
            <if test="excludedHaulway != null">#{excludedHaulway},</if>
            <if test="blackenReason != null and blackenReason != ''">#{blackenReason},</if>
            <if test="changeTelReason != null and changeTelReason != ''">#{changeTelReason},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="driverRating != null">#{driverRating},</if>
            <if test="driverTag != null">#{driverTag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
         </trim>
    </insert>

    <update id="updateSchedulerDriverProfile" parameterType="SchedulerDriverProfile">
        update scheduler_driver_profile
        <trim prefix="SET" suffixOverrides=",">
            <if test="driverId != null and driverId != ''">driver_id = #{driverId},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <if test="schedulerTelephone != null and schedulerTelephone != ''">scheduler_telephone = #{schedulerTelephone},</if>
            <if test="identityCard != null">identity_card = #{identityCard},</if>
            <if test="drivingYears != null">driving_years = #{drivingYears},</if>
            <if test="profileCompleteness != null">profile_completeness = #{profileCompleteness},</if>
            <if test="serviceCount != null">service_count = #{serviceCount},</if>
            <if test="totalCourse != null">total_course = #{totalCourse},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="operationDesc != null">operation_desc = #{operationDesc},</if>
            <if test="isTheDrivrLicenseValid != null">is_the_drivr_license_valid = #{isTheDrivrLicenseValid},</if>
            <if test="vehicleClass != null">vehicle_class = #{vehicleClass},</if>
            <if test="validPeriodTo != null">valid_period_to = #{validPeriodTo},</if>
            <if test="qualificationCertificate != null">qualification_certificate = #{qualificationCertificate},</if>
            <if test="isTheQualificationValid != null">is_the_qualification_valid = #{isTheQualificationValid},</if>
            <if test="actualCarrierEnd != null">actual_carrier_end = #{actualCarrierEnd},</if>
            <if test="lastServiceTime != null">last_service_time = #{lastServiceTime},</if>
            <if test="state != null">state = #{state},</if>
            <if test="address != null">address = #{address},</if>
            <if test="vehicles != null">vehicles = #{vehicles},</if>
            <if test="longDistanceRoutes != null">long_distance_routes = #{longDistanceRoutes},</if>
            <if test="cooperativeStatus != null">cooperative_status = #{cooperativeStatus},</if>
            <if test="blacklistStatus != null">blacklist_status = #{blacklistStatus},</if>
            <if test="overallEvaluate != null and overallEvaluate != ''">overall_evaluate = #{overallEvaluate},</if>
            <if test="schedulerVehicleStructure != null">scheduler_vehicle_structure = #{schedulerVehicleStructure},</if>
            <if test="wlhyVehicleStructure != null">wlhy_vehicle_structure = #{wlhyVehicleStructure},</if>
            <if test="excludedVehicleStructure != null">excluded_vehicle_structure = #{excludedVehicleStructure},</if>
            <if test="schedulerVehicleLength != null">scheduler_vehicle_length = #{schedulerVehicleLength},</if>
            <if test="wlhyVehicleLength != null">wlhy_vehicle_length = #{wlhyVehicleLength},</if>
            <if test="excludedVehicleLength != null">excluded_vehicle_length = #{excludedVehicleLength},</if>
            <if test="schedulerGoods != null">scheduler_goods = #{schedulerGoods},</if>
            <if test="wlhyGoods != null">wlhy_goods = #{wlhyGoods},</if>
            <if test="excludedGoods != null">excluded_goods = #{excludedGoods},</if>
            <if test="schedulerHaulway != null">scheduler_haulway = #{schedulerHaulway},</if>
            <if test="wlhyHaulway != null">wlhy_haulway = #{wlhyHaulway},</if>
            <if test="excludedHaulway != null">excluded_haulway = #{excludedHaulway},</if>
            <if test="blackenReason != null and blackenReason != ''">blacken_reason = #{blackenReason},</if>
            <if test="changeTelReason != null and changeTelReason != ''">change_tel_reason = #{changeTelReason},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="driverRating != null">driver_rating = #{driverRating},</if>
            <if test="driverTag != null">driver_tag = #{driverTag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSchedulerDriverProfileById" parameterType="Long">
        delete from scheduler_driver_profile where id = #{id}
    </delete>

    <delete id="deleteSchedulerDriverProfileByIds" parameterType="String">
        delete from scheduler_driver_profile where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>