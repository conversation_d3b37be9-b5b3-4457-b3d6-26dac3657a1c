<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.driver.mapper.SchedulerDriverProfileMapperEx">
    
    <resultMap type="SchedulerDriverProfileListRes" id="SchedulerDriverProfileResult">
        <result property="id"    column="id"    />
        <result property="driverId"    column="driver_id"    />
        <result property="driverName"    column="driver_name"    />
        <result property="telephone"    column="telephone"    />
        <result property="schedulerTelephone"    column="scheduler_telephone"    />
        <result property="identityCard"    column="identity_card"    />
        <result property="drivingYears"    column="driving_years"    />
        <result property="profileCompleteness"    column="profile_completeness"    />
        <result property="serviceCount"    column="service_count"    />
        <result property="totalCourse"    column="total_course"    />
        <result property="operation"    column="operation"    />
        <result property="operationDesc"    column="operation_desc"    />
        <result property="isTheDrivrLicenseValid"    column="is_the_drivr_license_valid"    />
        <result property="vehicleClass"    column="vehicle_class"    />
        <result property="validPeriodTo"    column="valid_period_to"    />
        <result property="qualificationCertificate"    column="qualification_certificate"    />
        <result property="isTheQualificationValid"    column="is_the_qualification_valid"    />
        <result property="actualCarrierEnd"    column="actual_carrier_end"    />
        <result property="lastServiceTime"    column="last_service_time"    />
        <result property="state"    column="state"    />
        <result property="address"    column="address"    />
        <result property="vehicles"    column="vehicles"    />
        <result property="longDistanceRoutes"    column="long_distance_routes"    />
        <result property="cooperativeStatus"    column="cooperative_status"    />
        <result property="blacklistStatus"    column="blacklist_status"    />
        <result property="overallEvaluate"    column="overall_evaluate"    />
        <result property="schedulerVehicleStructure"    column="scheduler_vehicle_structure"    />
        <result property="wlhyVehicleStructure"    column="wlhy_vehicle_structure"    />
        <result property="excludedVehicleStructure"    column="excluded_vehicle_structure"    />
        <result property="schedulerVehicleLength"    column="scheduler_vehicle_length"    />
        <result property="wlhyVehicleLength"    column="wlhy_vehicle_length"    />
        <result property="excludedVehicleLength"    column="excluded_vehicle_length"    />
        <result property="schedulerGoods"    column="scheduler_goods"    />
        <result property="wlhyGoods"    column="wlhy_goods"    />
        <result property="excludedGoods"    column="excluded_goods"    />
        <result property="schedulerHaulway"    column="scheduler_haulway"    />
        <result property="wlhyHaulway"    column="wlhy_haulway"    />
        <result property="excludedHaulway"    column="excluded_haulway"    />
        <result property="content"    column="content"    />
        <result property="blackenReason"    column="blacken_reason"    />
        <result property="changeTelReason"    column="change_tel_reason"    />
        <result property="remark"    column="remark"    />
        <result property="driverRating"    column="driver_rating"    />
        <result property="driverTag"    column="driver_tag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
    </resultMap>

    <sql id="selectSchedulerDriverProfileVo">
        select t.id, t.driver_name, t.scheduler_telephone, t.identity_card, t.wlhy_vehicle_structure, t.scheduler_vehicle_structure, t.excluded_vehicle_structure, t.wlhy_vehicle_length, t.scheduler_vehicle_length, t.excluded_vehicle_length, t.wlhy_goods, t.scheduler_goods, t.excluded_goods, t.wlhy_haulway, t.scheduler_haulway, t.excluded_haulway, tag.content, t.service_count, t.cooperative_status, t.blacklist_status, t.overall_evaluate, t.state, t.blacken_reason, t.create_by, t.create_time, t.update_by, t.update_time from scheduler_driver_profile t
    </sql>

    <select id="selectSchedulerDriverProfileList" parameterType="SchedulerDriverProfileReq" resultMap="SchedulerDriverProfileResult">
        <include refid="selectSchedulerDriverProfileVo"/>
        LEFT JOIN scheduler_driver_evaluate_tag tag ON t.id = tag.driver_profile_id AND tag.type = 2
        <where>  
            <if test="id != null "> and t.id = #{id}</if>
            <if test="driverId != null  and driverId != ''"> and t.driver_id = #{driverId}</if>
            <if test="driverName != null  and driverName != ''"> and t.driver_name = #{driverName}</if>
            <if test="telephone != null  and telephone != ''"> and t.telephone = #{telephone}</if>
            <choose>
            	<when test="telephoneState == 0">
					 and t.scheduler_telephone = ''
            	</when>
            	<otherwise>
            		<if test="schedulerTelephone != null  and schedulerTelephone != ''"> and t.scheduler_telephone = #{schedulerTelephone}</if>
            	</otherwise>
            </choose>
            <if test="identityCard != null  and identityCard != ''"> and t.identity_card = #{identityCard}</if>
            <if test="drivingYears != null "> and t.driving_years = #{drivingYears}</if>
            <if test="profileCompleteness != null "> and t.profile_completeness = #{profileCompleteness}</if>
            <if test="serviceCount != null "> and t.service_count = #{serviceCount}</if>
            <if test="totalCourse != null  and totalCourse != ''"> and t.total_course = #{totalCourse}</if>
            <if test="operation != null "> and t.operation = #{operation}</if>
            <if test="operationDesc != null  and operationDesc != ''"> and t.operation_desc = #{operationDesc}</if>
            <if test="isTheDrivrLicenseValid != null "> and t.is_the_drivr_license_valid = #{isTheDrivrLicenseValid}</if>
            <if test="vehicleClass != null  and vehicleClass != ''"> and t.vehicle_class = #{vehicleClass}</if>
            <if test="validPeriodTo != null  and validPeriodTo != ''"> and t.valid_period_to = #{validPeriodTo}</if>
            <if test="qualificationCertificate != null  and qualificationCertificate != ''"> and t.qualification_certificate = #{qualificationCertificate}</if>
            <if test="isTheQualificationValid != null "> and t.is_the_qualification_valid = #{isTheQualificationValid}</if>
            <if test="actualCarrierEnd != null  and actualCarrierEnd != ''"> and t.actual_carrier_end = #{actualCarrierEnd}</if>
            <if test="lastServiceTime != null "> and t.last_service_time = #{lastServiceTime}</if>
            <if test="state != null "> and t.state = #{state}</if>
            <if test="address != null  and address != ''"> and t.address = #{address}</if>
            <if test="vehicles != null  and vehicles != ''"> and t.vehicles = #{vehicles}</if>
            <if test="longDistanceRoutes != null  and longDistanceRoutes != ''"> and t.long_distance_routes = #{longDistanceRoutes}</if>
            <if test="cooperativeStatus != null "> and t.cooperative_status = #{cooperativeStatus}</if>
            <if test="blacklistStatus != null "> and t.blacklist_status = #{blacklistStatus}</if>
            <if test="overallEvaluate != null  and overallEvaluate != ''"> and t.overall_evaluate = #{overallEvaluate}</if>
            <if test="schedulerVehicleStructure != null  and schedulerVehicleStructure != ''"> and (JSON_CONTAINS(t.wlhy_vehicle_structure, '"${schedulerVehicleStructure}"') or JSON_CONTAINS(t.scheduler_vehicle_structure, '"${schedulerVehicleStructure}"'))</if>
            <if test="wlhyVehicleStructure != null  and wlhyVehicleStructure != ''"> and JSON_CONTAINS(t.wlhy_vehicle_structure, '"${wlhyVehicleStructure}"')</if>
            <if test="excludedVehicleStructure != null  and excludedVehicleStructure != ''"> and JSON_CONTAINS(t.excluded_vehicle_structure, '"${excludedVehicleStructure}"')</if>
            <if test="schedulerVehicleLength != null  and schedulerVehicleLength != ''"> and (JSON_CONTAINS(t.wlhy_vehicle_length, '"${schedulerVehicleLength}"') or JSON_CONTAINS(t.scheduler_vehicle_length, '"${schedulerVehicleLength}"'))</if>
            <if test="wlhyVehicleLength != null  and wlhyVehicleLength != ''"> and JSON_CONTAINS(t.wlhy_vehicle_length, '"${wlhyVehicleLength}"')</if>
            <if test="excludedVehicleLength != null  and excludedVehicleLength != ''"> and JSON_CONTAINS(t.excluded_vehicle_length, '"${excludedVehicleLength}"')</if>
            <if test="schedulerGoods != null  and schedulerGoods != ''"> and (JSON_CONTAINS(t.wlhy_goods, '"${schedulerGoods}"') or JSON_CONTAINS(t.scheduler_goods, '"${schedulerGoods}"'))</if>
            <if test="wlhyGoods != null  and wlhyGoods != ''"> and JSON_CONTAINS(t.wlhy_goods, '"${wlhyGoods}"')</if>
            <if test="excludedGoods != null  and excludedGoods != ''"> and JSON_CONTAINS(t.excluded_goods, '"${excludedGoods}"')</if>
            <choose>
            	<when test="schedulerHaulwayStart != null and schedulerHaulwayStart != '' and (schedulerHaulwayEnd == null or schedulerHaulwayEnd == '')">
					and (JSON_UNQUOTE(t.wlhy_haulway) REGEXP CONCAT('"([^"-]*', REPLACE(#{schedulerHaulwayStart}, '"', '\\"'), '[^"-]*)-') or JSON_UNQUOTE(t.scheduler_haulway) REGEXP CONCAT('"([^"-]*', REPLACE(#{schedulerHaulwayStart}, '"', '\\"'), '[^"-]*)-'))
            	</when>
            	<when test="schedulerHaulwayEnd != null and schedulerHaulwayEnd != '' and (schedulerHaulwayStart == null or schedulerHaulwayStart == '')">
            		and (JSON_UNQUOTE(t.wlhy_haulway) REGEXP CONCAT('"([^"]*)-([^"-]*', REPLACE(#{schedulerHaulwayEnd}, '"', '\\"'), '[^"-]*)"') or JSON_UNQUOTE(t.scheduler_haulway) REGEXP CONCAT('"([^"]*)-([^"-]*', REPLACE(#{schedulerHaulwayEnd}, '"', '\\"'), '[^"-]*)"'))
            	</when>
            	<otherwise>
            		<if test="schedulerHaulwayStart != null and schedulerHaulwayStart != '' and schedulerHaulwayEnd != null and schedulerHaulwayEnd != ''">
            			and (JSON_SEARCH(t.wlhy_haulway, 'all', CONCAT(#{schedulerHaulwayStart}, '%'), NULL, '$[*]') IS NOT NULL or JSON_SEARCH(t.scheduler_haulway, 'all', CONCAT(#{schedulerHaulwayStart}, '%'), NULL, '$[*]') IS NOT NULL)
            			and (JSON_SEARCH(t.wlhy_haulway, 'all', CONCAT('%-', #{schedulerHaulwayEnd}, '%'), NULL, '$[*]') IS NOT NULL or JSON_SEARCH(t.scheduler_haulway, 'all', CONCAT('%-', #{schedulerHaulwayEnd}, '%'), NULL, '$[*]') IS NOT NULL)
            		</if>
            	</otherwise>
            </choose>
            <if test="wlhyHaulway != null  and wlhyHaulway != ''"> and JSON_CONTAINS(t.wlhy_haulway, '"${wlhyHaulway}"')</if>
            <if test="excludedHaulway != null  and excludedHaulway != ''"> and JSON_CONTAINS(t.excluded_haulway, '"${excludedHaulway}"')</if>
            <if test="blackenReason != null  and blackenReason != ''"> and t.blacken_reason = #{blackenReason}</if>
            <if test="changeTelReason != null  and changeTelReason != ''"> and t.change_tel_reason = #{changeTelReason}</if>
            <if test="remark != null  and remark != ''"> and t.remark = #{remark}</if>
            <if test="driverRating != null  and driverRating != ''"> and t.driver_rating = #{driverRating}</if>
            <if test="driverTag != null  and driverTag != ''"> and t.driver_tag = #{driverTag}</if>
            <if test="createBy != null  and createBy != ''"> and t.create_by = #{createBy}</if>
            <if test="createTime != null "> and t.create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and t.update_by = #{updateBy}</if>
            <if test="updateTime != null "> and t.update_time = #{updateTime}</if>
            <if test="deleted != null "> and t.deleted = #{deleted}</if>
        </where>
        order by t.last_service_time desc
    </select>
    
    <select id="selectRepeatedSchedulerDriverProfileList" parameterType="SchedulerDriverProfileReq" resultMap="SchedulerDriverProfileResult">
        <include refid="selectSchedulerDriverProfileVo"/>
        INNER JOIN (
		    SELECT scheduler_telephone
		    FROM scheduler_driver_profile FORCE INDEX (idx_phone_blacklist)
		    <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="driverId != null  and driverId != ''"> and driver_id = #{driverId}</if>
            <if test="driverName != null  and driverName != ''"> and driver_name = #{driverName}</if>
            <if test="telephone != null  and telephone != ''"> and telephone = #{telephone}</if>
            <choose>
            	<when test="schedulerTelephone == null or schedulerTelephone == ''">
					 and scheduler_telephone <![CDATA[<>]]> ''
            	</when>
            	<otherwise>
            		<if test="schedulerTelephone != null  and schedulerTelephone != ''"> and scheduler_telephone = #{schedulerTelephone}</if>
            	</otherwise>
            </choose>
            <if test="identityCard != null  and identityCard != ''"> and identity_card = #{identityCard}</if>
            <if test="drivingYears != null "> and driving_years = #{drivingYears}</if>
            <if test="profileCompleteness != null "> and profile_completeness = #{profileCompleteness}</if>
            <if test="serviceCount != null "> and service_count = #{serviceCount}</if>
            <if test="totalCourse != null  and totalCourse != ''"> and total_course = #{totalCourse}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="operationDesc != null  and operationDesc != ''"> and operation_desc = #{operationDesc}</if>
            <if test="isTheDrivrLicenseValid != null "> and is_the_drivr_license_valid = #{isTheDrivrLicenseValid}</if>
            <if test="vehicleClass != null  and vehicleClass != ''"> and vehicle_class = #{vehicleClass}</if>
            <if test="validPeriodTo != null  and validPeriodTo != ''"> and valid_period_to = #{validPeriodTo}</if>
            <if test="qualificationCertificate != null  and qualificationCertificate != ''"> and qualification_certificate = #{qualificationCertificate}</if>
            <if test="isTheQualificationValid != null "> and is_the_qualification_valid = #{isTheQualificationValid}</if>
            <if test="actualCarrierEnd != null  and actualCarrierEnd != ''"> and actual_carrier_end = #{actualCarrierEnd}</if>
            <if test="lastServiceTime != null "> and last_service_time = #{lastServiceTime}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="vehicles != null  and vehicles != ''"> and vehicles = #{vehicles}</if>
            <if test="longDistanceRoutes != null  and longDistanceRoutes != ''"> and long_distance_routes = #{longDistanceRoutes}</if>
            <if test="cooperativeStatus != null "> and cooperative_status = #{cooperativeStatus}</if>
            <if test="blacklistStatus != null "> and blacklist_status = #{blacklistStatus}</if>
            <if test="overallEvaluate != null  and overallEvaluate != ''"> and overall_evaluate = #{overallEvaluate}</if>
            <if test="schedulerVehicleStructure != null  and schedulerVehicleStructure != ''"> and (JSON_CONTAINS(wlhy_vehicle_structure, '"${schedulerVehicleStructure}"') or JSON_CONTAINS(scheduler_vehicle_structure, '"${schedulerVehicleStructure}"'))</if>
            <if test="wlhyVehicleStructure != null  and wlhyVehicleStructure != ''"> and JSON_CONTAINS(wlhy_vehicle_structure, '"${wlhyVehicleStructure}"')</if>
            <if test="excludedVehicleStructure != null  and excludedVehicleStructure != ''"> and JSON_CONTAINS(excluded_vehicle_structure, '"${excludedVehicleStructure}"')</if>
            <if test="schedulerVehicleLength != null  and schedulerVehicleLength != ''"> and (JSON_CONTAINS(wlhy_vehicle_length, '"${schedulerVehicleLength}"') or JSON_CONTAINS(scheduler_vehicle_length, '"${schedulerVehicleLength}"'))</if>
            <if test="wlhyVehicleLength != null  and wlhyVehicleLength != ''"> and JSON_CONTAINS(wlhy_vehicle_length, '"${wlhyVehicleLength}"')</if>
            <if test="excludedVehicleLength != null  and excludedVehicleLength != ''"> and JSON_CONTAINS(excluded_vehicle_length, '"${excludedVehicleLength}"')</if>
            <if test="schedulerGoods != null  and schedulerGoods != ''"> and (JSON_CONTAINS(wlhy_goods, '"${schedulerGoods}"') or JSON_CONTAINS(scheduler_goods, '"${schedulerGoods}"'))</if>
            <if test="wlhyGoods != null  and wlhyGoods != ''"> and JSON_CONTAINS(wlhy_goods, '"${wlhyGoods}"')</if>
            <if test="excludedGoods != null  and excludedGoods != ''"> and JSON_CONTAINS(excluded_goods, '"${excludedGoods}"')</if>
            <choose>
            	<when test="schedulerHaulwayStart != null and schedulerHaulwayStart != '' and (schedulerHaulwayEnd == null or schedulerHaulwayEnd == '')">
					and (JSON_UNQUOTE(wlhy_haulway) REGEXP CONCAT('"([^"-]*', REPLACE(#{schedulerHaulwayStart}, '"', '\\"'), '[^"-]*)-') or JSON_UNQUOTE(scheduler_haulway) REGEXP CONCAT('"([^"-]*', REPLACE(#{schedulerHaulwayStart}, '"', '\\"'), '[^"-]*)-'))
            	</when>
            	<when test="schedulerHaulwayEnd != null and schedulerHaulwayEnd != '' and (schedulerHaulwayStart == null or schedulerHaulwayStart == '')">
            		and (JSON_UNQUOTE(wlhy_haulway) REGEXP CONCAT('"([^"]*)-([^"-]*', REPLACE(#{schedulerHaulwayEnd}, '"', '\\"'), '[^"-]*)"') or JSON_UNQUOTE(scheduler_haulway) REGEXP CONCAT('"([^"]*)-([^"-]*', REPLACE(#{schedulerHaulwayEnd}, '"', '\\"'), '[^"-]*)"'))
            	</when>
            	<otherwise>
            		<if test="schedulerHaulwayStart != null and schedulerHaulwayStart != '' and schedulerHaulwayEnd != null and schedulerHaulwayEnd != ''">
            			and (JSON_SEARCH(wlhy_haulway, 'all', CONCAT(#{schedulerHaulwayStart}, '%'), NULL, '$[*]') IS NOT NULL or JSON_SEARCH(scheduler_haulway, 'all', CONCAT(#{schedulerHaulwayStart}, '%'), NULL, '$[*]') IS NOT NULL)
            			and (JSON_SEARCH(wlhy_haulway, 'all', CONCAT('%-', #{schedulerHaulwayEnd}, '%'), NULL, '$[*]') IS NOT NULL or JSON_SEARCH(scheduler_haulway, 'all', CONCAT('%-', #{schedulerHaulwayEnd}, '%'), NULL, '$[*]') IS NOT NULL)
            		</if>
            	</otherwise>
            </choose>
            <if test="wlhyHaulway != null  and wlhyHaulway != ''"> and JSON_CONTAINS(wlhy_haulway, '"${wlhyHaulway}"')</if>
            <if test="excludedHaulway != null  and excludedHaulway != ''"> and JSON_CONTAINS(excluded_haulway, '"${excludedHaulway}"')</if>
            <if test="blackenReason != null  and blackenReason != ''"> and blacken_reason = #{blackenReason}</if>
            <if test="changeTelReason != null  and changeTelReason != ''"> and change_tel_reason = #{changeTelReason}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
            <if test="driverRating != null  and driverRating != ''"> and driver_rating = #{driverRating}</if>
            <if test="driverTag != null  and driverTag != ''"> and driver_tag = #{driverTag}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="deleted != null "> and deleted = #{deleted}</if>
        </where>
			GROUP BY scheduler_telephone HAVING COUNT(*) > 1
		) t1 ON t.scheduler_telephone = t1.scheduler_telephone
		LEFT JOIN scheduler_driver_evaluate_tag tag ON t.id = tag.driver_profile_id AND tag.type = 2
        order by t.scheduler_telephone,t.last_service_time desc
    </select>

    <update id="updateSchedulerDriverProfile" parameterType="SchedulerDriverProfile">
        update scheduler_driver_profile
        <trim prefix="SET" suffixOverrides=",">
            <if test="driverId != null and driverId != ''">driver_id = #{driverId},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <choose>
            	<when test="changeTelReason != null and changeTelReason != '' and schedulerTelephone != null and schedulerTelephone != '' and schedulerTelephone == '-1'">
            		scheduler_telephone = '',
            	</when>
            	<when test="changeTelReason != null and changeTelReason != '' and schedulerTelephone != null and schedulerTelephone != '' and schedulerTelephone != '-1'">
            		scheduler_telephone = #{schedulerTelephone},
            	</when>
            	<otherwise>
            		<if test="schedulerTelephone != null and schedulerTelephone != '' and schedulerTelephone != '-1'">scheduler_telephone = #{schedulerTelephone},</if>
            	</otherwise>
            </choose>
            <if test="identityCard != null">identity_card = #{identityCard},</if>
            <if test="drivingYears != null">driving_years = #{drivingYears},</if>
            <if test="profileCompleteness != null">profile_completeness = #{profileCompleteness},</if>
            <if test="serviceCount != null">service_count = #{serviceCount},</if>
            <if test="totalCourse != null">total_course = #{totalCourse},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="operationDesc != null">operation_desc = #{operationDesc},</if>
            <if test="isTheDrivrLicenseValid != null">is_the_drivr_license_valid = #{isTheDrivrLicenseValid},</if>
            <if test="vehicleClass != null">vehicle_class = #{vehicleClass},</if>
            <if test="validPeriodTo != null">valid_period_to = #{validPeriodTo},</if>
            <if test="qualificationCertificate != null">qualification_certificate = #{qualificationCertificate},</if>
            <if test="isTheQualificationValid != null">is_the_qualification_valid = #{isTheQualificationValid},</if>
            <if test="actualCarrierEnd != null">actual_carrier_end = #{actualCarrierEnd},</if>
            <if test="lastServiceTime != null">last_service_time = #{lastServiceTime},</if>
            <if test="state != null">state = #{state},</if>
            <if test="address != null">address = #{address},</if>
            <if test="vehicles != null">vehicles = #{vehicles},</if>
            <if test="longDistanceRoutes != null">long_distance_routes = #{longDistanceRoutes},</if>
            <if test="cooperativeStatus != null">cooperative_status = #{cooperativeStatus},</if>
            <if test="blacklistStatus != null">blacklist_status = #{blacklistStatus},</if>
            <if test="overallEvaluate != null and overallEvaluate != ''">overall_evaluate = #{overallEvaluate},</if>
            <if test="schedulerVehicleStructure != null">scheduler_vehicle_structure = #{schedulerVehicleStructure},</if>
            <if test="wlhyVehicleStructure != null">wlhy_vehicle_structure = #{wlhyVehicleStructure},</if>
            <if test="excludedVehicleStructure != null">excluded_vehicle_structure = #{excludedVehicleStructure},</if>
            <if test="schedulerVehicleLength != null">scheduler_vehicle_length = #{schedulerVehicleLength},</if>
            <if test="wlhyVehicleLength != null">wlhy_vehicle_length = #{wlhyVehicleLength},</if>
            <if test="excludedVehicleLength != null">excluded_vehicle_length = #{excludedVehicleLength},</if>
            <if test="schedulerGoods != null">scheduler_goods = #{schedulerGoods},</if>
            <if test="wlhyGoods != null">wlhy_goods = #{wlhyGoods},</if>
            <if test="excludedGoods != null">excluded_goods = #{excludedGoods},</if>
            <if test="schedulerHaulway != null">scheduler_haulway = #{schedulerHaulway},</if>
            <if test="wlhyHaulway != null">wlhy_haulway = #{wlhyHaulway},</if>
            <if test="excludedHaulway != null">excluded_haulway = #{excludedHaulway},</if>
            <if test="blackenReason != null and blackenReason != ''">blacken_reason = #{blackenReason},</if>
            <if test="changeTelReason != null and changeTelReason != ''">change_tel_reason = #{changeTelReason},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="driverRating != null">driver_rating = #{driverRating},</if>
            <if test="driverTag != null">driver_tag = #{driverTag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>