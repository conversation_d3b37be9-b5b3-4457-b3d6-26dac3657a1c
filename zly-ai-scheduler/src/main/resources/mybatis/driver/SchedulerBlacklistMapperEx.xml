<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.driver.mapper.SchedulerBlacklistMapperEx">

	<delete id="deleteSchedulerBlacklistByBlackMembers" parameterType="String">
		delete from scheduler_blacklist where black_member in
		<foreach collection="blackMembers" item="blackMember" open="(" separator="," close=")">
			#{blackMember}
		</foreach>
	</delete>

	<insert id="insertBatchSchedulerBlacklist">
		INSERT INTO scheduler_blacklist (id,black_member,create_by,update_by) VALUES
		<foreach collection="schedulerBlacklist" item="schedulerBlack" separator=",">
			(#{schedulerBlack.id}, #{schedulerBlack.blackMember}, #{schedulerBlack.createBy}, #{schedulerBlack.updateBy})
		</foreach>
	</insert>
</mapper>