<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.driver.mapper.SchedulerBlacklistMapper">
    
    <resultMap type="SchedulerBlacklist" id="SchedulerBlacklistResult">
        <result property="id"    column="id"    />
        <result property="blackMember"    column="black_member"    />
        <result property="type"    column="type"    />
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSchedulerBlacklistVo">
        select id, black_member, type, state, create_by, create_time, update_by, update_time from scheduler_blacklist
    </sql>

    <select id="selectSchedulerBlacklistList" parameterType="SchedulerBlacklist" resultMap="SchedulerBlacklistResult">
        <include refid="selectSchedulerBlacklistVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="blackMember != null  and blackMember != ''"> and black_member = #{blackMember}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''"> and update_time between #{params.beginUpdateTime} and #{params.endUpdateTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSchedulerBlacklistById" parameterType="Long" resultMap="SchedulerBlacklistResult">
        <include refid="selectSchedulerBlacklistVo"/>
        where id = #{id}
    </select>

    <insert id="insertSchedulerBlacklist" parameterType="SchedulerBlacklist">
        insert into scheduler_blacklist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="blackMember != null and blackMember != ''">black_member,</if>
            <if test="type != null">type,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="blackMember != null and blackMember != ''">#{blackMember},</if>
            <if test="type != null">#{type},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSchedulerBlacklist" parameterType="SchedulerBlacklist">
        update scheduler_blacklist
        <trim prefix="SET" suffixOverrides=",">
            <if test="blackMember != null and blackMember != ''">black_member = #{blackMember},</if>
            <if test="type != null">type = #{type},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSchedulerBlacklistById" parameterType="Long">
        delete from scheduler_blacklist where id = #{id}
    </delete>

    <delete id="deleteSchedulerBlacklistByIds" parameterType="String">
        delete from scheduler_blacklist where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>