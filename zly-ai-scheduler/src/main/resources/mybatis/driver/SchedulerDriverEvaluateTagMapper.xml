<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.driver.mapper.SchedulerDriverEvaluateTagMapper">
    
    <resultMap type="SchedulerDriverEvaluateTag" id="SchedulerDriverEvaluateTagResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="operation"    column="operation"    />
        <result property="driverProfileId"    column="driver_profile_id"    />
        <result property="content"    column="content"    />
        <result property="state"    column="state"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSchedulerDriverEvaluateTagVo">
        select id, type, operation, driver_profile_id, content, state, remark, create_by, create_time, update_by, update_time from scheduler_driver_evaluate_tag
    </sql>

    <select id="selectSchedulerDriverEvaluateTagList" parameterType="SchedulerDriverEvaluateTag" resultMap="SchedulerDriverEvaluateTagResult">
        <include refid="selectSchedulerDriverEvaluateTagVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="driverProfileId != null "> and driver_profile_id = #{driverProfileId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
        order by update_time desc
    </select>
    
    <select id="selectSchedulerDriverEvaluateTagById" parameterType="Long" resultMap="SchedulerDriverEvaluateTagResult">
        <include refid="selectSchedulerDriverEvaluateTagVo"/>
        where id = #{id}
    </select>

    <insert id="insertSchedulerDriverEvaluateTag" parameterType="SchedulerDriverEvaluateTag">
        insert into scheduler_driver_evaluate_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="operation != null">operation,</if>
            <if test="driverProfileId != null">driver_profile_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="state != null">state,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="operation != null">#{operation},</if>
            <if test="driverProfileId != null">#{driverProfileId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="state != null">#{state},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSchedulerDriverEvaluateTag" parameterType="SchedulerDriverEvaluateTag">
        update scheduler_driver_evaluate_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="driverProfileId != null">driver_profile_id = #{driverProfileId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSchedulerDriverEvaluateTagById" parameterType="Long">
        delete from scheduler_driver_evaluate_tag where id = #{id}
    </delete>

    <delete id="deleteSchedulerDriverEvaluateTagByIds" parameterType="String">
        delete from scheduler_driver_evaluate_tag where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>