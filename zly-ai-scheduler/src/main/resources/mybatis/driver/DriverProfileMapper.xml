<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.driver.mapper.DriverProfileMapper">
    
    <resultMap type="com.zly.project.driver.domain.DriverProfileDO" id="DriverProfileResult">
        <id     property="id"              column="id"                />
        <result property="driverId"        column="driver_id"         />
        <result property="driverName"      column="driver_name"       />
        <result property="telephone"       column="telephone"         />
        <result property="schedulerTelephone"    column="scheduler_telephone"    />
        <result property="identityCard"    column="identity_card"     />
        <result property="drivingYears"    column="driving_years"     />
        <result property="profileCompleteness" column="profile_completeness" />
        <result property="serviceCount"    column="service_count"     />
        <result property="totalCourse"     column="total_course"      />
        <result property="operation"       column="operation"         />
        <result property="operationDesc"   column="operation_desc"    />
        <result property="isTheDrivrLicenseValid" column="is_the_drivr_license_valid" />
        <result property="vehicleClass"    column="vehicle_class"     />
        <result property="validPeriodTo"   column="valid_period_to"   />
        <result property="qualificationCertificate" column="qualification_certificate" />
        <result property="isTheQualificationValid" column="is_the_qualification_valid" />
        <result property="actualCarrierEnd" column="actual_carrier_end" />
        <result property="lastServiceTime" column="last_service_time" />
        <result property="state"           column="state"             />
        <result property="address"         column="address"           />
        <result property="vehicles"        column="vehicles"          />
        <result property="longDistanceRoutes" column="long_distance_routes" />
        <result property="cooperativeStatus"    column="cooperative_status"    />
        <result property="blacklistStatus"    column="blacklist_status"    />
        <result property="overallEvaluate"    column="overall_evaluate"    />
        <result property="schedulerVehicleStructure"    column="scheduler_vehicle_structure"    />
        <result property="wlhyVehicleStructure"    column="wlhy_vehicle_structure"    />
        <result property="excludedVehicleStructure"    column="excluded_vehicle_structure"    />
        <result property="schedulerVehicleLength"    column="scheduler_vehicle_length"    />
        <result property="wlhyVehicleLength"    column="wlhy_vehicle_length"    />
        <result property="excludedVehicleLength"    column="excluded_vehicle_length"    />
        <result property="schedulerGoods"    column="scheduler_goods"    />
        <result property="wlhyGoods"    column="wlhy_goods"    />
        <result property="excludedGoods"    column="excluded_goods"    />
        <result property="schedulerHaulway"    column="scheduler_haulway"    />
        <result property="wlhyHaulway"    column="wlhy_haulway"    />
        <result property="excludedHaulway"    column="excluded_haulway"    />
        <result property="goodsWeight"       column="goods_weight"        />
        <result property="goodsVolume"       column="goods_volume"        />
        <result property="blackenReason"    column="blacken_reason"    />
        <result property="changeTelReason"    column="change_tel_reason"    />
        <result property="remark"    column="remark"    />
        <result property="driverRating"    column="driver_rating"     />
        <result property="driverTag"       column="driver_tag"        />
        <result property="createBy"        column="create_by"         />
        <result property="createTime"      column="create_time"       />
        <result property="updateBy"       column="update_by"        />
        <result property="updateTime"      column="update_time"       />
        <result property="deleted"         column="deleted"           />
    </resultMap>

    <sql id="selectDriverProfileVo">
        select id, driver_id, driver_name, telephone, scheduler_telephone, identity_card, driving_years, profile_completeness, service_count, total_course, operation, operation_desc, is_the_drivr_license_valid, vehicle_class, valid_period_to, qualification_certificate, is_the_qualification_valid, actual_carrier_end, last_service_time, state, address, vehicles, long_distance_routes, cooperative_status, blacklist_status, overall_evaluate, scheduler_vehicle_structure, wlhy_vehicle_structure, excluded_vehicle_structure, scheduler_vehicle_length, wlhy_vehicle_length, excluded_vehicle_length, scheduler_goods, wlhy_goods, excluded_goods, scheduler_haulway, wlhy_haulway, excluded_haulway,goods_weight,goods_volume, blacken_reason, change_tel_reason, remark, driver_rating, driver_tag, create_by, create_time, update_by, update_time, deleted from scheduler_driver_profile
    </sql>

    <!-- 根据司机ID查询司机画像 -->
    <select id="selectByDriverId" parameterType="String" resultMap="DriverProfileResult">
        <include refid="selectDriverProfileVo"/>
        where driver_id = #{driverId} and deleted = 0
    </select>

    <select id="selectById" resultMap="DriverProfileResult">
        <include refid="selectDriverProfileVo"/>
        where id = #{id} and deleted = 0
    </select>
    
    <!-- 查询所有司机画像 -->
    <select id="selectAllDriverList" resultMap="DriverProfileResult">
        <include refid="selectDriverProfileVo"/>
        where deleted = 0
    </select>
    
    <!-- 根据司机ID列表批量查询司机画像 -->
    <select id="selectListByDriverIds" parameterType="java.util.Collection" resultMap="DriverProfileResult">
        <include refid="selectDriverProfileVo"/>
        where driver_id in
        <foreach item="driverId" collection="collection" open="(" separator="," close=")">
            #{driverId}
        </foreach>
        and deleted = 0
    </select>
    
    <!-- 新增司机画像 -->
    <insert id="insertDriverProfile" parameterType="com.zly.project.driver.domain.DriverProfileDO" useGeneratedKeys="true" keyProperty="id">
        insert into scheduler_driver_profile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="driverId != null and driverId != ''">driver_id,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="telephone != null">telephone,</if>
            <if test="schedulerTelephone != null and schedulerTelephone != ''">scheduler_telephone,</if>
            <if test="identityCard != null">identity_card,</if>
            <if test="drivingYears != null">driving_years,</if>
            <if test="profileCompleteness != null">profile_completeness,</if>
            <if test="serviceCount != null">service_count,</if>
            <if test="totalCourse != null">total_course,</if>
            <if test="operation != null">operation,</if>
            <if test="operationDesc != null">operation_desc,</if>
            <if test="isTheDrivrLicenseValid != null">is_the_drivr_license_valid,</if>
            <if test="vehicleClass != null">vehicle_class,</if>
            <if test="validPeriodTo != null">valid_period_to,</if>
            <if test="qualificationCertificate != null">qualification_certificate,</if>
            <if test="isTheQualificationValid != null">is_the_qualification_valid,</if>
            <if test="actualCarrierEnd != null">actual_carrier_end,</if>
            <if test="lastServiceTime != null">last_service_time,</if>
            <if test="state != null">state,</if>
            <if test="address != null">address,</if>
            <if test="vehicles != null">vehicles,</if>
            <if test="longDistanceRoutes != null">long_distance_routes,</if>
            <if test="cooperativeStatus != null">cooperative_status,</if>
            <if test="blacklistStatus != null">blacklist_status,</if>
            <if test="overallEvaluate != null and overallEvaluate != ''">overall_evaluate,</if>
            <if test="schedulerVehicleStructure != null">scheduler_vehicle_structure,</if>
            <if test="wlhyVehicleStructure != null">wlhy_vehicle_structure,</if>
            <if test="excludedVehicleStructure != null">excluded_vehicle_structure,</if>
            <if test="schedulerVehicleLength != null">scheduler_vehicle_length,</if>
            <if test="wlhyVehicleLength != null">wlhy_vehicle_length,</if>
            <if test="excludedVehicleLength != null">excluded_vehicle_length,</if>
            <if test="schedulerGoods != null">scheduler_goods,</if>
            <if test="wlhyGoods != null">wlhy_goods,</if>
            <if test="excludedGoods != null">excluded_goods,</if>
            <if test="schedulerHaulway != null">scheduler_haulway,</if>
            <if test="wlhyHaulway != null">wlhy_haulway,</if>
            <if test="excludedHaulway != null">excluded_haulway,</if>
            <if test="goodsWeight != null">goods_weight,</if>
            <if test="goodsVolume != null">goods_volume,</if>
            <if test="blackenReason != null and blackenReason != ''">blacken_reason,</if>
            <if test="changeTelReason != null and changeTelReason != ''">change_tel_reason,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="driverRating != null">driver_rating,</if>
            <if test="driverTag != null">driver_tag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="driverId != null and driverId != ''">#{driverId},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="telephone != null">#{telephone},</if>
            <if test="schedulerTelephone != null and schedulerTelephone != ''">#{schedulerTelephone},</if>
            <if test="identityCard != null">#{identityCard},</if>
            <if test="drivingYears != null">#{drivingYears},</if>
            <if test="profileCompleteness != null">#{profileCompleteness},</if>
            <if test="serviceCount != null">#{serviceCount},</if>
            <if test="totalCourse != null">#{totalCourse},</if>
            <if test="operation != null">#{operation},</if>
            <if test="operationDesc != null">#{operationDesc},</if>
            <if test="isTheDrivrLicenseValid != null">#{isTheDrivrLicenseValid},</if>
            <if test="vehicleClass != null">#{vehicleClass},</if>
            <if test="validPeriodTo != null">#{validPeriodTo},</if>
            <if test="qualificationCertificate != null">#{qualificationCertificate},</if>
            <if test="isTheQualificationValid != null">#{isTheQualificationValid},</if>
            <if test="actualCarrierEnd != null">#{actualCarrierEnd},</if>
            <if test="lastServiceTime != null">#{lastServiceTime},</if>
            <if test="state != null">#{state},</if>
            <if test="address != null">#{address},</if>
            <if test="vehicles != null">#{vehicles},</if>
            <if test="longDistanceRoutes != null">#{longDistanceRoutes},</if>
            <if test="cooperativeStatus != null">#{cooperativeStatus},</if>
            <if test="blacklistStatus != null">#{blacklistStatus},</if>
            <if test="overallEvaluate != null and overallEvaluate != ''">#{overallEvaluate},</if>
            <if test="schedulerVehicleStructure != null">#{schedulerVehicleStructure},</if>
            <if test="wlhyVehicleStructure != null">#{wlhyVehicleStructure},</if>
            <if test="excludedVehicleStructure != null">#{excludedVehicleStructure},</if>
            <if test="schedulerVehicleLength != null">#{schedulerVehicleLength},</if>
            <if test="wlhyVehicleLength != null">#{wlhyVehicleLength},</if>
            <if test="excludedVehicleLength != null">#{excludedVehicleLength},</if>
            <if test="schedulerGoods != null">#{schedulerGoods},</if>
            <if test="wlhyGoods != null">#{wlhyGoods},</if>
            <if test="excludedGoods != null">#{excludedGoods},</if>
            <if test="schedulerHaulway != null">#{schedulerHaulway},</if>
            <if test="wlhyHaulway != null">#{wlhyHaulway},</if>
            <if test="excludedHaulway != null">#{excludedHaulway},</if>
            <if test="goodsWeight != null">#{goodsWeight},</if>
            <if test="goodsVolume != null">#{goodsVolume},</if>
            <if test="blackenReason != null and blackenReason != ''">#{blackenReason},</if>
            <if test="changeTelReason != null and changeTelReason != ''">#{changeTelReason},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="driverRating != null">#{driverRating},</if>
            <if test="driverTag != null">#{driverTag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
         </trim>
    </insert>

    <!-- 修改司机画像 -->
    <update id="updateDriverProfile" parameterType="com.zly.project.driver.domain.DriverProfileDO">
        update scheduler_driver_profile
        <trim prefix="SET" suffixOverrides=",">
            <if test="driverId != null and driverId != ''">driver_id = #{driverId},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <if test="schedulerTelephone != null and schedulerTelephone != ''">scheduler_telephone = #{schedulerTelephone},</if>
            <if test="identityCard != null">identity_card = #{identityCard},</if>
            <if test="drivingYears != null">driving_years = #{drivingYears},</if>
            <if test="profileCompleteness != null">profile_completeness = #{profileCompleteness},</if>
            <if test="serviceCount != null">service_count = #{serviceCount},</if>
            <if test="totalCourse != null">total_course = #{totalCourse},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="operationDesc != null">operation_desc = #{operationDesc},</if>
            <if test="isTheDrivrLicenseValid != null">is_the_drivr_license_valid = #{isTheDrivrLicenseValid},</if>
            <if test="vehicleClass != null">vehicle_class = #{vehicleClass},</if>
            <if test="validPeriodTo != null">valid_period_to = #{validPeriodTo},</if>
            <if test="qualificationCertificate != null">qualification_certificate = #{qualificationCertificate},</if>
            <if test="isTheQualificationValid != null">is_the_qualification_valid = #{isTheQualificationValid},</if>
            <if test="actualCarrierEnd != null">actual_carrier_end = #{actualCarrierEnd},</if>
            <if test="lastServiceTime != null">last_service_time = #{lastServiceTime},</if>
            <if test="state != null">state = #{state},</if>
            <if test="address != null">address = #{address},</if>
            <if test="vehicles != null">vehicles = #{vehicles},</if>
            <if test="longDistanceRoutes != null">long_distance_routes = #{longDistanceRoutes},</if>
            <if test="cooperativeStatus != null">cooperative_status = #{cooperativeStatus},</if>
            <if test="blacklistStatus != null">blacklist_status = #{blacklistStatus},</if>
            <if test="overallEvaluate != null and overallEvaluate != ''">overall_evaluate = #{overallEvaluate},</if>
            <if test="schedulerVehicleStructure != null">scheduler_vehicle_structure = #{schedulerVehicleStructure},</if>
            <if test="wlhyVehicleStructure != null">wlhy_vehicle_structure = #{wlhyVehicleStructure},</if>
            <if test="excludedVehicleStructure != null">excluded_vehicle_structure = #{excludedVehicleStructure},</if>
            <if test="schedulerVehicleLength != null">scheduler_vehicle_length = #{schedulerVehicleLength},</if>
            <if test="wlhyVehicleLength != null">wlhy_vehicle_length = #{wlhyVehicleLength},</if>
            <if test="excludedVehicleLength != null">excluded_vehicle_length = #{excludedVehicleLength},</if>
            <if test="schedulerGoods != null">scheduler_goods = #{schedulerGoods},</if>
            <if test="wlhyGoods != null">wlhy_goods = #{wlhyGoods},</if>
            <if test="excludedGoods != null">excluded_goods = #{excludedGoods},</if>
            <if test="schedulerHaulway != null">scheduler_haulway = #{schedulerHaulway},</if>
            <if test="wlhyHaulway != null">wlhy_haulway = #{wlhyHaulway},</if>
            <if test="excludedHaulway != null">excluded_haulway = #{excludedHaulway},</if>
            <if test="goodsWeight != null">goods_weight = #{goodsWeight},</if>
            <if test="goodsVolume != null">goods_volume = #{goodsVolume},</if>
            <if test="blackenReason != null and blackenReason != ''">blacken_reason = #{blackenReason},</if>
            <if test="changeTelReason != null and changeTelReason != ''">change_tel_reason = #{changeTelReason},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="driverRating != null">driver_rating = #{driverRating},</if>
            <if test="driverTag != null">driver_tag = #{driverTag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where 
        <choose>
            <when test="id != null">
                id = #{id}
            </when>
            <otherwise>
                driver_id = #{driverId}
            </otherwise>
        </choose>
        and deleted = 0
    </update>

    <update id="updateDriverProfileBatch" parameterType="java.util.List">
        <if test="list != null and list.size() > 0">
            update scheduler_driver_profile
            <trim prefix="SET" suffixOverrides=",">
                <trim prefix="driver_name = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.driverName}
                    </foreach>
                    ELSE driver_name
                </trim>
                <trim prefix="telephone = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.telephone}
                    </foreach>
                    ELSE telephone
                </trim>
                <trim prefix="identity_card = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.identityCard}
                    </foreach>
                    ELSE identity_card
                </trim>
                <!--                <trim prefix="driving_years = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.drivingYears != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.drivingYears}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE driving_years-->
                <!--                </trim>-->

                <!--                <trim prefix="profile_completeness = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.profileCompleteness != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.profileCompleteness}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE profile_completeness-->
                <!--                </trim>-->

                <trim prefix="service_count = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.serviceCount}
                    </foreach>
                    ELSE service_count
                </trim>
                <trim prefix="total_course = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.totalCourse}
                    </foreach>
                    ELSE total_course
                </trim>
<!--                <trim prefix="operation = CASE" suffix="END,">-->
<!--                    <foreach collection="list" item="item">-->
<!--                        <if test="item.operation != null">-->
<!--                            WHEN id = #{item.id} THEN #{item.operation}-->
<!--                        </if>-->
<!--                    </foreach>-->
<!--                    ELSE operation-->
<!--                </trim>-->
<!--                <trim prefix="operation_desc = CASE" suffix="END,">-->
<!--                    <foreach collection="list" item="item">-->
<!--                        <if test="item.operationDesc != null">-->
<!--                            WHEN id = #{item.id} THEN #{item.operationDesc}-->
<!--                        </if>-->
<!--                    </foreach>-->
<!--                    ELSE operation_desc-->
<!--                </trim>-->

                <!--                <trim prefix="is_the_drivr_license_valid = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.isTheDrivrLicenseValid != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.isTheDrivrLicenseValid}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE is_the_drivr_license_valid-->
                <!--                </trim>-->

                <!--                <trim prefix="vehicle_class = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.vehicleClass != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.vehicleClass}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE vehicle_class-->
                <!--                </trim>-->

                <!--                <trim prefix="valid_period_to = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.validPeriodTo != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.validPeriodTo}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE valid_period_to-->
                <!--                </trim>-->

                <!--                <trim prefix="qualification_certificate = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.qualificationCertificate != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.qualificationCertificate}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE qualification_certificate-->
                <!--                </trim>-->

                <!--                <trim prefix="is_the_qualification_valid = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.isTheQualificationValid != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.isTheQualificationValid}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE is_the_qualification_valid-->
                <!--                </trim>-->

                <!--                <trim prefix="actual_carrier_end = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.actualCarrierEnd != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.actualCarrierEnd}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE actual_carrier_end-->
                <!--                </trim>-->

                <trim prefix="last_service_time = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.lastServiceTime}
                    </foreach>
                    ELSE last_service_time
                </trim>
                <trim prefix="state = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.state}
                    </foreach>
                    ELSE state
                </trim>

                <!--                <trim prefix="address = CASE" suffix="END,">-->
                <!--                    <foreach collection="list" item="item">-->
                <!--                        <if test="item.address != null">-->
                <!--                            WHEN id = #{item.id} THEN #{item.address}-->
                <!--                        </if>-->
                <!--                    </foreach>-->
                <!--                    ELSE address-->
                <!--                </trim>-->

                <trim prefix="vehicles = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.vehicles}
                    </foreach>
                    ELSE vehicles
                </trim>

                <trim prefix="long_distance_routes = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.longDistanceRoutes}
                    </foreach>
                    ELSE long_distance_routes
                </trim>

                <trim prefix="wlhy_goods = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.wlhyGoods,jdbcType=VARCHAR}
                    </foreach>
                    ELSE wlhy_goods
                </trim>

                <trim prefix="scheduler_goods = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.schedulerGoods,jdbcType=VARCHAR}
                    </foreach>
                    ELSE scheduler_goods
                </trim>

                <trim prefix="wlhy_haulway = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.wlhyHaulway,jdbcType=VARCHAR}
                    </foreach>
                    ELSE wlhy_haulway
                </trim>

                <trim prefix="scheduler_haulway = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.schedulerHaulway,jdbcType=VARCHAR}
                    </foreach>
                    ELSE scheduler_haulway
                </trim>

                <trim prefix="wlhy_vehicle_structure = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.wlhyVehicleStructure,jdbcType=VARCHAR}
                    </foreach>
                    ELSE wlhy_vehicle_structure
                </trim>

                <trim prefix="scheduler_vehicle_structure = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.schedulerVehicleStructure,jdbcType=VARCHAR}
                    </foreach>
                    ELSE scheduler_vehicle_structure
                </trim>

                <trim prefix="wlhy_vehicle_length = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.wlhyVehicleLength,jdbcType=VARCHAR}
                    </foreach>
                    ELSE wlhy_vehicle_length
                </trim>

                <trim prefix="scheduler_vehicle_length = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.schedulerVehicleLength,jdbcType=VARCHAR}
                    </foreach>
                    ELSE scheduler_vehicle_length
                </trim>

                <trim prefix="goods_weight = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.goodsWeight}
                    </foreach>
                    ELSE goods_weight
                </trim>

                <trim prefix="goods_volume = CASE" suffix="END,">
                    <foreach collection="list" item="item">
                        WHEN id = #{item.id} THEN #{item.goodsVolume}
                    </foreach>
                    ELSE goods_volume
                </trim>

                update_time = sysdate(),
            </trim>
            WHERE id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

    <!-- 批量插入司机画像数据，一次插入多条记录 -->
    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into scheduler_driver_profile
        (driver_id, driver_name, telephone, scheduler_telephone, identity_card, driving_years, 
        profile_completeness, service_count, total_course, operation, operation_desc, 
        is_the_drivr_license_valid, vehicle_class, valid_period_to, qualification_certificate, 
        is_the_qualification_valid, actual_carrier_end, last_service_time, state, address, 
        vehicles, long_distance_routes, wlhy_vehicle_structure,scheduler_vehicle_structure, wlhy_vehicle_length,scheduler_vehicle_length, wlhy_goods,scheduler_goods, wlhy_haulway,scheduler_haulway,
        goods_weight,goods_volume,
        create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.driverId}, #{item.driverName}, #{item.telephone}, #{item.schedulerTelephone}, 
            #{item.identityCard}, #{item.drivingYears}, #{item.profileCompleteness}, 
            #{item.serviceCount}, #{item.totalCourse}, #{item.operation}, #{item.operationDesc}, 
            #{item.isTheDrivrLicenseValid}, #{item.vehicleClass}, #{item.validPeriodTo}, 
            #{item.qualificationCertificate}, #{item.isTheQualificationValid}, #{item.actualCarrierEnd}, 
            #{item.lastServiceTime}, #{item.state}, #{item.address}, #{item.vehicles}, 
            #{item.longDistanceRoutes}, #{item.wlhyVehicleStructure}, #{item.schedulerVehicleStructure}, #{item.wlhyVehicleLength}, #{item.schedulerVehicleLength}, #{item.wlhyGoods}, #{item.schedulerGoods}, #{item.wlhyHaulway}, #{item.schedulerHaulway},
            #{item.goodsWeight}, #{item.goodsVolume}, #{item.createBy},
            #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>



    <!-- 根据身份证号查询司机画像 -->
    <select id="selectByIdentityCard" parameterType="String" resultMap="DriverProfileResult">
        <include refid="selectDriverProfileVo"/>
        where identity_card = #{identityCard} and deleted = 0
    </select>
    
    <!-- 根据手机号查询司机画像 -->
    <select id="selectByTelephone" parameterType="String" resultMap="DriverProfileResult">
        <include refid="selectDriverProfileVo"/>
        where telephone = #{telephone} and deleted = 0
    </select>
    
    <!-- 根据姓名和手机号查询司机画像 -->
    <select id="selectByNameAndTelephone" resultMap="DriverProfileResult">
        <include refid="selectDriverProfileVo"/>
        where driver_name = #{driverName} and telephone = #{telephone} and deleted = 0
    </select>
    <select id="selectByNameAndTelephoneAndIdentityCard"
            resultType="com.zly.project.driver.domain.DriverProfileDO">
        <include refid="selectDriverProfileVo"/>
        where driver_name = #{driverName} and telephone = #{telephone} and identity_card = #{identityCard} and deleted = 0
    </select>

    <!-- 分页查询有效且非黑名单的司机数据 -->
    <select id="selectActiveNonBlacklistDrivers" resultMap="DriverProfileResult">
        <include refid="selectDriverProfileVo"/>
        where state = 0 and blacklist_status = 0 and deleted = 0
        and scheduler_telephone is not null and scheduler_telephone != ''
        order by id
        limit #{offset},#{pageSize}
    </select>

    <select id="countDriverProfiles" resultType="int">
        SELECT COUNT(1) FROM scheduler_driver_profile
    </select>


    <select id="selectDriversByRoutes" resultMap="DriverProfileResult">
        SELECT * FROM scheduler_driver_profile
        WHERE (
            wlhy_haulway LIKE CONCAT('%', #{startPoint}, '%')
                AND wlhy_haulway LIKE CONCAT('%', #{endPoint}, '%')
            ) OR (
                     scheduler_haulway LIKE CONCAT('%', #{startPoint}, '%')
                         AND scheduler_haulway LIKE CONCAT('%', #{endPoint}, '%')
                     )
            AND blacklist_status != 1
            AND deleted = 0
    </select>

</mapper>