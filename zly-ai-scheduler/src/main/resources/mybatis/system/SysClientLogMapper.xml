<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.system.mapper.SysClientLogMapper">

    <resultMap type="SysClientLog" id="SysClientLogResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="actionName" column="action_name"/>
        <result property="actionScene" column="action_scene"/>
        <result property="actionType" column="action_type"/>
        <result property="platform" column="platform"/>
        <result property="createTime" column="create_time"/>
        <result property="customerId" column="customer_id"/>
        <result property="beforeActionData" column="before_action_data"/>
        <result property="afterActionData" column="after_action_data"/>
        <result property="keyWord" column="key_word"/>
    </resultMap>

    <resultMap type="com.zly.project.system.domain.SysClientLogRes" id="SysClientLogResResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="actionName" column="action_name"/>
        <result property="actionScene" column="action_scene"/>
        <result property="actionType" column="action_type"/>
        <result property="platform" column="platform"/>
        <result property="createTime" column="create_time"/>
        <result property="customerId" column="customer_id"/>
        <result property="beforeActionData" column="before_action_data"/>
        <result property="afterActionData" column="after_action_data"/>
        <result property="keyWord" column="key_word"/>
    </resultMap>

    <select id="selectDatabase" resultType="java.lang.String">
        SELECT database()
    </select>

    <select id="tableCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM information_schema.tables
        WHERE TABLE_SCHEMA = #{database}
          AND TABLE_NAME = #{tableName}
    </select>

    <update id="createTable">
        CREATE TABLE ${tableName}
        (
            id           BIGINT(20) NOT NULL,
            user_id      BIGINT(20) NOT NULL COMMENT '用户ID',
            user_name    VARCHAR(50)                      NOT NULL COMMENT '用户账号',
            nick_name    VARCHAR(50)                      NOT NULL COMMENT '用户名称',
            action_name  VARCHAR(5000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '操作',
            action_scene TINYINT(4) NOT NULL COMMENT '操作场景(1：登录系统， 2：每日货源，3：常发货源，4：司机画像，5：司机画像标记)',
            action_type  TINYINT(4) NOT NULL COMMENT '操作类型(1:查询 2:新增 3:删除 4:更新 5:其他操作)',
            platform     TINYINT(4) NOT NULL COMMENT '平台(0AI调度系统)',
            ip           VARCHAR(150) CHARACTER SET utf8  NOT NULL DEFAULT '' COMMENT 'IP地址',
            create_time  DATETIME                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            customer_id  BIGINT(20) NOT NULL COMMENT '公司id',
            `before_action_data` varchar(5000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '变更前内容',
            `after_action_data` varchar(5000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '变更后内容',
            `key_word` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '关键字',
            PRIMARY KEY (`id`),
            KEY `nick_name` (`nick_name`) USING BTREE,
            KEY `idx_customer_create_time` (`customer_id`,`create_time`) USING BTREE,
            KEY `customer_id` (`customer_id`) USING BTREE,
            KEY `key_word` (`key_word`) USING BTREE
        ) ENGINE = InnoDB
		  DEFAULT CHARSET = utf8mb4 COMMENT ='操作日志表';
    </update>

    <select id="selectByParams" resultMap="SysClientLogResResult">
        SELECT t.* FROM (
        <foreach collection="tableNames" separator="UNION ALL" item="tableName">
            SELECT * FROM ${tableName}
            where 1=1
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
        </foreach>
        ) t
        <where>
            <if test="beginTime != null">
                AND t.create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                AND t.create_time &lt;= #{endTime}
            </if>
            <if test="actionType != null">
                AND t.action_type = #{actionType}
            </if>
            <if test="actionScene != null">
                AND t.action_scene = #{actionScene}
            </if>
            <if test="actionName != null and actionName != ''">
                AND t.action_name LIKE '%${actionName}%'
            </if>
            <if test="platform != null">
                AND t.platform = #{platform}
            </if>
            <!--<if test="userName != null and userName != ''">
                AND (a.user_name LIKE '%${userName}%' OR a.nick_name LIKE '%${userName}%')
            </if>-->
            <if test="userName != null and userName != ''">
                AND t.nick_name = #{userName}
            </if>
            <if test="keyWord != null and keyWord != ''">
                AND t.key_word LIKE '%${keyWord}%'
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>


    <insert id="insertSelective">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="nickName != null">
                nick_name,
            </if>
            <if test="actionName != null">
                action_name,
            </if>
            <if test="actionScene != null">
                action_scene,
            </if>
            <if test="actionType != null">
                action_type,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="beforeActionData != null">
                before_action_data,
            </if>
            <if test="afterActionData != null">
                after_action_data,
            </if>
            <if test="keyWord != null">
                key_word,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="userName != null">
                #{userName},
            </if>
            <if test="nickName != null">
                #{nickName},
            </if>
            <if test="actionName != null">
                #{actionName},
            </if>
            <if test="actionScene != null">
                #{actionScene},
            </if>
            <if test="actionType != null">
                #{actionType},
            </if>
            <if test="platform != null">
                #{platform},
            </if>
            <if test="ip != null">
                #{ip},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="customerId != null">
                #{customerId},
            </if>
            <if test="beforeActionData != null">
                #{beforeActionData},
            </if>
            <if test="afterActionData != null">
                #{afterActionData},
            </if>
            <if test="keyWord != null">
                #{keyWord},
            </if>
        </trim>
    </insert>
</mapper>
