<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.match.mapper.DispatchCallMapper">
    
    <resultMap type="DispatchCallDO" id="DispatchCallResult">
        <id     property="id"              column="id"               />
        <result property="taskMatchId"      column="task_match_id"   />
        <result property="externalTaskId"   column="external_task_id"/>
        <result property="callStatus"       column="call_status"     />
        <result property="hasIntention"     column="has_intention"   />
        <result property="callContent"      column="call_content"    />
        <result property="voiceUrl"         column="voice_url"       />
        <result property="callTime"         column="call_time"       />
        <result property="extra"            column="extra"       />
        <result property="remark"            column="remark"       />
        <result property="createBy"         column="create_by"       />
        <result property="createTime"       column="create_time"     />
        <result property="updateBy"         column="update_by"       />
        <result property="updateTime"       column="update_time"     />
        <result property="deleted"          column="deleted"         />
    </resultMap>

    <sql id="selectDispatchCallVo">
        select id, task_match_id, external_task_id, call_status, has_intention, call_content, 
        voice_url, call_time, extra,remark,create_by, create_time, update_by, update_time, deleted
        from scheduler_dispatch_call
    </sql>

    <select id="selectDispatchCallById" parameterType="Long" resultMap="DispatchCallResult">
        <include refid="selectDispatchCallVo"/>
        where id = #{id} and deleted = 0
    </select>

    <select id="selectDispatchCallList" parameterType="DispatchCallDO" resultMap="DispatchCallResult">
        <include refid="selectDispatchCallVo"/>
        <where>
            deleted = 0
            <if test="taskMatchId != null ">and task_match_id = #{taskMatchId}</if>
            <if test="externalTaskId != null  and externalTaskId != ''">and external_task_id = #{externalTaskId}</if>
            <if test="callStatus != null  and callStatus != ''">and call_status = #{callStatus}</if>
            <if test="hasIntention != null ">and has_intention = #{hasIntention}</if>
            <if test="extra!= null ">and extra = #{extra}</if>
            <if test="remark!= null ">and remark = #{remark}</if>
            <if test="callTime != null ">and call_time = #{callTime}</if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertDispatchCall" parameterType="DispatchCallDO" useGeneratedKeys="true" keyProperty="id">
        insert into scheduler_dispatch_call
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskMatchId != null">task_match_id,</if>
            <if test="externalTaskId != null">external_task_id,</if>
            <if test="callStatus != null">call_status,</if>
            <if test="hasIntention != null">has_intention,</if>
            <if test="callContent != null">call_content,</if>
            <if test="voiceUrl != null">voice_url,</if>
            <if test="callTime != null">call_time,</if>
            <if test="extra!= null">extra,</if>
            <if test="remark!= null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskMatchId != null">#{taskMatchId},</if>
            <if test="externalTaskId != null">#{externalTaskId},</if>
            <if test="callStatus != null">#{callStatus},</if>
            <if test="hasIntention != null">#{hasIntention},</if>
            <if test="callContent != null">#{callContent},</if>
            <if test="voiceUrl != null">#{voiceUrl},</if>
            <if test="callTime != null">#{callTime},</if>
            <if test="extra!= null">#{extra},</if>
            <if test="remark!= null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateDispatchCall" parameterType="DispatchCallDO">
        update scheduler_dispatch_call
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskMatchId != null">task_match_id = #{taskMatchId},</if>
            <if test="externalTaskId != null">external_task_id = #{externalTaskId},</if>
            <if test="callStatus != null">call_status = #{callStatus},</if>
            <if test="hasIntention != null">has_intention = #{hasIntention},</if>
            <if test="callContent != null">call_content = #{callContent},</if>
            <if test="voiceUrl != null">voice_url = #{voiceUrl},</if>
            <if test="extra!= null">extra = #{extra},</if>
            <if test="remark!= null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDispatchCallById" parameterType="Long">
        update scheduler_dispatch_call set deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteDispatchCallByIds" parameterType="String">
        update scheduler_dispatch_call set deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据匹配司机ID查询呼叫记录集合 -->
    <select id="selectByDriverMatchIds" parameterType="java.util.List" resultMap="DispatchCallResult">
        <include refid="selectDispatchCallVo"/>
        WHERE task_match_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
        ORDER BY call_time DESC
    </select>

    <select id="getDispatchCallByExtra" resultMap="DispatchCallResult">
        <include refid="selectDispatchCallVo"/>
        WHERE extra = #{extra} AND deleted = 0
        ORDER BY call_time DESC
        LIMIT 1
    </select>

    <!-- 其他已有的SQL映射... -->

    <!-- 根据matchId查询最新的通话记录 -->
    <select id="getLatestCallByMatchId" parameterType="Long" resultMap="DispatchCallResult">
        <include refid="selectDispatchCallVo"/>
        WHERE task_match_id = #{matchId}
        ORDER BY call_time DESC
        LIMIT 1
    </select>

</mapper>