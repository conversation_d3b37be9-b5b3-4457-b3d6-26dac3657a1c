<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.match.mapper.TaskDriverMatchMapper">
    
    <resultMap type="com.zly.project.match.domain.TaskDriverMatchDO" id="TaskDriverMatchResult">
        <id     property="id"                column="id"                 />
        <result property="taskPlanId"        column="task_plan_id"       />
        <result property="outboundTaskId"    column="outbound_task_id"   />
        <result property="driverProfileId"   column="driver_profile_id"  />
        <result property="driverName"        column="driver_name"        />
        <result property="identityCard"      column="identity_card"      />
        <result property="telephone"         column="telephone"          />
        <result property="matchScore"        column="match_score"        />
        <result property="dealStatus"        column="deal_status"        />
        <result property="isOutbound"        column="is_outbound"        />
        <result property="hasIntention"      column="has_intention"      />
        <result property="isSendSms"         column="is_send_sms"        />
        <result property="smsSendTime"       column="sms_send_time"      />
        <result property="isConfirmSms"      column="is_confirm_sms"     />
        <result property="confirmSmsTime"    column="confirm_sms_time"   />
        <result property="unitCount"         column="unit_count"   />
        <result property="freightCost"       column="freight_cost"       />
        <result property="aiAnalysis"        column="ai_analysis"        />
        <result property="remark"            column="remark"             />
        <result property="createBy"          column="create_by"          />
        <result property="createTime"        column="create_time"        />
        <result property="updateBy"          column="update_by"          />
        <result property="updateTime"        column="update_time"        />
        <result property="deleted"           column="deleted"            />
    </resultMap>

    <sql id="selectTaskDriverMatchVo">
        select id, task_plan_id, outbound_task_id, driver_profile_id, driver_name, identity_card, 
               telephone, match_score, deal_status, is_outbound, has_intention, is_send_sms, 
               sms_send_time, is_confirm_sms, confirm_sms_time,unit_count, freight_cost, ai_analysis, remark, create_by,
               create_time, update_by, update_time, deleted
        from scheduler_task_driver_match
    </sql>

    <select id="selectTaskDriverMatchList" parameterType="com.zly.project.match.controller.vo.TaskDriverMatchPageReqVO" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        <where>
            deleted = 0
            <if test="taskPlanId != null">
                AND task_plan_id = #{taskPlanId}
            </if>
            <if test="driverName != null and driverName != ''">
                AND driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="telephone != null and telephone != ''">
                AND telephone like concat('%', #{telephone}, '%')
            </if>
            <if test="dealStatus != null">
                AND deal_status = #{dealStatus}
            </if>
            <if test="isOutbound != null">
                AND is_outbound = #{isOutbound}
            </if>
            <if test="hasIntention != null">
                AND has_intention = #{hasIntention}
            </if>
        </where>
        order by match_score desc
    </select>
    
    <select id="selectTaskDriverMatchById" parameterType="Long" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        where id = #{id} and deleted = 0
    </select>
    
    <select id="selectListByTaskPlanId" parameterType="Long" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        where task_plan_id = #{taskPlanId} and deleted = 0
        order by match_score desc
    </select>
    
    <select id="selectListByDealStatus" parameterType="Integer" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        where deal_status = #{dealStatus} and deleted = 0
        order by create_time desc
    </select>
    
    <select id="selectTaskDriverMatchBatchIds" parameterType="Collection" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        where id in
        <foreach item="id" collection="collection" open="(" separator="," close=")">
            #{id}
        </foreach>
        and deleted = 0
    </select>
    <select id="selectByTaskId" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        where task_plan_id = #{id} and deleted = 0
    </select>


    <select id="selectByTelephoneAndOutboundTaskIdNotNull" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        where telephone = #{telephone} and outbound_task_id is not null and deleted = 0
    </select>


     <!-- 插入语句修改 -->
    <insert id="insertTaskDriverMatch" parameterType="com.zly.project.match.domain.TaskDriverMatchDO" useGeneratedKeys="true" keyProperty="id">
        insert into scheduler_task_driver_match
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskPlanId != null">task_plan_id,</if>
            <if test="outboundTaskId != null">outbound_task_id,</if>
            <if test="driverProfileId != null">driver_profile_id,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="identityCard != null">identity_card,</if>
            <if test="telephone != null">telephone,</if>
            <if test="matchScore != null">match_score,</if>
            <if test="dealStatus != null">deal_status,</if>
            <if test="isOutbound != null">is_outbound,</if>
            <if test="hasIntention != null">has_intention,</if>
            <if test="isSendSms != null">is_send_sms,</if>
            <if test="smsSendTime != null">sms_send_time,</if>
            <if test="isConfirmSms != null">is_confirm_sms,</if>
            <if test="confirmSmsTime != null">confirm_sms_time,</if>
            <if test="unitCount != null">unit_count,</if>
            <if test="freightCost != null">freight_cost,</if>
            <if test="aiAnalysis != null">ai_analysis,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskPlanId != null">#{taskPlanId},</if>
            <if test="outboundTaskId != null">#{outboundTaskId},</if>
            <if test="driverProfileId != null">#{driverProfileId},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="identityCard != null">#{identityCard},</if>
            <if test="telephone != null">#{telephone},</if>
            <if test="matchScore != null">#{matchScore},</if>
            <if test="dealStatus != null">#{dealStatus},</if>
            <if test="isOutbound != null">#{isOutbound},</if>
            <if test="hasIntention != null">#{hasIntention},</if>
            <if test="isSendSms != null">#{isSendSms},</if>
            <if test="smsSendTime != null">#{smsSendTime},</if>
            <if test="isConfirmSms != null">#{isConfirmSms},</if>
            <if test="confirmSmsTime != null">#{confirmSmsTime},</if>
            <if test="unitCount != null">#{unitCount},</if>
            <if test="freightCost != null">#{freightCost},</if>
            <if test="aiAnalysis != null">#{aiAnalysis},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            now(),
            <if test="updateBy != null">#{updateBy},</if>
            now(),
            0,
        </trim>
    </insert>
    
      <!-- 批量插入语句修改 -->
    <insert id="insertBatch">
        insert into scheduler_task_driver_match
        (id,task_plan_id, outbound_task_id, driver_profile_id, driver_name, identity_card,
         telephone, match_score, deal_status, is_outbound, has_intention, is_send_sms, 
         sms_send_time, is_confirm_sms, confirm_sms_time,unit_count, freight_cost, ai_analysis, remark,
         create_by, create_time, update_by, update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.taskPlanId}, #{item.outboundTaskId}, #{item.driverProfileId}, #{item.driverName}, #{item.identityCard},
             #{item.telephone}, #{item.matchScore}, #{item.dealStatus}, #{item.isOutbound}, #{item.hasIntention}, #{item.isSendSms},
             #{item.smsSendTime}, #{item.isConfirmSms}, #{item.confirmSmsTime}, #{item.unitCount}, #{item.freightCost}, #{item.aiAnalysis}, #{item.remark},
             #{item.createBy}, now(), #{item.updateBy}, now(), 0)
        </foreach>
    </insert>
    
      <!-- 更新语句修改 -->
    <update id="updateTaskDriverMatchById" parameterType="com.zly.project.match.domain.TaskDriverMatchDO">
        update scheduler_task_driver_match
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskPlanId != null">task_plan_id = #{taskPlanId},</if>
            <if test="outboundTaskId != null">outbound_task_id = #{outboundTaskId},</if>
            <if test="driverProfileId != null">driver_profile_id = #{driverProfileId},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="identityCard != null">identity_card = #{identityCard},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <if test="matchScore != null">match_score = #{matchScore},</if>
            <if test="dealStatus != null">deal_status = #{dealStatus},</if>
            <if test="isOutbound != null">is_outbound = #{isOutbound},</if>
            <if test="hasIntention != null">has_intention = #{hasIntention},</if>
            <if test="isSendSms != null">is_send_sms = #{isSendSms},</if>
            <if test="smsSendTime != null">sms_send_time = #{smsSendTime},</if>
            <if test="isConfirmSms != null">is_confirm_sms = #{isConfirmSms},</if>
            <if test="confirmSmsTime != null">confirm_sms_time = #{confirmSmsTime},</if>
            <if test="unitCount != null">unit_count = #{unitCount},</if>
            <if test="freightCost != null">freight_cost = #{freightCost},</if>
            <if test="aiAnalysis != null">ai_analysis = #{aiAnalysis},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now(),
        </trim>
        where id = #{id} and deleted = 0
    </update>
    
    <update id="updateDealStatus">
        update scheduler_task_driver_match
        set deal_status = #{dealStatus},
            update_by = #{updateBy},
            update_time = now()
        where id = #{id} and deleted = 0
    </update>
    
    <update id="deleteTaskDriverMatchById" parameterType="Long">
        update scheduler_task_driver_match set deleted = 1 where id = #{id}
    </update>
    
    <update id="deleteTaskDriverMatchByIds" parameterType="Collection">
        update scheduler_task_driver_match set deleted = 1 
        where id in
        <foreach item="id" collection="collection" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据任务计划ID和外呼状态查询匹配司机列表 -->
    <select id="selectListByTaskPlanIdAndOutboundStatus" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        WHERE task_plan_id = #{taskPlanId}
          AND is_outbound = #{outboundStatus}
          AND deleted = 0
        ORDER BY match_score DESC
    </select>
    <select id="selectListByOutboundStatus" resultMap="TaskDriverMatchResult">
        <include refid="selectTaskDriverMatchVo"/>
        WHERE is_outbound = #{outboundStatus}
        AND deleted = 0
        ORDER BY match_score DESC
    </select>

    <select id="selectCalledDriversByPhones" resultMap="TaskDriverMatchResult">
        SELECT * FROM scheduler_task_driver_match
        WHERE telephone IN
        <foreach collection="phones" item="phone" open="(" separator="," close=")">
            #{phone}
        </foreach>
        AND create_time BETWEEN #{startTime} AND #{endTime}
        AND (is_outbound = 3 OR deal_status = 1)
    </select>

</mapper>