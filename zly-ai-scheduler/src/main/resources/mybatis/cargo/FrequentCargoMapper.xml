<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.cargo.mapper.FrequentCargoMapper">

    <resultMap type="com.zly.project.cargo.domain.FrequentCargo" id="FrequentCargoResult">
        <id     property="id"                   column="id"                   />
        <result property="customerId"           column="customer_id"          />
        <result property="cargoName"            column="cargo_name"           />
        <result property="cargoType"            column="cargo_type"           />
        <result property="packagingMethod"      column="packaging_method"     />
        <result property="loadingProvinceCode"  column="loading_province_code"/>
        <result property="loadingProvinceName"  column="loading_province_name"/>
        <result property="loadingCityCode"      column="loading_city_code"    />
        <result property="loadingCityName"      column="loading_city_name"    />
        <result property="loadingDistrictCode"  column="loading_district_code"/>
        <result property="loadingDistrictName"  column="loading_district_name"/>
        <result property="loadingAddress"       column="loading_address"      />
        <result property="loadingLongitude"     column="loading_longitude"    />
        <result property="loadingLatitude"      column="loading_latitude"     />
        <result property="unloadingProvinceCode" column="unloading_province_code"/>
        <result property="unloadingProvinceName" column="unloading_province_name"/>
        <result property="unloadingCityCode"     column="unloading_city_code" />
        <result property="unloadingCityName"     column="unloading_city_name" />
        <result property="unloadingDistrictCode" column="unloading_district_code"/>
        <result property="unloadingDistrictName" column="unloading_district_name"/>
        <result property="unloadingAddress"      column="unloading_address"   />
        <result property="unloadingLongitude"    column="unloading_longitude" />
        <result property="unloadingLatitude"     column="unloading_latitude"  />
        <result property="status"                column="status"              />
        <result property="createBy"              column="create_by"           />
        <result property="createTime"            column="create_time"         />
        <result property="updateBy"              column="update_by"           />
        <result property="updateTime"            column="update_time"         />
        <result property="remark"                column="remark"              />
        <result property="deleted"               column="deleted"             />
    </resultMap>
    
    <resultMap id="FrequentCargoWithCarrierResult" type="com.zly.project.cargo.controller.vo.FrequentCargoRespVo">
        <id     property="id"                   column="id"                   />
        <result property="customerId"           column="customer_id"          />
        <result property="customerName"          column="customer_name"         />
        <result property="cargoName"            column="cargo_name"           />
        <result property="cargoType"            column="cargo_type"           />
        <result property="packagingMethod"      column="packaging_method"     />
        <result property="loadingFullAddress"   column="loading_full_address" />
        <result property="unloadingFullAddress" column="unloading_full_address"/>
        <result property="contactPerson"        column="contact_person"       />
        <result property="carrierContact"       column="contact_phone"        />
        <result property="status"               column="status"               />
        <result property="createTime"           column="create_time"          />
        <result property="updateTime"           column="update_time"          />
    </resultMap>

    <sql id="selectFrequentCargoVo">
        select id, customer_id, cargo_name, cargo_type, packaging_method,
               loading_province_code, loading_province_name, loading_city_code, loading_city_name, 
               loading_district_code, loading_district_name, loading_address, loading_longitude, loading_latitude,
               unloading_province_code, unloading_province_name, unloading_city_code, unloading_city_name, 
               unloading_district_code, unloading_district_name, unloading_address, unloading_longitude, unloading_latitude,
               status, create_by, create_time, update_by, update_time, remark, deleted
        from frequent_cargo
    </sql>
    
    <select id="selectFrequentCargoById" parameterType="Long" resultMap="FrequentCargoResult">
        <include refid="selectFrequentCargoVo"/>
        where id = #{id}
        AND deleted = 0
    </select>
    
    <select id="selectFrequentCargoList" parameterType="com.zly.project.cargo.domain.FrequentCargo" resultMap="FrequentCargoResult">
        <include refid="selectFrequentCargoVo"/>
        <where>
            deleted = 0
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="customerId != null">
                AND customer_id = #{customerId}
            </if>
            <if test="cargoName != null and cargoName != ''">
                AND cargo_name like concat('%', #{cargoName}, '%')
            </if>
            <if test="loadingAddress != null and loadingAddress != ''">
                AND loading_address like concat('%', #{loadingAddress}, '%')
            </if>
            <if test="unloadingAddress != null and unloadingAddress != ''">
                AND unloading_address like concat('%', #{unloadingAddress}, '%')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectFrequentCargoListWithCarrier" parameterType="com.zly.project.cargo.controller.vo.FrequentCargoQueryVo" resultMap="FrequentCargoWithCarrierResult">
        SELECT 
            fc.id, 
            fc.customer_id, 
            ci.customer_name,
            fc.cargo_name,
            fc.cargo_type,
            fc.packaging_method,
            CONCAT(IFNULL(fc.loading_province_name, ''), IFNULL(fc.loading_city_name, ''), IFNULL(fc.loading_district_name, ''), IFNULL(fc.loading_address, '')) AS loading_full_address,
            CONCAT(IFNULL(fc.unloading_province_name, ''), IFNULL(fc.unloading_city_name, ''), IFNULL(fc.unloading_district_name, ''), IFNULL(fc.unloading_address, '')) AS unloading_full_address,
            ci.contact_person,
            ci.contact_phone,
            fc.status,
            fc.create_time,
            fc.update_time
        FROM 
            frequent_cargo fc
        LEFT JOIN 
            customer_info ci ON fc.customer_id = ci.id
        <where>
            fc.deleted = 0
            <if test="status != null and status != ''">
                AND fc.status = #{status}
            </if>
            <if test="customerName != null and customerName != ''">
                AND ci.customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="cargoName != null and cargoName != ''">
                AND fc.cargo_name like concat('%', #{cargoName}, '%')
            </if>
            <if test="loadingAddress != null and loadingAddress != ''">
                AND (
                    fc.loading_province_name like concat('%', #{loadingAddress}, '%') OR
                    fc.loading_city_name like concat('%', #{loadingAddress}, '%') OR
                    fc.loading_district_name like concat('%', #{loadingAddress}, '%') OR
                    fc.loading_address like concat('%', #{loadingAddress}, '%')
                )
            </if>
            <if test="unloadingAddress != null and unloadingAddress != ''">
                AND (
                    fc.unloading_province_name like concat('%', #{unloadingAddress}, '%') OR
                    fc.unloading_city_name like concat('%', #{unloadingAddress}, '%') OR
                    fc.unloading_district_name like concat('%', #{unloadingAddress}, '%') OR
                    fc.unloading_address like concat('%', #{unloadingAddress}, '%')
                )
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                AND fc.create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                AND fc.create_time &lt;= #{createTimeEnd}
            </if>
        </where>
        order by fc.create_time desc
    </select>
    
    <insert id="insertFrequentCargo" parameterType="com.zly.project.cargo.domain.FrequentCargo" useGeneratedKeys="true" keyProperty="id">
        insert into frequent_cargo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="cargoName != null and cargoName != ''">cargo_name,</if>
            <if test="cargoType != null and cargoType != ''">cargo_type,</if>
            <if test="packagingMethod != null and packagingMethod != ''">packaging_method,</if>
            <if test="loadingProvinceCode != null and loadingProvinceCode != ''">loading_province_code,</if>
            <if test="loadingProvinceName != null and loadingProvinceName != ''">loading_province_name,</if>
            <if test="loadingCityCode != null and loadingCityCode != ''">loading_city_code,</if>
            <if test="loadingCityName != null and loadingCityName != ''">loading_city_name,</if>
            <if test="loadingDistrictCode != null and loadingDistrictCode != ''">loading_district_code,</if>
            <if test="loadingDistrictName != null and loadingDistrictName != ''">loading_district_name,</if>
            <if test="loadingAddress != null and loadingAddress != ''">loading_address,</if>
            <if test="loadingLongitude != null">loading_longitude,</if>
            <if test="loadingLatitude != null">loading_latitude,</if>
            <if test="unloadingProvinceCode != null and unloadingProvinceCode != ''">unloading_province_code,</if>
            <if test="unloadingProvinceName != null and unloadingProvinceName != ''">unloading_province_name,</if>
            <if test="unloadingCityCode != null and unloadingCityCode != ''">unloading_city_code,</if>
            <if test="unloadingCityName != null and unloadingCityName != ''">unloading_city_name,</if>
            <if test="unloadingDistrictCode != null and unloadingDistrictCode != ''">unloading_district_code,</if>
            <if test="unloadingDistrictName != null and unloadingDistrictName != ''">unloading_district_name,</if>
            <if test="unloadingAddress != null and unloadingAddress != ''">unloading_address,</if>
            <if test="unloadingLongitude != null">unloading_longitude,</if>
            <if test="unloadingLatitude != null">unloading_latitude,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="cargoName != null and cargoName != ''">#{cargoName},</if>
            <if test="cargoType != null and cargoType != ''">#{cargoType},</if>
            <if test="packagingMethod != null and packagingMethod != ''">#{packagingMethod},</if>
            <if test="loadingProvinceCode != null and loadingProvinceCode != ''">#{loadingProvinceCode},</if>
            <if test="loadingProvinceName != null and loadingProvinceName != ''">#{loadingProvinceName},</if>
            <if test="loadingCityCode != null and loadingCityCode != ''">#{loadingCityCode},</if>
            <if test="loadingCityName != null and loadingCityName != ''">#{loadingCityName},</if>
            <if test="loadingDistrictCode != null and loadingDistrictCode != ''">#{loadingDistrictCode},</if>
            <if test="loadingDistrictName != null and loadingDistrictName != ''">#{loadingDistrictName},</if>
            <if test="loadingAddress != null and loadingAddress != ''">#{loadingAddress},</if>
            <if test="loadingLongitude != null">#{loadingLongitude},</if>
            <if test="loadingLatitude != null">#{loadingLatitude},</if>
            <if test="unloadingProvinceCode != null and unloadingProvinceCode != ''">#{unloadingProvinceCode},</if>
            <if test="unloadingProvinceName != null and unloadingProvinceName != ''">#{unloadingProvinceName},</if>
            <if test="unloadingCityCode != null and unloadingCityCode != ''">#{unloadingCityCode},</if>
            <if test="unloadingCityName != null and unloadingCityName != ''">#{unloadingCityName},</if>
            <if test="unloadingDistrictCode != null and unloadingDistrictCode != ''">#{unloadingDistrictCode},</if>
            <if test="unloadingDistrictName != null and unloadingDistrictName != ''">#{unloadingDistrictName},</if>
            <if test="unloadingAddress != null and unloadingAddress != ''">#{unloadingAddress},</if>
            <if test="unloadingLongitude != null">#{unloadingLongitude},</if>
            <if test="unloadingLatitude != null">#{unloadingLatitude},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>
    
    <update id="updateFrequentCargo" parameterType="com.zly.project.cargo.domain.FrequentCargo">
        update frequent_cargo
        <trim prefix="set" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="cargoName != null and cargoName != ''">cargo_name = #{cargoName},</if>
            <if test="cargoType != null and cargoType != ''">cargo_type = #{cargoType},</if>
            <if test="packagingMethod != null and packagingMethod != ''">packaging_method = #{packagingMethod},</if>
            <if test="loadingProvinceCode != null and loadingProvinceCode != ''">loading_province_code = #{loadingProvinceCode},</if>
            <if test="loadingProvinceName != null and loadingProvinceName != ''">loading_province_name = #{loadingProvinceName},</if>
            <if test="loadingCityCode != null and loadingCityCode != ''">loading_city_code = #{loadingCityCode},</if>
            <if test="loadingCityName != null and loadingCityName != ''">loading_city_name = #{loadingCityName},</if>
            <if test="loadingDistrictCode != null and loadingDistrictCode != ''">loading_district_code = #{loadingDistrictCode},</if>
            <if test="loadingDistrictName != null and loadingDistrictName != ''">loading_district_name = #{loadingDistrictName},</if>
            <if test="loadingAddress != null and loadingAddress != ''">loading_address = #{loadingAddress},</if>
            <if test="loadingLongitude != null">loading_longitude = #{loadingLongitude},</if>
            <if test="loadingLatitude != null">loading_latitude = #{loadingLatitude},</if>
            <if test="unloadingProvinceCode != null and unloadingProvinceCode != ''">unloading_province_code = #{unloadingProvinceCode},</if>
            <if test="unloadingProvinceName != null and unloadingProvinceName != ''">unloading_province_name = #{unloadingProvinceName},</if>
            <if test="unloadingCityCode != null and unloadingCityCode != ''">unloading_city_code = #{unloadingCityCode},</if>
            <if test="unloadingCityName != null and unloadingCityName != ''">unloading_city_name = #{unloadingCityName},</if>
            <if test="unloadingDistrictCode != null and unloadingDistrictCode != ''">unloading_district_code = #{unloadingDistrictCode},</if>
            <if test="unloadingDistrictName != null and unloadingDistrictName != ''">unloading_district_name = #{unloadingDistrictName},</if>
            <if test="unloadingAddress != null and unloadingAddress != ''">unloading_address = #{unloadingAddress},</if>
            <if test="unloadingLongitude != null">unloading_longitude = #{unloadingLongitude},</if>
            <if test="unloadingLatitude != null">unloading_latitude = #{unloadingLatitude},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where id = #{id}
    </update>
    
    <select id="checkDuplicateFrequentCargo" parameterType="com.zly.project.cargo.domain.FrequentCargo" resultMap="FrequentCargoResult">
        <include refid="selectFrequentCargoVo"/>
        <where>
            status = '0'
            AND deleted = 0
            AND customer_id = #{customerId}
            AND cargo_name = #{cargoName}
            AND cargo_type = #{cargoType}
            AND packaging_method = #{packagingMethod}
            AND loading_province_name = #{loadingProvinceName}
            AND loading_city_name = #{loadingCityName}
            AND loading_district_name = #{loadingDistrictName}
            AND loading_address = #{loadingAddress}
            AND unloading_province_name = #{unloadingProvinceName}
            AND unloading_city_name = #{unloadingCityName}
            AND unloading_district_name = #{unloadingDistrictName}
            AND unloading_address = #{unloadingAddress}
            <if test="id != null">
                AND id != #{id}
            </if>
        </where>
        limit 1
    </select>
</mapper> 