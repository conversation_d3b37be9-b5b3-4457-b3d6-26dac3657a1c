<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.cargo.mapper.DailyCargoMapper">

    <resultMap type="com.zly.project.cargo.domain.DailyCargo" id="DailyCargoResult">
        <id     property="id"                   column="id"                   />
        <result property="customerId"           column="customer_id"          />
        <result property="taskId"               column="task_id"              />
        <result property="frequentCargoId"      column="frequent_cargo_id"    />
        <result property="cargoName"            column="cargo_name"           />
        <result property="cargoType"            column="cargo_type"           />
        <result property="packagingMethod"      column="packaging_method"     />
        <result property="cargoWeight"          column="cargo_weight"         />
        <result property="cargoVolume"          column="cargo_volume"         />
        <result property="loadingProvinceCode"  column="loading_province_code"/>
        <result property="loadingProvinceName"  column="loading_province_name"/>
        <result property="loadingCityCode"      column="loading_city_code"    />
        <result property="loadingCityName"      column="loading_city_name"    />
        <result property="loadingDistrictCode"  column="loading_district_code"/>
        <result property="loadingDistrictName"  column="loading_district_name"/>
        <result property="loadingAddress"       column="loading_address"      />
        <result property="loadingLongitude"     column="loading_longitude"    />
        <result property="loadingLatitude"      column="loading_latitude"     />
        <result property="unloadingProvinceCode" column="unloading_province_code"/>
        <result property="unloadingProvinceName" column="unloading_province_name"/>
        <result property="unloadingCityCode"     column="unloading_city_code" />
        <result property="unloadingCityName"     column="unloading_city_name" />
        <result property="unloadingDistrictCode" column="unloading_district_code"/>
        <result property="unloadingDistrictName" column="unloading_district_name"/>
        <result property="unloadingAddress"      column="unloading_address"   />
        <result property="unloadingLongitude"    column="unloading_longitude" />
        <result property="unloadingLatitude"     column="unloading_latitude"  />
        <result property="loadingDate"           column="loading_date"        />
        <result property="loadingTimeStart"      column="loading_time_start"  />
        <result property="loadingTimeEnd"        column="loading_time_end"    />
        <result property="vehicleType"           column="vehicle_type"        />
        <result property="vehicleLength"         column="vehicle_length"      />
        <result property="vehicleCount"          column="vehicle_count"       />
        <result property="specialRequirements"   column="special_requirements"/>
        <result property="status"                column="status"              />
        <result property="isFrequentSource"      column="is_frequent_source"  />
        <result property="aiScheduleStatus"      column="ai_schedule_status"  />
        <result property="transportCapacity"     column="transport_capacity"  />
        <result property="onlineTime"            column="online_time"         />
        <result property="offlineTime"           column="offline_time"        />
        <result property="createBy"              column="create_by"           />
        <result property="createTime"            column="create_time"         />
        <result property="updateBy"              column="update_by"           />
        <result property="updateTime"            column="update_time"         />
        <result property="remark"                column="remark"              />
    </resultMap>
    
    <resultMap id="DailyCargoWithCarrierResult" type="com.zly.project.cargo.controller.vo.DailyCargoRespVo">
        <id     property="id"                   column="id"                   />
        <result property="customerId"           column="customer_id"          />
        <result property="taskId"               column="task_id"              />
        <result property="frequentCargoId"      column="frequent_cargo_id"    />
        <result property="customerName"          column="customer_name"         />
        <result property="cargoName"            column="cargo_name"           />
        <result property="cargoType"            column="cargo_type"           />
        <result property="packagingMethod"      column="packaging_method"     />
        <result property="cargoWeight"          column="cargo_weight"         />
        <result property="cargoVolume"          column="cargo_volume"         />
        <result property="loadingAddress"       column="loading_full_address" />
        <result property="loadingLongitude"     column="loading_longitude"    />
        <result property="loadingLatitude"      column="loading_latitude"     />
        <result property="unloadingAddress"     column="unloading_full_address"/>
        <result property="unloadingLongitude"   column="unloading_longitude"  />
        <result property="unloadingLatitude"    column="unloading_latitude"   />
        <result property="loadingDate"          column="loading_date"         />
        <result property="loadingTime"          column="loading_time"         />
        <result property="vehicleType"          column="vehicle_type"         />
        <result property="vehicleLength"        column="vehicle_length"       />
        <result property="vehicleCount"         column="vehicle_count"        />
        <result property="specialRequirements"  column="special_requirements" />
        <result property="contactPerson"        column="contact_person"       />
        <result property="carrierContact"       column="contact_phone"        />
        <result property="aiScheduleStatus"     column="ai_schedule_status"   />
        <result property="transportCapacity"    column="transport_capacity"   />
        <result property="isFrequentSource"     column="is_frequent_source"   />
        <result property="status"               column="status"               />
        <result property="onlineTime"           column="online_time"          />
        <result property="offlineTime"          column="offline_time"         />
        <result property="createTime"           column="create_time"          />
        <result property="updateTime"           column="update_time"          />
    </resultMap>

    <sql id="selectDailyCargoVo">
        select id, customer_id, task_id, frequent_cargo_id, cargo_name, cargo_type, packaging_method, cargo_weight, cargo_volume,
               loading_province_code, loading_province_name, loading_city_code, loading_city_name, 
               loading_district_code, loading_district_name, loading_address, loading_longitude, loading_latitude,
               unloading_province_code, unloading_province_name, unloading_city_code, unloading_city_name, 
               unloading_district_code, unloading_district_name, unloading_address, unloading_longitude, unloading_latitude,
               loading_date, loading_time_start, loading_time_end, vehicle_type, vehicle_length, vehicle_count, 
               special_requirements, status, is_frequent_source, ai_schedule_status, transport_capacity,
               online_time, offline_time, create_by, create_time, update_by, update_time, remark
        from daily_cargo
    </sql>
    
    <select id="selectDailyCargoById" parameterType="Long" resultMap="DailyCargoResult">
        <include refid="selectDailyCargoVo"/>
        where id = #{id}
    </select>
    
    <select id="selectDailyCargoList" parameterType="com.zly.project.cargo.domain.DailyCargo" resultMap="DailyCargoResult">
        <include refid="selectDailyCargoVo"/>
        <where>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="customerId != null">
                AND customer_id = #{customerId}
            </if>
            <if test="cargoName != null and cargoName != ''">
                AND cargo_name like concat('%', #{cargoName}, '%')
            </if>
            <if test="loadingAddress != null and loadingAddress != ''">
                AND loading_address like concat('%', #{loadingAddress}, '%')
            </if>
            <if test="unloadingAddress != null and unloadingAddress != ''">
                AND unloading_address like concat('%', #{unloadingAddress}, '%')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDailyCargoListWithCarrier" parameterType="com.zly.project.cargo.controller.vo.DailyCargoQueryVo" resultMap="DailyCargoWithCarrierResult">
        SELECT 
            dc.id, 
            dc.customer_id,
            dc.task_id,
            dc.frequent_cargo_id, 
            ci.customer_name,
            dc.cargo_name,
            dc.cargo_type,
            dc.packaging_method,
            dc.cargo_weight,
            dc.cargo_volume,
            CONCAT(IFNULL(dc.loading_province_name, ''), IFNULL(dc.loading_city_name, ''), IFNULL(dc.loading_district_name, ''), IFNULL(dc.loading_address, '')) AS loading_full_address,
            dc.loading_longitude,
            dc.loading_latitude,
            CONCAT(IFNULL(dc.unloading_province_name, ''), IFNULL(dc.unloading_city_name, ''), IFNULL(dc.unloading_district_name, ''), IFNULL(dc.unloading_address, '')) AS unloading_full_address,
            dc.unloading_longitude,
            dc.unloading_latitude,
            dc.loading_date,
            CONCAT(IFNULL(dc.loading_time_start, ''), '-', IFNULL(dc.loading_time_end, '')) AS loading_time,
            dc.vehicle_type,
            dc.vehicle_length,
            dc.vehicle_count,
            dc.special_requirements,
            ci.contact_person,
            ci.contact_phone,
            dc.ai_schedule_status,
            dc.transport_capacity,
            dc.is_frequent_source,
            dc.status,
            dc.online_time,
            dc.offline_time,
            dc.create_time,
            dc.update_time
        FROM 
            daily_cargo dc
        LEFT JOIN 
            customer_info ci ON dc.customer_id = ci.id
        <where>
            <if test="status != null and status != ''">
                AND dc.status = #{status}
                <if test="status == '1'">
                    <!-- 下架货源默认展示下架时间在近30天的运单 -->
                    <if test="offlineTimeStart == null and offlineTimeEnd == null">
                        AND dc.offline_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    </if>
                </if>
            </if>
            <if test="customerIdList != null and customerIdList.size() > 0">
                and dc.customer_id IN
                <foreach collection="customerIdList" item="customerId" open="(" close=")" separator=",">
                    #{customerId}
                </foreach>
            </if>
            <if test="aiScheduleStatus != null and aiScheduleStatus != ''">
                AND dc.ai_schedule_status = #{aiScheduleStatus}
            </if>
            <if test="transportCapacity != null and transportCapacity != ''">
                AND dc.transport_capacity = #{transportCapacity}
            </if>
            <if test="customerName != null and customerName != ''">
                AND ci.customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="cargoName != null and cargoName != ''">
                AND dc.cargo_name like concat('%', #{cargoName}, '%')
            </if>
            <if test="loadingAddress != null and loadingAddress != ''">
                AND (
                    dc.loading_province_name like concat('%', #{loadingAddress}, '%') OR
                    dc.loading_city_name like concat('%', #{loadingAddress}, '%') OR
                    dc.loading_district_name like concat('%', #{loadingAddress}, '%') OR
                    dc.loading_address like concat('%', #{loadingAddress}, '%')
                )
            </if>
            <if test="unloadingAddress != null and unloadingAddress != ''">
                AND (
                    dc.unloading_province_name like concat('%', #{unloadingAddress}, '%') OR
                    dc.unloading_city_name like concat('%', #{unloadingAddress}, '%') OR
                    dc.unloading_district_name like concat('%', #{unloadingAddress}, '%') OR
                    dc.unloading_address like concat('%', #{unloadingAddress}, '%')
                )
            </if>
            <if test="onlineTimeStart != null">
                AND dc.online_time >= #{onlineTimeStart}
            </if>
            <if test="onlineTimeEnd != null">
                AND dc.online_time &lt;= #{onlineTimeEnd}
            </if>
            <if test="offlineTimeStart != null">
                AND dc.offline_time >= #{offlineTimeStart}
            </if>
            <if test="offlineTimeEnd != null">
                AND dc.offline_time &lt;= #{offlineTimeEnd}
            </if>
        </where>
        <choose>
            <when test="status != null and status == 0">
                <!-- 上架货源按照装车日期与当前日期的比较来排序 -->
                order by
                <!-- 先按照装货日期与当前日期的关系排序 -->
                CASE
                <!-- 装货日期小于当前日期的排在后面（过期的排在前面） -->
                WHEN dc.loading_date &lt; CURDATE() THEN -1
                <!-- 装货日期等于当前日期的排在中间 -->
                WHEN dc.loading_date = CURDATE() THEN 0
                <!-- 装货日期大于当前日期的排在前面（未来的排在后面） -->
                ELSE 1
                END,
                <!-- 对于装货日期小于当前日期的（过期的），按照日期倒序排序 -->
                CASE WHEN dc.loading_date &lt; CURDATE() THEN dc.loading_date END ASC,
                <!-- 对于装货日期等于或大于当前日期的，按照日期正序排序 -->
                CASE WHEN dc.loading_date >= CURDATE() THEN dc.loading_date END ASC,
                <!-- 装货日期相同的情况下，按照装货时间结束时间正序排序 -->
                dc.loading_time_end asc,dc.loading_time_start desc
            </when>
            <when test="status != null and status == 1">
                <!-- 下架货源按照下架时间倒序，下架时间一致时按照上架时间正序 -->
                order by dc.offline_time desc, dc.online_time asc
            </when>
            <otherwise>
                order by dc.create_time desc
            </otherwise>
        </choose>
    </select>
    
    <insert id="insertDailyCargo" parameterType="com.zly.project.cargo.domain.DailyCargo" useGeneratedKeys="true" keyProperty="id">
        insert into daily_cargo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="frequentCargoId != null">frequent_cargo_id,</if>
            <if test="cargoName != null and cargoName != ''">cargo_name,</if>
            <if test="cargoType != null and cargoType != ''">cargo_type,</if>
            <if test="packagingMethod != null and packagingMethod != ''">packaging_method,</if>
            <if test="cargoWeight != null">cargo_weight,</if>
            <if test="cargoVolume != null">cargo_volume,</if>
            <if test="loadingProvinceCode != null and loadingProvinceCode != ''">loading_province_code,</if>
            <if test="loadingProvinceName != null and loadingProvinceName != ''">loading_province_name,</if>
            <if test="loadingCityCode != null and loadingCityCode != ''">loading_city_code,</if>
            <if test="loadingCityName != null and loadingCityName != ''">loading_city_name,</if>
            <if test="loadingDistrictCode != null and loadingDistrictCode != ''">loading_district_code,</if>
            <if test="loadingDistrictName != null and loadingDistrictName != ''">loading_district_name,</if>
            <if test="loadingAddress != null and loadingAddress != ''">loading_address,</if>
            <if test="loadingLongitude != null">loading_longitude,</if>
            <if test="loadingLatitude != null">loading_latitude,</if>
            <if test="unloadingProvinceCode != null and unloadingProvinceCode != ''">unloading_province_code,</if>
            <if test="unloadingProvinceName != null and unloadingProvinceName != ''">unloading_province_name,</if>
            <if test="unloadingCityCode != null and unloadingCityCode != ''">unloading_city_code,</if>
            <if test="unloadingCityName != null and unloadingCityName != ''">unloading_city_name,</if>
            <if test="unloadingDistrictCode != null and unloadingDistrictCode != ''">unloading_district_code,</if>
            <if test="unloadingDistrictName != null and unloadingDistrictName != ''">unloading_district_name,</if>
            <if test="unloadingAddress != null and unloadingAddress != ''">unloading_address,</if>
            <if test="unloadingLongitude != null">unloading_longitude,</if>
            <if test="unloadingLatitude != null">unloading_latitude,</if>
            <if test="loadingDate != null">loading_date,</if>
            <if test="loadingTimeStart != null and loadingTimeStart != ''">loading_time_start,</if>
            <if test="loadingTimeEnd != null and loadingTimeEnd != ''">loading_time_end,</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type,</if>
            <if test="vehicleLength != null">vehicle_length,</if>
            <if test="vehicleCount != null">vehicle_count,</if>
            <if test="specialRequirements != null and specialRequirements != ''">special_requirements,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="isFrequentSource != null and isFrequentSource != ''">is_frequent_source,</if>
            <if test="aiScheduleStatus != null and aiScheduleStatus != ''">ai_schedule_status,</if>
            <if test="transportCapacity != null and transportCapacity != ''">transport_capacity,</if>
            <if test="onlineTime != null">online_time,</if>
            <if test="offlineTime != null">offline_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="frequentCargoId != null">#{frequentCargoId},</if>
            <if test="cargoName != null and cargoName != ''">#{cargoName},</if>
            <if test="cargoType != null and cargoType != ''">#{cargoType},</if>
            <if test="packagingMethod != null and packagingMethod != ''">#{packagingMethod},</if>
            <if test="cargoWeight != null">#{cargoWeight},</if>
            <if test="cargoVolume != null">#{cargoVolume},</if>
            <if test="loadingProvinceCode != null and loadingProvinceCode != ''">#{loadingProvinceCode},</if>
            <if test="loadingProvinceName != null and loadingProvinceName != ''">#{loadingProvinceName},</if>
            <if test="loadingCityCode != null and loadingCityCode != ''">#{loadingCityCode},</if>
            <if test="loadingCityName != null and loadingCityName != ''">#{loadingCityName},</if>
            <if test="loadingDistrictCode != null and loadingDistrictCode != ''">#{loadingDistrictCode},</if>
            <if test="loadingDistrictName != null and loadingDistrictName != ''">#{loadingDistrictName},</if>
            <if test="loadingAddress != null and loadingAddress != ''">#{loadingAddress},</if>
            <if test="loadingLongitude != null">#{loadingLongitude},</if>
            <if test="loadingLatitude != null">#{loadingLatitude},</if>
            <if test="unloadingProvinceCode != null and unloadingProvinceCode != ''">#{unloadingProvinceCode},</if>
            <if test="unloadingProvinceName != null and unloadingProvinceName != ''">#{unloadingProvinceName},</if>
            <if test="unloadingCityCode != null and unloadingCityCode != ''">#{unloadingCityCode},</if>
            <if test="unloadingCityName != null and unloadingCityName != ''">#{unloadingCityName},</if>
            <if test="unloadingDistrictCode != null and unloadingDistrictCode != ''">#{unloadingDistrictCode},</if>
            <if test="unloadingDistrictName != null and unloadingDistrictName != ''">#{unloadingDistrictName},</if>
            <if test="unloadingAddress != null and unloadingAddress != ''">#{unloadingAddress},</if>
            <if test="unloadingLongitude != null">#{unloadingLongitude},</if>
            <if test="unloadingLatitude != null">#{unloadingLatitude},</if>
            <if test="loadingDate != null">#{loadingDate},</if>
            <if test="loadingTimeStart != null and loadingTimeStart != ''">#{loadingTimeStart},</if>
            <if test="loadingTimeEnd != null and loadingTimeEnd != ''">#{loadingTimeEnd},</if>
            <if test="vehicleType != null and vehicleType != ''">#{vehicleType},</if>
            <if test="vehicleLength != null">#{vehicleLength},</if>
            <if test="vehicleCount != null">#{vehicleCount},</if>
            <if test="specialRequirements != null and specialRequirements != ''">#{specialRequirements},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="isFrequentSource != null and isFrequentSource != ''">#{isFrequentSource},</if>
            <if test="aiScheduleStatus != null and aiScheduleStatus != ''">#{aiScheduleStatus},</if>
            <if test="transportCapacity != null and transportCapacity != ''">#{transportCapacity},</if>
            <if test="onlineTime != null">#{onlineTime},</if>
            <if test="offlineTime != null">#{offlineTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>
    
    <update id="updateDailyCargo" parameterType="com.zly.project.cargo.domain.DailyCargo">
        update daily_cargo
        <trim prefix="set" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="frequentCargoId != null">frequent_cargo_id = #{frequentCargoId},</if>
            <if test="cargoName != null and cargoName != ''">cargo_name = #{cargoName},</if>
            <if test="cargoType != null and cargoType != ''">cargo_type = #{cargoType},</if>
            <if test="packagingMethod != null and packagingMethod != ''">packaging_method = #{packagingMethod},</if>
            <if test="cargoWeight != null">cargo_weight = #{cargoWeight},</if>
            <if test="cargoVolume != null">cargo_volume = #{cargoVolume},</if>
            <if test="loadingProvinceCode != null and loadingProvinceCode != ''">loading_province_code = #{loadingProvinceCode},</if>
            <if test="loadingProvinceName != null and loadingProvinceName != ''">loading_province_name = #{loadingProvinceName},</if>
            <if test="loadingCityCode != null and loadingCityCode != ''">loading_city_code = #{loadingCityCode},</if>
            <if test="loadingCityName != null and loadingCityName != ''">loading_city_name = #{loadingCityName},</if>
            <if test="loadingDistrictCode != null and loadingDistrictCode != ''">loading_district_code = #{loadingDistrictCode},</if>
            <if test="loadingDistrictName != null and loadingDistrictName != ''">loading_district_name = #{loadingDistrictName},</if>
            <if test="loadingAddress != null and loadingAddress != ''">loading_address = #{loadingAddress},</if>
            <if test="loadingLongitude != null">loading_longitude = #{loadingLongitude},</if>
            <if test="loadingLatitude != null">loading_latitude = #{loadingLatitude},</if>
            <if test="unloadingProvinceCode != null and unloadingProvinceCode != ''">unloading_province_code = #{unloadingProvinceCode},</if>
            <if test="unloadingProvinceName != null and unloadingProvinceName != ''">unloading_province_name = #{unloadingProvinceName},</if>
            <if test="unloadingCityCode != null and unloadingCityCode != ''">unloading_city_code = #{unloadingCityCode},</if>
            <if test="unloadingCityName != null and unloadingCityName != ''">unloading_city_name = #{unloadingCityName},</if>
            <if test="unloadingDistrictCode != null and unloadingDistrictCode != ''">unloading_district_code = #{unloadingDistrictCode},</if>
            <if test="unloadingDistrictName != null and unloadingDistrictName != ''">unloading_district_name = #{unloadingDistrictName},</if>
            <if test="unloadingAddress != null and unloadingAddress != ''">unloading_address = #{unloadingAddress},</if>
            <if test="unloadingLongitude != null">unloading_longitude = #{unloadingLongitude},</if>
            <if test="unloadingLatitude != null">unloading_latitude = #{unloadingLatitude},</if>
            <if test="loadingDate != null">loading_date = #{loadingDate},</if>
            <if test="loadingTimeStart != null and loadingTimeStart != ''">loading_time_start = #{loadingTimeStart},</if>
            <if test="loadingTimeEnd != null and loadingTimeEnd != ''">loading_time_end = #{loadingTimeEnd},</if>
            <if test="vehicleType != null and vehicleType != ''">vehicle_type = #{vehicleType},</if>
            <if test="vehicleLength != null">vehicle_length = #{vehicleLength},</if>
            <if test="vehicleCount != null">vehicle_count = #{vehicleCount},</if>
            <if test="specialRequirements != null and specialRequirements != ''">special_requirements = #{specialRequirements},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="isFrequentSource != null and isFrequentSource != ''">is_frequent_source = #{isFrequentSource},</if>
            <if test="aiScheduleStatus != null and aiScheduleStatus != ''">ai_schedule_status = #{aiScheduleStatus},</if>
            <if test="transportCapacity != null and transportCapacity != ''">transport_capacity = #{transportCapacity},</if>
            <if test="onlineTime != null">online_time = #{onlineTime},</if>
            <if test="offlineTime != null">offline_time = #{offlineTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectNormalCargos" resultMap="DailyCargoResult">
        <include refid="selectDailyCargoVo"/>
        where status = '0' <!-- 正常状态 -->
        order by loading_date asc
    </select>
    
    <select id="selectInterestedDriverCountByCargos" resultType="java.util.Map" parameterType="java.util.List">
        SELECT 
            dc.id as customerId,
            COUNT(DISTINCT stdm.id) as interestedDriverCount
        FROM 
            daily_cargo dc
        LEFT JOIN 
            scheduler_task_plan stp ON dc.task_id = stp.id
        LEFT JOIN 
            scheduler_task_driver_match stdm ON stp.id = stdm.task_plan_id AND stdm.has_intention = 1 AND stdm.deleted = 0
        WHERE 
            dc.id IN
            <foreach item="id" collection="list" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND (dc.ai_schedule_status = '1' OR dc.ai_schedule_status = '2')
        GROUP BY 
            dc.id
    </select>
</mapper> 