<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.cargo.mapper.CarrierDriverMapper">

    <resultMap type="com.zly.project.cargo.domain.CarrierDriver" id="CarrierDriverResult">
        <id     property="id"                   column="id"                   />
        <result property="cargoId"              column="cargo_id"             />
        <result property="taskId"               column="task_id"              />
        <result property="taskDriverMatchId"    column="task_driver_match_id" />
        <result property="driverName"           column="driver_name"          />
        <result property="idCard"               column="id_card"              />
        <result property="phoneNumber"          column="phone_number"         />
        <result property="plateNumber"          column="plate_number"         />
        <result property="plateColor"           column="plate_color"          />
        <result property="acceptTime"           column="accept_time"          />
        <result property="carrierStatus"        column="carrier_status"       />
        <result property="status"               column="status"               />
        <result property="createBy"             column="create_by"            />
        <result property="createTime"           column="create_time"          />
        <result property="updateBy"             column="update_by"            />
        <result property="updateTime"           column="update_time"          />
        <result property="remark"               column="remark"               />
    </resultMap>

    <sql id="selectCarrierDriverVo">
        select id, cargo_id, task_id, task_driver_match_id, driver_name, id_card, phone_number, plate_number, plate_color,
               accept_time, carrier_status, status, create_by, create_time, update_by, update_time, remark
        from customer_driver
    </sql>
    
    <select id="selectCarrierDriverById" parameterType="Long" resultMap="CarrierDriverResult">
        <include refid="selectCarrierDriverVo"/>
        where id = #{id}
    </select>
    
    <select id="selectCarrierDriverList" parameterType="com.zly.project.cargo.domain.CarrierDriver" resultMap="CarrierDriverResult">
        <include refid="selectCarrierDriverVo"/>
        <where>
            <if test="cargoId != null">
                AND cargo_id = #{cargoId}
            </if>
            <if test="taskId != null">
                AND task_id = #{taskId}
            </if>
            <if test="driverName != null and driverName != ''">
                AND driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="idCard != null and idCard != ''">
                AND id_card = #{idCard}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                AND phone_number = #{phoneNumber}
            </if>
            <if test="plateNumber != null and plateNumber != ''">
                AND plate_number like concat('%', #{plateNumber}, '%')
            </if>
            <if test="plateColor != null and plateColor != ''">
                AND plate_color = #{plateColor}
            </if>
            <if test="carrierStatus != null and carrierStatus != ''">
                AND carrier_status = #{carrierStatus}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="countDriversByCargoId" parameterType="Long" resultType="Integer">
        select count(1) from customer_driver where cargo_id = #{cargoId} and status = '0'
    </select>
    
    <select id="countDriversByCargoIds" resultType="java.util.Map" parameterType="java.util.List">
        SELECT 
            cargo_id as cargoId,
            COUNT(1) as driverCount
        FROM 
            customer_driver
        WHERE 
            cargo_id IN
            <foreach item="id" collection="list" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND status = '0'
        GROUP BY 
            cargo_id
    </select>
    
    <select id="selectDriversByCustomerId" parameterType="Long" resultMap="CarrierDriverResult">
        <include refid="selectCarrierDriverVo"/>
        where cargo_id = #{cargoId} and status = '0'
        order by create_time desc
    </select>
    
    <select id="selectDriversByTaskDriverMatchId" parameterType="Long" resultMap="CarrierDriverResult">
        <include refid="selectCarrierDriverVo"/>
        where task_driver_match_id = #{taskDriverMatchId} and status = '0'
        order by create_time desc
    </select>
    
    <insert id="insertCarrierDriver" parameterType="com.zly.project.cargo.domain.CarrierDriver" useGeneratedKeys="true" keyProperty="id">
        insert into customer_driver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="cargoId != null">cargo_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskDriverMatchId != null">task_driver_match_id,</if>
            <if test="driverName != null and driverName != ''">driver_name,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="plateNumber != null and plateNumber != ''">plate_number,</if>
            <if test="plateColor != null and plateColor != ''">plate_color,</if>
            <if test="acceptTime != null">accept_time,</if>
            <if test="carrierStatus != null and carrierStatus != ''">carrier_status,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="cargoId != null">#{cargoId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskDriverMatchId != null">#{taskDriverMatchId},</if>
            <if test="driverName != null and driverName != ''">#{driverName},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="plateNumber != null and plateNumber != ''">#{plateNumber},</if>
            <if test="plateColor != null and plateColor != ''">#{plateColor},</if>
            <if test="acceptTime != null">#{acceptTime},</if>
            <if test="carrierStatus != null and carrierStatus != ''">#{carrierStatus},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>
    
    <update id="updateCarrierDriver" parameterType="com.zly.project.cargo.domain.CarrierDriver">
        update customer_driver
        <trim prefix="set" suffixOverrides=",">
            <if test="cargoId != null">cargo_id = #{cargoId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskDriverMatchId != null">task_driver_match_id = #{taskDriverMatchId},</if>
            <if test="driverName != null and driverName != ''">driver_name = #{driverName},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="plateNumber != null and plateNumber != ''">plate_number = #{plateNumber},</if>
            <if test="plateColor != null and plateColor != ''">plate_color = #{plateColor},</if>
            <if test="acceptTime != null">accept_time = #{acceptTime},</if>
            <if test="carrierStatus != null and carrierStatus != ''">carrier_status = #{carrierStatus},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
</mapper> 