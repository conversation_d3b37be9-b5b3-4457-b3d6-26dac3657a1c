<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.plan.mapper.TaskPlanMapper">
    
    <resultMap type="com.zly.project.plan.domain.TaskPlanDO" id="TaskPlanResult">
        <id     property="id"                   column="id"                    />
        <result property="dailyCargoId"         column="daily_cargo_id"        />
        <result property="planName"             column="plan_name"             />
        <result property="startPoint"           column="start_point"           />
        <result property="endPoint"             column="end_point"             />
        <result property="cargoName"            column="cargo_name"            />
        <result property="packagingMethod"      column="packaging_method"      />
        <result property="cargoWeight"          column="cargo_weight"          />
        <result property="cargoVolume"          column="cargo_volume"          />
        <result property="loadingProvinceCode"  column="loading_province_code" />
        <result property="loadingProvinceName"  column="loading_province_name" />
        <result property="loadingCityCode"      column="loading_city_code"     />
        <result property="loadingCityName"      column="loading_city_name"     />
        <result property="loadingDistrictCode"  column="loading_district_code" />
        <result property="loadingDistrictName"  column="loading_district_name" />
        <result property="loadingAddress"       column="loading_address"       />
        <result property="loadingLongitude"     column="loading_longitude"     />
        <result property="loadingLatitude"      column="loading_latitude"      />
        <result property="unloadingProvinceCode" column="unloading_province_code" />
        <result property="unloadingProvinceName" column="unloading_province_name" />
        <result property="unloadingCityCode"    column="unloading_city_code"   />
        <result property="unloadingCityName"    column="unloading_city_name"   />
        <result property="unloadingDistrictCode" column="unloading_district_code" />
        <result property="unloadingDistrictName" column="unloading_district_name" />
        <result property="unloadingAddress"     column="unloading_address"     />
        <result property="unloadingLongitude"   column="unloading_longitude"   />
        <result property="unloadingLatitude"    column="unloading_latitude"    />
        <result property="vehicleType"          column="vehicle_type"          />
        <result property="vehicleLength"        column="vehicle_length"        />
        <result property="vehicleCount"         column="vehicle_count"         />
        <result property="initiator"            column="initiator"             />
        <result property="timeoutStatus"        column="timeout_status"        />
        <result property="interestedDriverCount" column="interested_driver_count" />
        <result property="planStartTime"        column="plan_start_time"       />
        <result property="planEndTime"          column="plan_end_time"         />
        <result property="estimatedCost"        column="estimated_cost"        />
        <result property="requirements"         column="requirements"          />
        <result property="status"               column="status"                />
        <result property="dispatcherName"       column="dispatcher_name"       />
        <result property="dispatcherPhone"      column="dispatcher_phone"      />
        <result property="communicationMethod"  column="communication_method"  />
        <result property="isManualPause"        column="is_manual_pause"       />
        <result property="isAutoPause"          column="is_auto_pause"         />
        <result property="sessionType"          column="session_type"         />
        <result property="sessionTaskId"        column="session_task_id"         />
        <result property="outTaskId"            column="out_task_id"         />
        <result property="taskState"            column="task_state"         />
        <result property="completeTime"         column="complete_time"         />
        <result property="cancelTime"           column="cancel_time"         />
        <result property="remark"               column="remark"                />
        <result property="createBy"             column="create_by"             />
        <result property="createTime"           column="create_time"           />
        <result property="updateBy"             column="update_by"             />
        <result property="updateTime"           column="update_time"           />
        <result property="deleted"              column="deleted"               />
    </resultMap>

    <sql id="selectTaskPlanVo">
        select id, daily_cargo_id, plan_name, start_point, end_point, cargo_name, 
               packaging_method, cargo_weight, cargo_volume,
               loading_province_code, loading_province_name, loading_city_code, loading_city_name, 
               loading_district_code, loading_district_name, loading_address, loading_longitude, loading_latitude,
               unloading_province_code, unloading_province_name, unloading_city_code, unloading_city_name, 
               unloading_district_code, unloading_district_name, unloading_address, unloading_longitude, unloading_latitude,
               vehicle_type, vehicle_length, 
               vehicle_count, initiator, timeout_status, interested_driver_count, plan_start_time, 
               plan_end_time, estimated_cost, requirements, status, dispatcher_name, dispatcher_phone, 
               communication_method, is_manual_pause, is_auto_pause, session_type, session_task_id, out_task_id, task_state, complete_time, cancel_time, remark, create_by, create_time, update_by, update_time, deleted
        from scheduler_task_plan
    </sql>

    <select id="selectTaskPlanList" parameterType="com.zly.project.plan.controller.vo.TaskPlanPageReqVO" resultMap="TaskPlanResult">
        <include refid="selectTaskPlanVo"/>
        <where>
            deleted = 0
            <if test="planName != null and planName != ''">
                AND plan_name like concat('%', #{planName}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectListByStatus" parameterType="Integer" resultMap="TaskPlanResult">
        <include refid="selectTaskPlanVo"/>
        where status = #{status} and deleted = 0
        order by create_time desc
    </select>
    
    <select id="selectTaskPlanById" parameterType="Long" resultMap="TaskPlanResult">
        <include refid="selectTaskPlanVo"/>
        where id = #{id} and deleted = 0
    </select>
    
    <select id="selectTasksBatchIds" parameterType="Collection" resultMap="TaskPlanResult">
        <include refid="selectTaskPlanVo"/>
        where id in
        <foreach item="id" collection="collection" open="(" separator="," close=")">
            #{id}
        </foreach>
        and deleted = 0
    </select>
    
    <insert id="insertTaskPlan" parameterType="com.zly.project.plan.domain.TaskPlanDO" useGeneratedKeys="true" keyProperty="id">
        insert into scheduler_task_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dailyCargoId != null">daily_cargo_id,</if>
            <if test="planName != null">plan_name,</if>
            <if test="startPoint != null">start_point,</if>
            <if test="endPoint != null">end_point,</if>
            <if test="cargoName != null">cargo_name,</if>
            <if test="packagingMethod != null">packaging_method,</if>
            <if test="cargoWeight != null">cargo_weight,</if>
            <if test="cargoVolume != null">cargo_volume,</if>
            <if test="loadingProvinceCode != null">loading_province_code,</if>
            <if test="loadingProvinceName != null">loading_province_name,</if>
            <if test="loadingCityCode != null">loading_city_code,</if>
            <if test="loadingCityName != null">loading_city_name,</if>
            <if test="loadingDistrictCode != null">loading_district_code,</if>
            <if test="loadingDistrictName != null">loading_district_name,</if>
            <if test="loadingAddress != null">loading_address,</if>
            <if test="loadingLongitude != null">loading_longitude,</if>
            <if test="loadingLatitude != null">loading_latitude,</if>
            <if test="unloadingProvinceCode != null">unloading_province_code,</if>
            <if test="unloadingProvinceName != null">unloading_province_name,</if>
            <if test="unloadingCityCode != null">unloading_city_code,</if>
            <if test="unloadingCityName != null">unloading_city_name,</if>
            <if test="unloadingDistrictCode != null">unloading_district_code,</if>
            <if test="unloadingDistrictName != null">unloading_district_name,</if>
            <if test="unloadingAddress != null">unloading_address,</if>
            <if test="unloadingLongitude != null">unloading_longitude,</if>
            <if test="unloadingLatitude != null">unloading_latitude,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="vehicleLength != null">vehicle_length,</if>
            <if test="vehicleCount != null">vehicle_count,</if>
            <if test="initiator != null">initiator,</if>
            <if test="timeoutStatus != null">timeout_status,</if>
            <if test="interestedDriverCount != null">interested_driver_count,</if>
            <if test="planStartTime != null">plan_start_time,</if>
            <if test="planEndTime != null">plan_end_time,</if>
            <if test="estimatedCost != null">estimated_cost,</if>
            <if test="requirements != null">requirements,</if>
            <if test="status != null">status,</if>
            <if test="dispatcherName != null">dispatcher_name,</if>
            <if test="dispatcherPhone != null">dispatcher_phone,</if>
            <if test="communicationMethod != null">communication_method,</if>
            <if test="isManualPause != null">is_manual_pause,</if>
            <if test="isAutoPause != null">is_auto_pause,</if>
            <if test="sessionType != null">session_type,</if>
            <if test="sessionTaskId != null">session_task_id,</if>
            <if test="outTaskId != null">out_task_id,</if>
            <if test="taskState != null">task_state,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dailyCargoId != null">#{dailyCargoId},</if>
            <if test="planName != null">#{planName},</if>
            <if test="startPoint != null">#{startPoint},</if>
            <if test="endPoint != null">#{endPoint},</if>
            <if test="cargoName != null">#{cargoName},</if>
            <if test="packagingMethod != null">#{packagingMethod},</if>
            <if test="cargoWeight != null">#{cargoWeight},</if>
            <if test="cargoVolume != null">#{cargoVolume},</if>
            <if test="loadingProvinceCode != null">#{loadingProvinceCode},</if>
            <if test="loadingProvinceName != null">#{loadingProvinceName},</if>
            <if test="loadingCityCode != null">#{loadingCityCode},</if>
            <if test="loadingCityName != null">#{loadingCityName},</if>
            <if test="loadingDistrictCode != null">#{loadingDistrictCode},</if>
            <if test="loadingDistrictName != null">#{loadingDistrictName},</if>
            <if test="loadingAddress != null">#{loadingAddress},</if>
            <if test="loadingLongitude != null">#{loadingLongitude},</if>
            <if test="loadingLatitude != null">#{loadingLatitude},</if>
            <if test="unloadingProvinceCode != null">#{unloadingProvinceCode},</if>
            <if test="unloadingProvinceName != null">#{unloadingProvinceName},</if>
            <if test="unloadingCityCode != null">#{unloadingCityCode},</if>
            <if test="unloadingCityName != null">#{unloadingCityName},</if>
            <if test="unloadingDistrictCode != null">#{unloadingDistrictCode},</if>
            <if test="unloadingDistrictName != null">#{unloadingDistrictName},</if>
            <if test="unloadingAddress != null">#{unloadingAddress},</if>
            <if test="unloadingLongitude != null">#{unloadingLongitude},</if>
            <if test="unloadingLatitude != null">#{unloadingLatitude},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="vehicleLength != null">#{vehicleLength},</if>
            <if test="vehicleCount != null">#{vehicleCount},</if>
            <if test="initiator != null">#{initiator},</if>
            <if test="timeoutStatus != null">#{timeoutStatus},</if>
            <if test="interestedDriverCount != null">#{interestedDriverCount},</if>
            <if test="planStartTime != null">#{planStartTime},</if>
            <if test="planEndTime != null">#{planEndTime},</if>
            <if test="estimatedCost != null">#{estimatedCost},</if>
            <if test="requirements != null">#{requirements},</if>
            <if test="status != null">#{status},</if>
            <if test="dispatcherName != null">#{dispatcherName},</if>
            <if test="dispatcherPhone != null">#{dispatcherPhone},</if>
            <if test="communicationMethod != null">#{communicationMethod},</if>
            <if test="isManualPause != null">#{isManualPause},</if>
            <if test="isAutoPause != null">#{isAutoPause},</if>
            <if test="sessionType != null">#{sessionType},</if>
            <if test="sessionTaskId != null">#{sessionTaskId},</if>
            <if test="outTaskId != null">#{outTaskId},</if>
            <if test="taskState != null">#{taskState},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            now(),
            <if test="updateBy != null">#{updateBy},</if>
            now(),
            0,
        </trim>
    </insert>
    
    <update id="updateTaskPlanById" parameterType="com.zly.project.plan.domain.TaskPlanDO">
        update scheduler_task_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="dailyCargoId != null">daily_cargo_id = #{dailyCargoId},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="startPoint != null">start_point = #{startPoint},</if>
            <if test="endPoint != null">end_point = #{endPoint},</if>
            <if test="cargoName != null">cargo_name = #{cargoName},</if>
            <if test="packagingMethod != null">packaging_method = #{packagingMethod},</if>
            <if test="cargoWeight != null">cargo_weight = #{cargoWeight},</if>
            <if test="cargoVolume != null">cargo_volume = #{cargoVolume},</if>
            <if test="loadingProvinceCode != null">loading_province_code = #{loadingProvinceCode},</if>
            <if test="loadingProvinceName != null">loading_province_name = #{loadingProvinceName},</if>
            <if test="loadingCityCode != null">loading_city_code = #{loadingCityCode},</if>
            <if test="loadingCityName != null">loading_city_name = #{loadingCityName},</if>
            <if test="loadingDistrictCode != null">loading_district_code = #{loadingDistrictCode},</if>
            <if test="loadingDistrictName != null">loading_district_name = #{loadingDistrictName},</if>
            <if test="loadingAddress != null">loading_address = #{loadingAddress},</if>
            <if test="loadingLongitude != null">loading_longitude = #{loadingLongitude},</if>
            <if test="loadingLatitude != null">loading_latitude = #{loadingLatitude},</if>
            <if test="unloadingProvinceCode != null">unloading_province_code = #{unloadingProvinceCode},</if>
            <if test="unloadingProvinceName != null">unloading_province_name = #{unloadingProvinceName},</if>
            <if test="unloadingCityCode != null">unloading_city_code = #{unloadingCityCode},</if>
            <if test="unloadingCityName != null">unloading_city_name = #{unloadingCityName},</if>
            <if test="unloadingDistrictCode != null">unloading_district_code = #{unloadingDistrictCode},</if>
            <if test="unloadingDistrictName != null">unloading_district_name = #{unloadingDistrictName},</if>
            <if test="unloadingAddress != null">unloading_address = #{unloadingAddress},</if>
            <if test="unloadingLongitude != null">unloading_longitude = #{unloadingLongitude},</if>
            <if test="unloadingLatitude != null">unloading_latitude = #{unloadingLatitude},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="vehicleLength != null">vehicle_length = #{vehicleLength},</if>
            <if test="vehicleCount != null">vehicle_count = #{vehicleCount},</if>
            <if test="initiator != null">initiator = #{initiator},</if>
            <if test="timeoutStatus != null">timeout_status = #{timeoutStatus},</if>
            <if test="interestedDriverCount != null">interested_driver_count = #{interestedDriverCount},</if>
            <if test="planStartTime != null">plan_start_time = #{planStartTime},</if>
            <if test="planEndTime != null">plan_end_time = #{planEndTime},</if>
            <if test="estimatedCost != null">estimated_cost = #{estimatedCost},</if>
            <if test="requirements != null">requirements = #{requirements},</if>
            <if test="status != null">status = #{status},</if>
            <if test="dispatcherName != null">dispatcher_name = #{dispatcherName},</if>
            <if test="dispatcherPhone != null">dispatcher_phone = #{dispatcherPhone},</if>
            <if test="communicationMethod != null">communication_method = #{communicationMethod},</if>
            <if test="isManualPause != null">is_manual_pause = #{isManualPause},</if>
            <if test="isAutoPause != null">is_auto_pause = #{isAutoPause},</if>
            <if test="sessionType != null">session_type = #{sessionType},</if>
            <if test="sessionTaskId != null">session_task_id = #{sessionTaskId},</if>
            <if test="outTaskId != null">out_task_id = #{outTaskId},</if>
            <if test="taskState != null">task_state = #{taskState},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now(),
        </trim>
        where id = #{id} and deleted = 0
    </update>
    
    <delete id="deleteTaskPlanById" parameterType="Long">
        update scheduler_task_plan set deleted = 1 where id = #{id}
    </delete>

    <select id="selectTaskPlansByCondition" parameterType="com.zly.project.plan.controller.vo.TaskPlanPageReqVO" resultMap="TaskPlanResult">
        <include refid="selectTaskPlanVo"/>
        <where>
            deleted = 0
            <if test="communicationMethod != null">
                and communication_method = #{communicationMethod}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="isManualPause != null">
                and is_manual_pause = #{isManualPause}
            </if>
            <if test="isAutoPause != null">
                and is_auto_pause = #{isAutoPause}
            </if>
            <!-- 未超时或即将超时的任务 -->
            and (timeout_status is null or timeout_status = 0 or timeout_status = 1)
        </where>
        order by create_time desc
    </select>
    <select id="getTaskPlanByOutTaskIdAndStatus" resultMap="TaskPlanResult">
        <include refid="selectTaskPlanVo"/>
        <where>
            deleted = 0
            <if test="outTaskId != null and outTaskId != ''">
                AND out_task_id = #{outTaskId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

</mapper>