<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.scheduler.mapper.CustomerContractMapper">
    
    <resultMap type="CustomerContract" id="CustomerContractResult">
        <id property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="contractStatus" column="contract_status"/>
        <result property="attachmentPath" column="attachment_path"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="selectCustomerContractVo">
        select id, customer_id, start_date, end_date, contract_status, attachment_path, create_by, create_time, update_by, update_time, remark, deleted from customer_contract
    </sql>

    <select id="selectCustomerContractList" parameterType="CustomerContract" resultMap="CustomerContractResult">
        <include refid="selectCustomerContractVo"/>
        <where>
            deleted = 0
            <if test="customerId != null">
                AND customer_id = #{customerId}
            </if>
            <if test="startDate != null">
                AND start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND end_date &lt;= #{endDate}
            </if>
            <if test="contractStatus != null and contractStatus != ''">
                AND contract_status = #{contractStatus}
            </if>
            <if test="attachmentPath != null and attachmentPath != ''">
                AND attachment_path = #{attachmentPath}
            </if>
        </where>
    </select>
    
    <select id="selectCustomerContractById" parameterType="Long" resultMap="CustomerContractResult">
        <include refid="selectCustomerContractVo"/>
        where id = #{id} and deleted = 0
    </select>
    
    <select id="selectCustomerContractsByCustomerId" parameterType="Long" resultMap="CustomerContractResult">
        <include refid="selectCustomerContractVo"/>
        where customer_id = #{customerId} and deleted = 0
        order by start_date desc
    </select>
        
    <insert id="insertCustomerContract" parameterType="CustomerContract" useGeneratedKeys="true" keyProperty="id">
        insert into customer_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="contractStatus != null">contract_status,</if>
            <if test="attachmentPath != null">attachment_path,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="contractStatus != null">#{contractStatus},</if>
            <if test="attachmentPath != null">#{attachmentPath},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            0,
        </trim>
    </insert>

    <update id="updateCustomerContract" parameterType="CustomerContract">
        update customer_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="contractStatus != null">contract_status = #{contractStatus},</if>
            <if test="attachmentPath != null">attachment_path = #{attachmentPath},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id} and deleted = 0
    </update>

    <update id="deleteCustomerContractById" parameterType="Long">
        update customer_contract set deleted = 1 where id = #{id}
    </update>

    <update id="deleteCustomerContractByIds" parameterType="String">
        update customer_contract set deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据多个货主ID查询所有合约 -->
    <select id="selectCustomerContractsByCustomerIds" parameterType="java.util.List" resultMap="CustomerContractResult">
        <include refid="selectCustomerContractVo"/>
        <where>
            deleted = 0
            AND customer_id in
            <foreach item="customerId" collection="list" open="(" separator="," close=")">
                #{customerId}
            </foreach>
        </where>
    </select>
</mapper> 