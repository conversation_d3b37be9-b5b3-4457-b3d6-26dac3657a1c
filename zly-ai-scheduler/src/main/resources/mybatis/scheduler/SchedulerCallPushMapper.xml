<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.scheduler.mapper.SchedulerCallPushMapper">
    
    <resultMap type="SchedulerCallPush" id="SchedulerCallPushResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="callid"    column="callid"    />
        <result property="groupId"    column="group_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="telephone"    column="telephone"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="operator"    column="operator"    />
        <result property="status"    column="status"    />
        <result property="statusStr"    column="status_str"    />
        <result property="calldate"    column="calldate"    />
        <result property="answerdate"    column="answerdate"    />
        <result property="hangupdate"    column="hangupdate"    />
        <result property="duration"    column="duration"    />
        <result property="bill"    column="bill"    />
        <result property="intentionResults"    column="intention_results"    />
        <result property="rounds"    column="rounds"    />
        <result property="score"    column="score"    />
        <result property="levelId"    column="level_id"    />
        <result property="levelName"    column="level_name"    />
        <result property="tags"    column="tags"    />
        <result property="voiceUrl"    column="voice_url"    />
        <result property="voiceText"    column="voice_text"    />
        <result property="extra"    column="extra"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSchedulerCallPushVo">
        select id, user_id, callid, group_id, group_name, task_id, task_name, customer_id, telephone, province, city, operator, status, status_str, calldate, answerdate, hangupdate, duration, bill, intention_results, rounds, score, level_id, level_name, tags, voice_url, voice_text, extra, create_time, update_time from scheduler_call_push
    </sql>

    <select id="selectSchedulerCallPushList" parameterType="SchedulerCallPush" resultMap="SchedulerCallPushResult">
        <include refid="selectSchedulerCallPushVo"/>
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="callid != null  and callid != ''"> and callid = #{callid}</if>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="groupName != null  and groupName != ''"> and group_name = #{groupName}</if>
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="taskName != null  and taskName != ''"> and task_name = #{taskName}</if>
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="telephone != null  and telephone != ''"> and telephone = #{telephone}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="statusStr != null  and statusStr != ''"> and status_str = #{statusStr}</if>
            <if test="params.beginCalldate != null and params.beginCalldate != '' and params.endCalldate != null and params.endCalldate != ''"> and calldate between #{params.beginCalldate} and #{params.endCalldate}</if>
            <if test="params.beginAnswerdate != null and params.beginAnswerdate != '' and params.endAnswerdate != null and params.endAnswerdate != ''"> and answerdate between #{params.beginAnswerdate} and #{params.endAnswerdate}</if>
            <if test="params.beginHangupdate != null and params.beginHangupdate != '' and params.endHangupdate != null and params.endHangupdate != ''"> and hangupdate between #{params.beginHangupdate} and #{params.endHangupdate}</if>
            <if test="duration != null "> and duration = #{duration}</if>
            <if test="bill != null "> and bill = #{bill}</if>
            <if test="intentionResults != null "> and intention_results = #{intentionResults}</if>
            <if test="rounds != null "> and rounds = #{rounds}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="levelId != null  and levelId != ''"> and level_id = #{levelId}</if>
            <if test="levelName != null  and levelName != ''"> and level_name = #{levelName}</if>
            <if test="tags != null  and tags != ''"> and tags like concat('%', #{tags}, '%')</if>
            <if test="voiceUrl != null  and voiceUrl != ''"> and voice_url = #{voiceUrl}</if>
            <if test="voiceText != null  and voiceText != ''"> and voice_text = #{voiceText}</if>
            <if test="extra != null  and extra != ''"> and extra = #{extra}</if>
        </where>
    </select>
    
    <select id="selectSchedulerCallPushById" parameterType="Long" resultMap="SchedulerCallPushResult">
        <include refid="selectSchedulerCallPushVo"/>
        where id = #{id}
    </select>

    <insert id="insertSchedulerCallPush" parameterType="SchedulerCallPush" useGeneratedKeys="true" keyProperty="id">
        insert into scheduler_call_push
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="callid != null and callid != ''">callid,</if>
            <if test="groupId != null">group_id,</if>
            <if test="groupName != null and groupName != ''">group_name,</if>
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="customerId != null and customerId != ''">customer_id,</if>
            <if test="telephone != null and telephone != ''">telephone,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="operator != null and operator != ''">operator,</if>
            <if test="status != null">status,</if>
            <if test="statusStr != null and statusStr != ''">status_str,</if>
            <if test="calldate != null">calldate,</if>
            <if test="answerdate != null">answerdate,</if>
            <if test="hangupdate != null">hangupdate,</if>
            <if test="duration != null">duration,</if>
            <if test="bill != null">bill,</if>
            <if test="intentionResults != null">intention_results,</if>
            <if test="rounds != null">rounds,</if>
            <if test="score != null">score,</if>
            <if test="levelId != null and levelId != ''">level_id,</if>
            <if test="levelName != null and levelName != ''">level_name,</if>
            <if test="tags != null and tags != ''">tags,</if>
            <if test="voiceUrl != null and voiceUrl != ''">voice_url,</if>
            <if test="voiceText != null and voiceText != ''">voice_text,</if>
            <if test="extra != null and extra != ''">extra,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="callid != null and callid != ''">#{callid},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="groupName != null and groupName != ''">#{groupName},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="customerId != null and customerId != ''">#{customerId},</if>
            <if test="telephone != null and telephone != ''">#{telephone},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="operator != null and operator != ''">#{operator},</if>
            <if test="status != null">#{status},</if>
            <if test="statusStr != null and statusStr != ''">#{statusStr},</if>
            <if test="calldate != null">#{calldate},</if>
            <if test="answerdate != null">#{answerdate},</if>
            <if test="hangupdate != null">#{hangupdate},</if>
            <if test="duration != null">#{duration},</if>
            <if test="bill != null">#{bill},</if>
            <if test="intentionResults != null">#{intentionResults},</if>
            <if test="rounds != null">#{rounds},</if>
            <if test="score != null">#{score},</if>
            <if test="levelId != null and levelId != ''">#{levelId},</if>
            <if test="levelName != null and levelName != ''">#{levelName},</if>
            <if test="tags != null and tags != ''">#{tags},</if>
            <if test="voiceUrl != null and voiceUrl != ''">#{voiceUrl},</if>
            <if test="voiceText != null and voiceText != ''">#{voiceText},</if>
            <if test="extra != null and extra != ''">#{extra},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSchedulerCallPush" parameterType="SchedulerCallPush">
        update scheduler_call_push
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="callid != null and callid != ''">callid = #{callid},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="customerId != null and customerId != ''">customer_id = #{customerId},</if>
            <if test="telephone != null and telephone != ''">telephone = #{telephone},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="operator != null and operator != ''">operator = #{operator},</if>
            <if test="status != null">status = #{status},</if>
            <if test="statusStr != null and statusStr != ''">status_str = #{statusStr},</if>
            <if test="calldate != null">calldate = #{calldate},</if>
            <if test="answerdate != null">answerdate = #{answerdate},</if>
            <if test="hangupdate != null">hangupdate = #{hangupdate},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="bill != null">bill = #{bill},</if>
            <if test="intentionResults != null">intention_results = #{intentionResults},</if>
            <if test="rounds != null">rounds = #{rounds},</if>
            <if test="score != null">score = #{score},</if>
            <if test="levelId != null and levelId != ''">level_id = #{levelId},</if>
            <if test="levelName != null and levelName != ''">level_name = #{levelName},</if>
            <if test="tags != null and tags != ''">tags = #{tags},</if>
            <if test="voiceUrl != null and voiceUrl != ''">voice_url = #{voiceUrl},</if>
            <if test="voiceText != null and voiceText != ''">voice_text = #{voiceText},</if>
            <if test="extra != null and extra != ''">extra = #{extra},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSchedulerCallPushById" parameterType="Long">
        delete from scheduler_call_push where id = #{id}
    </delete>

    <delete id="deleteSchedulerCallPushByIds" parameterType="String">
        delete from scheduler_call_push where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>