<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.scheduler.mapper.CustomerInfoMapper">
    
    <resultMap type="CustomerInfo" id="CustomerInfoResult">
        <id property="id" column="id"/>
        <result property="customerName" column="customer_name"/>
        <result property="socialCreditCode" column="social_credit_code"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="transportRoutes" column="transport_routes"/>
        <result property="dispatcher" column="dispatcher"/>
        <result property="cooperationStatus" column="cooperation_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="selectCustomerInfoVo">
        select id, customer_name, social_credit_code, contact_person, contact_phone, transport_routes, dispatcher, cooperation_status, create_by, create_time, update_by, update_time, remark, deleted from customer_info
    </sql>

    <select id="selectCustomerInfoList" parameterType="CustomerInfo" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        <where>
            deleted = 0
            <if test="customerName != null and customerName != ''">
                AND customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="socialCreditCode != null and socialCreditCode != ''">
                AND social_credit_code = #{socialCreditCode}
            </if>
            <if test="contactPerson != null and contactPerson != ''">
                AND contact_person like concat('%', #{contactPerson}, '%')
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                AND contact_phone = #{contactPhone}
            </if>
            <if test="dispatcher != null and dispatcher != ''">
                AND dispatcher = #{dispatcher}
            </if>
            <if test="cooperationStatus != null and cooperationStatus != ''">
                AND cooperation_status = #{cooperationStatus}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <select id="selectCustomerInfoById" parameterType="Long" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        where id = #{id} and deleted = 0
    </select>
        
    <select id="selectCustomerInfoBySocialCreditCode" parameterType="String" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        where social_credit_code = #{socialCreditCode} and deleted = 0
    </select>
    
    <insert id="insertCustomerInfo" parameterType="CustomerInfo" useGeneratedKeys="true" keyProperty="id">
        insert into customer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="socialCreditCode != null">social_credit_code,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="transportRoutes != null">transport_routes,</if>
            <if test="dispatcher != null">dispatcher,</if>
            <if test="cooperationStatus != null">cooperation_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            deleted,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="socialCreditCode != null">#{socialCreditCode},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="transportRoutes != null">#{transportRoutes},</if>
            <if test="dispatcher != null">#{dispatcher},</if>
            <if test="cooperationStatus != null">#{cooperationStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            0,
        </trim>
    </insert>

    <update id="updateCustomerInfo" parameterType="CustomerInfo">
        update customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="socialCreditCode != null">social_credit_code = #{socialCreditCode},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="transportRoutes != null">transport_routes = #{transportRoutes},</if>
            <if test="dispatcher != null">dispatcher = #{dispatcher},</if>
            <if test="cooperationStatus != null">cooperation_status = #{cooperationStatus},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id} and deleted = 0
    </update>

    <update id="deleteCustomerInfoById" parameterType="Long">
        update customer_info set deleted = 1 where id = #{id}
    </update>

    <update id="deleteCustomerInfoByIds" parameterType="String">
        update customer_info set deleted = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询非指定状态的货主信息列表 -->
    <select id="selectCustomerInfoListExcludeStatus" parameterType="CustomerInfo" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        <where>  
            deleted = 0
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="socialCreditCode != null  and socialCreditCode != ''"> and social_credit_code = #{socialCreditCode}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="transportRoutes != null  and transportRoutes != ''"> and transport_routes like concat('%', #{transportRoutes}, '%')</if>
            <if test="dispatcher != null  and dispatcher != ''"> and dispatcher = #{dispatcher}</if>
            <if test="cooperationStatus != null  and cooperationStatus != ''"> and cooperation_status != #{cooperationStatus}</if>
        </where>
    </select>

    <!-- 根据合作状态列表查询货主信息 -->
    <select id="selectCustomerInfoByStatusList" parameterType="java.util.List" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        <where>
            deleted = 0
            <if test="statusList != null and statusList.size() > 0">
                AND cooperation_status IN
                <foreach collection="list" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper> 