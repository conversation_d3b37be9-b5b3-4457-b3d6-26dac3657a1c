<?xml version="1.0" encoding="UTF-8"?>
<!-- scan 配置文件如果发生改变，将会被重新加载 scanPeriod 检测间隔时间 -->
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <!-- ↓↓↓↓↓↓ 只需定义app.name 、 app.package 和 app.level，后面的内容都是固定的 ↓↓↓↓↓↓ -->
    <property name="app.name" value="zly-ai-scheduler-test"/>
    <property name="app.package" value="com.zly"/>
    <property name="app.level" value="DEBUG"/>
    <!-- ↑↑↑↑↑↑ 只需定义app.name 、 app.package 和 app.level，后面的内容都是固定的 ↑↑↑↑↑↑ -->

    <contextName>${app.name}-log</contextName>
    <!-- 日志文件存储路径 不要在 logback 的配置中使用相对路径 -->
    <property name="log.path" value="/home/<USER>/logs/${app.name}"/>
    <!-- 日志输出格式 %d表示日期，%thread表示线程名，%-5level表示级别，从左显示5个字符宽度，%msg表示日志消息，%n表示换行符-->
    <property name="log.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{LOG_ID}] [%thread] %-5level %logger Line:%-3line - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 日志格式 -->
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--此日志appender是为开发使用，只配置最低级别，控制台输出的日志级别是大于或等于此级别的日志信息 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!-- 只有这个日志权限才能看，sql语句 -->
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${app.name}.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志命名:单个文件大于64MB 按照时间+自增i 生成log文件 -->
            <fileNamePattern>${log.path}/${app.name}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>64MB</maxFileSize>
            <!-- 最大保存时间：60天 -->
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="com.zly.common.filter.LogbackLevelFilter">
            <!-- 过滤的级别(INFO，显示INFO级别的信息，DEBUG，显示INFO和DEBUG级别的信息) -->
            <level>DEBUG</level>
            <!-- 匹配时的操作：接收（记录） -->
            <!-- <onMatch>ACCEPT</onMatch>-->
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <!-- <onMismatch>DENY</onMismatch>-->
        </filter>
    </appender>

    <!-- 错误日志 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/${app.name}-error.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志命名:单个文件大于64MB 按照时间+自增i 生成log文件 -->
            <fileNamePattern>${log.path}/${app.name}-error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>64MB</maxFileSize>
            <!-- 最大保存时间：180天 -->
            <maxHistory>180</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <!-- 日志格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 系统模块日志级别控制  -->
    <logger name="${app.package}" level="${app.level}" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </logger>

    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="WARN"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
</configuration>