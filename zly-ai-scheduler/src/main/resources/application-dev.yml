# 开发环境配置
server:
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

########## logback-{xxx}.xml 文件打包在项目内部 ##########
# 日志配置
logging:
  config: classpath:logback/logback-dev.xml

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping:
  username: zly-swagger
  password: 51f9CmbDih%B4EcVLlI!$@

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *******************************************************************************************************************************************************************
        username: cltx
        password: cltxCzzlyTms1qaz@WSX
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 500
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 6000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: false
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: zly-druid-u
        login-password: Qt&%rKFwAxo8#eE3
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 30000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

  # redis 配置
  redis:
    # 地址
    host: ************
    # 端口，默认为6379
    port: 5438
    # 数据库索引
    database: 3
    # 密码
    password: cltx456/*-
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

#以下是自定义配置
custom:
  iphone:
    url: https://sms.56zly.com/sms/send
    acc: tms
    pas: 123
  #gps接口相关配置
  gps:
    acc: tms
    pas: 123
    url: https://gps.56zly.com/gps
    lastLocation: /findNewLocationV3
    historyTrack: /vHisTrackV3
    checkExist: /verifyV2
    historyLocation: /findHistoryTrackV3
    trackList: /list
  #高德地图API配置
  amap:
    webServerKey: b4ef1b65f0ca5eb684a70952d1c56210
    distanceUrl: https://restapi.amap.com/v3/distance
    geocodeUrl: https://restapi.amap.com/v3/geocode/regeo
    geoUrl: https://restapi.amap.com/v3/geocode/geo
    truckDirectionUrl: https://restapi.amap.com/v4/direction/truck
    drivingDirectionUrl: https://restapi.amap.com/v5/direction/driving
  #七牛相关配置
  qiniu:
    accessKey: hffeLiXlirnt_lyYEVkyo1UMpBm9v7WF7zjDYpxT
    secretKey: tVWGHsoWlqOfN_5VkzGhmKfFd3Bt5FU49bA9AiHc
    domain: https://wlhyfile.56zly.com/
    bucket: wlhy
    #存储区域 华东:0 华北:1 华南:2
    region: 0
  #蜂鸟迅达接口配置
  beeBird:
    apiUrl: http://47.111.5.195:39000/
    getField: api/api/getField
    taskList: api/api/taskList
    taskStart: api/api/taskStart
    taskStop: api/api/taskStop
    taskNumber: api/api/taskNumber
    taskNumberNew: api/api/taskNumberNew
    numberReset: api/api/numberReset
    deleteNumber: api/api/deleteNumber
    getCallOrder: api/api/getCallOrder
  #阿里云百炼配置
  bailian:
    #API-Key
    apiKey: sk-89675ad40f0a4d29992950785daf1a17
    #智能体应用ID
    appId: ace7dc35d9cd43a7aaaa5cbd3b1b56b3
    #数据库默认目录
    agentKey: default
    #空间ID
    modelId: llm-54zye8uci10w2pl3
    #知识库ID
    indexId: r5oeo2f20p
    #通义千问模型名称
    model: qwen-turbo
    accessKeyId: LTAI5tJcLTSTUvQsHhVHNJ7C
    accessKeySecret: ******************************
    endpoint: bailian.cn-beijing.aliyuncs.com
  #查询4.0系统司机画像数据的URL
  scheduler:
    # 远程服务基础URL
    baseUrl: http://localhost:8081
    auth:
      # 客户端ID
      clientId: test_client_001
      # 客户端密钥
      clientSecret: test_secret_001
      # 客户端类型：2=网络货运人端，3=托运人端，7=代运营端，8=金融端
      type: 2
      # Token过期时间（秒），默认1小时
      tokenExpireTime: 600
    driverProfile:
      apiUrl: http://localhost:8081/carrier/driver/portrait/driverPortraits
  areas:
    list:
      apiUrl: http://localhost:8081/common/areas/listOut
      resolveAddressIpLimit: http://localhost:8081/common/resolveAddressIpLimit
  #询价功能
  inquiry:
    #询价API地址
    apiUrl: http://*************/api/v1/run/920d476ac5ec43cda16aaff1c94ae384
    #询价API-Key
    apiKey: sk-6_l9rAC8mxGwVsMSIVZSF9-K6zRKpSM_9fqHAGSk05g
