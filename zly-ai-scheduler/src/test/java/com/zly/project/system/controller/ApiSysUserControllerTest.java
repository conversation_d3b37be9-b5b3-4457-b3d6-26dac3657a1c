package com.zly.project.system.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.service.IApiSysUserSyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 用户同步控制器测试类
 * 
 * <AUTHOR>
 */
@WebMvcTest(ApiSysUserController.class)
public class ApiSysUserControllerTest
{
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IApiSysUserSyncService userSyncService;

    @MockBean
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private SysUser testUser;
    private List<SysUser> testUsers;

    @BeforeEach
    void setUp()
    {
        testUser = new SysUser();
        testUser.setUserName("test_user");
        testUser.setNickName("测试用户");
        testUser.setEmail("<EMAIL>");
        testUser.setPhonenumber("13800138000");

        testUsers = new ArrayList<>();
        testUsers.add(testUser);
    }

    @Test
    void testSyncSingleUser_Success() throws Exception
    {
        // Mock 同步服务返回成功
        AjaxResult syncResult = AjaxResult.success("用户新增成功并分配角色: test_user");
        when(userSyncService.syncSingleUser(any(SysUser.class))).thenReturn(syncResult);

        mockMvc.perform(post("/api/user/syncSingleUser")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("userName", "test_user")
                .param("nickName", "测试用户")
                .param("email", "<EMAIL>"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("用户新增成功并分配角色: test_user"));
    }

    @Test
    void testGetCommonRoleId_Success() throws Exception
    {
        // Mock 服务返回角色ID
        when(userSyncService.getCommonRoleId()).thenReturn(2L);

        mockMvc.perform(post("/api/user/getCommonRoleId")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(2));
    }

    @Test
    void testSyncSingleUser_Error() throws Exception
    {
        // Mock 同步服务返回失败
        AjaxResult syncResult = AjaxResult.error("用户信息不完整");
        when(userSyncService.syncSingleUser(any(SysUser.class))).thenReturn(syncResult);

        mockMvc.perform(post("/api/user/syncSingleUser")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("nickName", "测试用户"))  // 缺少用户名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("用户信息不完整"));
    }
}
