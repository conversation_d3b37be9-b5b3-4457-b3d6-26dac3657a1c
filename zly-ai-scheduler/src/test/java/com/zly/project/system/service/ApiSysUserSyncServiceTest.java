package com.zly.project.system.service;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.service.impl.ApiSysUserSyncServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户同步服务测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@SpringJUnitConfig
@Transactional
public class ApiSysUserSyncServiceTest
{
    @Resource
    private IApiSysUserSyncService userSyncService;

    private SysUser testUser;
    private List<SysUser> testUsers;

    @BeforeEach
    void setUp()
    {
        // 创建测试用户数据
        testUser = new SysUser();
        testUser.setUserName("test_shandao_user");
        testUser.setNickName("测试善道用户");
        testUser.setEmail("<EMAIL>");
        testUser.setPhonenumber("13800138000");
        testUser.setSex("0");
        testUser.setStatus("0");
        testUser.setPwd("123456");
        testUser.setFreightForwarderId(1L);
        testUser.setDefaultProject(1L);

        testUsers = new ArrayList<>();
        testUsers.add(testUser);

        // 创建第二个测试用户
        SysUser testUser2 = new SysUser();
        testUser2.setUserName("test_shandao_user2");
        testUser2.setNickName("测试善道用户2");
        testUser2.setEmail("<EMAIL>");
        testUser2.setPhonenumber("13800138001");
        testUser2.setSex("1");
        testUser2.setStatus("0");
        testUser2.setPwd("123456");
        testUser2.setFreightForwarderId(1L);
        testUser2.setDefaultProject(0L);
        testUsers.add(testUser2);
    }

    @Test
    void testSyncSingleUser_NewUser()
    {
        // 测试新增用户
        AjaxResult result = userSyncService.syncSingleUser(testUser);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.get("msg").toString().contains("新增成功"));
    }

    @Test
    void testSyncSingleUser_ExistingUser()
    {
        // 先新增用户
        userSyncService.syncSingleUser(testUser);
        
        // 修改用户信息
        testUser.setNickName("修改后的善道用户");
        testUser.setEmail("<EMAIL>");
        
        // 测试更新用户
        AjaxResult result = userSyncService.syncSingleUser(testUser);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.get("msg").toString().contains("更新成功"));
    }

    @Test
    void testSyncShandaoUsers()
    {
        // 测试批量同步用户
        AjaxResult result = userSyncService.syncShandaoUsers(testUsers);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.get("msg").toString().contains("成功: 2"));
    }

    @Test
    void testSyncShandaoUsers_EmptyList()
    {
        // 测试空列表
        AjaxResult result = userSyncService.syncShandaoUsers(new ArrayList<>());
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("远程用户数据为空", result.get("msg"));
    }

    @Test
    void testSyncShandaoUsers_NullList()
    {
        // 测试null列表
        AjaxResult result = userSyncService.syncShandaoUsers(null);
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("远程用户数据为空", result.get("msg"));
    }

    @Test
    void testCheckUserExists()
    {
        // 测试用户不存在
        SysUser user = userSyncService.checkUserExists("non_existing_user");
        assertNull(user);
        
        // 先新增用户
        userSyncService.syncSingleUser(testUser);
        
        // 测试用户存在
        user = userSyncService.checkUserExists(testUser.getUserName());
        assertNotNull(user);
        assertEquals(testUser.getUserName(), user.getUserName());
    }

    @Test
    void testAssignDefaultRole()
    {
        // 先新增用户
        userSyncService.syncSingleUser(testUser);
        
        // 获取用户ID
        SysUser existingUser = userSyncService.checkUserExists(testUser.getUserName());
        assertNotNull(existingUser);
        
        // 测试分配默认角色
        boolean result = userSyncService.assignDefaultRole(existingUser);
        assertTrue(result);
    }

    @Test
    void testGetCommonRoleId()
    {
        // 测试获取普通角色ID
        Long roleId = userSyncService.getCommonRoleId();
        assertNotNull(roleId);
        assertEquals(2L, roleId);
    }

    @Test
    void testSyncSingleUser_InvalidData()
    {
        // 测试无效数据 - 空用户名
        SysUser invalidUser = new SysUser();
        invalidUser.setNickName("无效用户");
        
        AjaxResult result = userSyncService.syncSingleUser(invalidUser);
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("用户信息不完整", result.get("msg"));
    }

    @Test
    void testSyncSingleUser_NullUser()
    {
        // 测试null用户
        AjaxResult result = userSyncService.syncSingleUser(null);
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("用户信息不完整", result.get("msg"));
    }
}
