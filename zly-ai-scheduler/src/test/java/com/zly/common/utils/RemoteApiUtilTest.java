package com.zly.common.utils;

import cn.hutool.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RemoteApiUtil 测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@SpringJUnitConfig
public class RemoteApiUtilTest
{
    @Resource
    private RemoteApiUtil remoteApiUtil;

    @Test
    void testGetAccessToken()
    {
        try
        {
            // 测试获取访问令牌
            String token = remoteApiUtil.getAccessToken();
            assertNotNull(token);
            assertFalse(token.isEmpty());
            System.out.println("获取到的token: " + token);

            // 验证token格式（应该是JWT格式）
            assertTrue(token.contains("."), "Token应该是JWT格式，包含点分隔符");
        }
        catch (Exception e)
        {
            // 如果配置不完整或服务不可用，这是正常的
            System.out.println("获取token失败（可能是配置问题）: " + e.getMessage());
        }
    }

    @Test
    void testRefreshAccessToken()
    {
        try
        {
            // 测试刷新访问令牌
            String token = remoteApiUtil.refreshAccessToken();
            assertNotNull(token);
            assertFalse(token.isEmpty());
            System.out.println("刷新获取到的token: " + token);
        }
        catch (Exception e)
        {
            // 如果配置不完整或服务不可用，这是正常的
            System.out.println("刷新token失败（可能是配置问题）: " + e.getMessage());
        }
    }

    @Test
    void testClearCachedToken()
    {
        try
        {
            // 测试清除缓存的token
            remoteApiUtil.clearCachedToken();
            System.out.println("清除缓存token成功");
        }
        catch (Exception e)
        {
            System.out.println("清除缓存token失败: " + e.getMessage());
        }
    }

    @Test
    void testPostWithSignWithAuth()
    {
        try
        {
            // 测试带认证的请求
            JSONObject requestBody = new JSONObject();
            requestBody.set("test", "value");
            
            // 这里使用一个测试URL，实际使用时需要替换为真实的API地址
            String testUrl = "http://localhost:8081/api/test";
            
            JSONObject response = remoteApiUtil.postWithSign(testUrl, requestBody, true,"");
            assertNotNull(response);
            System.out.println("带认证的请求响应: " + response);
        }
        catch (Exception e)
        {
            // 如果服务不可用，这是正常的
            System.out.println("带认证的请求失败（可能是服务不可用）: " + e.getMessage());
        }
    }

    @Test
    void testPostWithSignWithoutAuth()
    {
        try
        {
            // 测试不带认证的请求
            JSONObject requestBody = new JSONObject();
            requestBody.set("test", "value");
            
            // 这里使用一个测试URL，实际使用时需要替换为真实的API地址
            String testUrl = "http://localhost:8081/api/test";
            
            JSONObject response = remoteApiUtil.postWithSign(testUrl, requestBody, false,"");
            assertNotNull(response);
            System.out.println("不带认证的请求响应: " + response);
        }
        catch (Exception e)
        {
            // 如果服务不可用，这是正常的
            System.out.println("不带认证的请求失败（可能是服务不可用）: " + e.getMessage());
        }
    }

    @Test
    void testCalculateSignature()
    {
        // 测试签名计算
        String clientId = "test_client_001";
        Long userId = 123L;
        Integer type = 2;
        Long timestamp = 1640995200000L; // 固定时间戳用于测试
        String clientSecret = "test_secret_001";

        String signature = RemoteApiUtil.calculateSignature(clientId, userId, type, timestamp, clientSecret);
        assertNotNull(signature);
        assertEquals(32, signature.length()); // MD5签名长度应该是32位
        System.out.println("计算的签名: " + signature);

        // 验证相同参数生成相同签名
        String signature2 = RemoteApiUtil.calculateSignature(clientId, userId, type, timestamp, clientSecret);
        assertEquals(signature, signature2, "相同参数应该生成相同的签名");
    }
}
